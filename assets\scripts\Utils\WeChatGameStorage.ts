import { _decorator } from 'cc';

/**
 * 微信小游戏存储兼容性工具类
 * 处理localStorage在微信小游戏环境中的兼容性问题
 */
export class WeChatGameStorage {
    
    /**
     * 安全地获取localStorage中的字符串值
     * @param key 存储键
     * @param defaultValue 默认值
     * @returns 存储的值或默认值
     */
    public static getString(key: string, defaultValue: string = ""): string {
        try {
            const value = localStorage.getItem(key);
            if (value !== null && value !== undefined) {
                return value;
            }
            return defaultValue;
        } catch (error) {
            console.error(`获取localStorage字符串失败，key: ${key}`, error);
            return defaultValue;
        }
    }

    /**
     * 安全地获取localStorage中的数字值
     * @param key 存储键
     * @param defaultValue 默认值
     * @returns 存储的数字值或默认值
     */
    public static getNumber(key: string, defaultValue: number = 0): number {
        try {
            const value = localStorage.getItem(key);
            if (value !== null && value !== undefined && value !== "") {
                const parsedValue = parseInt(value);
                if (!isNaN(parsedValue) && isFinite(parsedValue)) {
                    return parsedValue;
                } else {
                    console.warn(`localStorage数字解析失败，key: ${key}, value: "${value}"，使用默认值: ${defaultValue}`);
                    return defaultValue;
                }
            }
            return defaultValue;
        } catch (error) {
            console.error(`获取localStorage数字失败，key: ${key}`, error);
            return defaultValue;
        }
    }

    /**
     * 安全地获取localStorage中的浮点数值
     * @param key 存储键
     * @param defaultValue 默认值
     * @returns 存储的浮点数值或默认值
     */
    public static getFloat(key: string, defaultValue: number = 0): number {
        try {
            const value = localStorage.getItem(key);
            if (value !== null && value !== undefined && value !== "") {
                const parsedValue = parseFloat(value);
                if (!isNaN(parsedValue) && isFinite(parsedValue)) {
                    return parsedValue;
                } else {
                    console.warn(`localStorage浮点数解析失败，key: ${key}, value: "${value}"，使用默认值: ${defaultValue}`);
                    return defaultValue;
                }
            }
            return defaultValue;
        } catch (error) {
            console.error(`获取localStorage浮点数失败，key: ${key}`, error);
            return defaultValue;
        }
    }

    /**
     * 安全地设置localStorage中的值
     * @param key 存储键
     * @param value 要存储的值
     * @returns 是否成功存储
     */
    public static setItem(key: string, value: string | number): boolean {
        try {
            const stringValue = value.toString();
            localStorage.setItem(key, stringValue);
            console.log(`localStorage设置成功，key: ${key}, value: ${stringValue}`);
            return true;
        } catch (error) {
            console.error(`设置localStorage失败，key: ${key}, value: ${value}`, error);
            return false;
        }
    }

    /**
     * 安全地移除localStorage中的值
     * @param key 存储键
     * @returns 是否成功移除
     */
    public static removeItem(key: string): boolean {
        try {
            localStorage.removeItem(key);
            console.log(`localStorage移除成功，key: ${key}`);
            return true;
        } catch (error) {
            console.error(`移除localStorage失败，key: ${key}`, error);
            return false;
        }
    }

    /**
     * 检查localStorage是否可用
     * @returns localStorage是否可用
     */
    public static isAvailable(): boolean {
        try {
            const testKey = '__localStorage_test__';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return true;
        } catch (error) {
            console.error('localStorage不可用:', error);
            return false;
        }
    }

    /**
     * 获取所有以指定前缀开头的键值对
     * @param prefix 键前缀
     * @returns 键值对对象
     */
    public static getItemsWithPrefix(prefix: string): { [key: string]: string } {
        const result: { [key: string]: string } = {};
        try {
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(prefix)) {
                    const value = localStorage.getItem(key);
                    if (value !== null) {
                        result[key] = value;
                    }
                }
            }
        } catch (error) {
            console.error(`获取前缀为 ${prefix} 的localStorage项失败:`, error);
        }
        return result;
    }

    /**
     * 清除所有以指定前缀开头的localStorage项
     * @param prefix 键前缀
     * @returns 清除的项数
     */
    public static clearItemsWithPrefix(prefix: string): number {
        let count = 0;
        try {
            const keysToRemove: string[] = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(prefix)) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                count++;
            });
            
            console.log(`清除了 ${count} 个前缀为 ${prefix} 的localStorage项`);
        } catch (error) {
            console.error(`清除前缀为 ${prefix} 的localStorage项失败:`, error);
        }
        return count;
    }

    /**
     * 获取localStorage的使用情况统计
     * @returns 使用情况统计
     */
    public static getStorageStats(): { totalItems: number; totalSize: number; availableSpace: number } {
        let totalItems = 0;
        let totalSize = 0;
        let availableSpace = 0;

        try {
            totalItems = localStorage.length;
            
            // 计算总大小（近似值）
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key) {
                    const value = localStorage.getItem(key);
                    if (value) {
                        totalSize += key.length + value.length;
                    }
                }
            }

            // 尝试估算可用空间（这是一个粗略的估算）
            try {
                const testData = 'x'.repeat(1024); // 1KB的测试数据
                let testSize = 0;
                while (testSize < 10240) { // 最多测试10MB
                    const testKey = `__size_test_${testSize}__`;
                    localStorage.setItem(testKey, testData);
                    localStorage.removeItem(testKey);
                    testSize += 1024;
                }
                availableSpace = testSize;
            } catch (e) {
                // 如果测试失败，说明空间不足
                availableSpace = 0;
            }
        } catch (error) {
            console.error('获取localStorage统计信息失败:', error);
        }

        return {
            totalItems,
            totalSize,
            availableSpace
        };
    }
}
