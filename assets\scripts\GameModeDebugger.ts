import { _decorator, Component, Node } from 'cc';
import { GameData, GameMode } from './GameData';
import { GameDifficulty } from './GameDifficulty';
import { ChallengeMode } from './ChallengeMode';

const { ccclass, property } = _decorator;

/**
 * 游戏模式调试器
 * 用于实时监控游戏模式设置和分数保存过程
 */
@ccclass('GameModeDebugger')
export class GameModeDebugger extends Component {

    onLoad() {
        console.log("=== GameModeDebugger 已启动 ===");
        this.startDebugging();
    }

    private startDebugging() {
        // 监控GameData的关键方法
        this.interceptGameDataMethods();
        
        // 定期输出当前状态
        this.schedule(this.logCurrentState, 2.0);
    }

    /**
     * 拦截GameData的关键方法进行调试
     */
    private interceptGameDataMethods() {
        // 保存原始方法
        const originalSaveScore = GameData.saveScore;
        const originalSetCurrentGameMode = GameData.setCurrentGameMode;
        const originalDetermineGameMode = GameData.determineGameMode;

        // 拦截saveScore方法
        GameData.saveScore = function(mode?: GameMode) {
            const actualMode = mode !== undefined ? mode : GameData.getCurrentGameMode();
            console.log(`🔍 [DEBUG] saveScore 被调用:`);
            console.log(`  - 传入模式: ${mode !== undefined ? mode : 'undefined'}`);
            console.log(`  - 实际使用模式: ${actualMode} (${GameData.getGameModeName(actualMode)})`);
            console.log(`  - 当前分数: ${GameData.getScore()}`);
            console.log(`  - 当前GameData._currentGameMode: ${GameData.getCurrentGameMode()}`);
            
            // 调用原始方法
            const result = originalSaveScore.call(this, mode);
            
            // 验证保存结果
            const savedBestScore = GameData.getBestScore(actualMode);
            const savedTopScores = GameData.getTopScores(actualMode);
            console.log(`  - 保存后最高分: ${savedBestScore}`);
            console.log(`  - 保存后前三分: [${savedTopScores.join(', ')}]`);
            
            return result;
        };

        // 拦截setCurrentGameMode方法
        GameData.setCurrentGameMode = function(mode: GameMode) {
            console.log(`🔍 [DEBUG] setCurrentGameMode 被调用:`);
            console.log(`  - 设置模式: ${mode} (${GameData.getGameModeName(mode)})`);
            console.log(`  - 调用栈:`, new Error().stack?.split('\n').slice(1, 4));
            
            return originalSetCurrentGameMode.call(this, mode);
        };

        // 拦截determineGameMode方法
        GameData.determineGameMode = function(difficulty: number, challengeMode: number): GameMode {
            const result = originalDetermineGameMode.call(this, difficulty, challengeMode);
            console.log(`🔍 [DEBUG] determineGameMode 被调用:`);
            console.log(`  - 难度: ${difficulty}`);
            console.log(`  - 挑战模式: ${challengeMode}`);
            console.log(`  - 计算结果: ${result} (${GameData.getGameModeName(result)})`);
            
            return result;
        };
    }

    /**
     * 定期输出当前状态
     */
    private logCurrentState() {
        console.log(`\n📊 [状态监控] ${new Date().toLocaleTimeString()}`);
        console.log(`当前难度: ${GameDifficulty.getDifficulty()}`);
        console.log(`当前挑战模式: ${ChallengeMode.getMode()}`);
        console.log(`当前GameData模式: ${GameData.getCurrentGameMode()} (${GameData.getGameModeName(GameData.getCurrentGameMode())})`);
        
        // 输出所有模式的记录
        console.log(`\n各模式记录:`);
        for (let mode = 0; mode <= 5; mode++) {
            const gameMode = mode as GameMode;
            const bestScore = GameData.getBestScore(gameMode);
            const topScores = GameData.getTopScores(gameMode);
            const modeName = GameData.getGameModeName(gameMode);
            console.log(`  ${modeName}: 最高分=${bestScore}, 前三分=[${topScores.join(', ')}]`);
        }
        
        // 检查localStorage中的实际存储
        console.log(`\nlocalStorage 实际存储:`);
        for (let mode = 0; mode <= 5; mode++) {
            const bestScoreKey = `BestScore_Mode_${mode}`;
            const topScoresKey = `TopScores_Mode_${mode}`;
            const bestScore = localStorage.getItem(bestScoreKey) || '0';
            const topScores = localStorage.getItem(topScoresKey) || '[0,0,0]';
            const modeName = GameData.getGameModeName(mode as GameMode);
            console.log(`  ${modeName}: BestScore=${bestScore}, TopScores=${topScores}`);
        }
    }

    /**
     * 手动测试分数保存
     */
    public testScoreSaving() {
        console.log("\n🧪 [手动测试] 开始测试分数保存");
        
        // 清除所有记录
        GameData.clearAllGameRecords();
        console.log("已清除所有记录");
        
        // 测试轻松模式
        console.log("\n--- 测试轻松模式 ---");
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_EASY);
        ChallengeMode.clearMode();
        
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        const gameMode = GameData.determineGameMode(difficulty, challengeMode);
        
        console.log(`设置: 难度=${difficulty}, 挑战=${challengeMode}, 计算模式=${gameMode}`);
        
        GameData.setCurrentGameMode(gameMode);
        GameData.resetScore();
        GameData.addScore(15);
        
        console.log(`准备保存分数: ${GameData.getScore()}`);
        GameData.saveScore(gameMode);
        
        // 验证结果
        const easyBest = GameData.getBestScore(GameMode.NORMAL_EASY);
        const standardBest = GameData.getBestScore(GameMode.NORMAL_STANDARD);
        
        console.log(`轻松模式最高分: ${easyBest} (期望: 15)`);
        console.log(`标准模式最高分: ${standardBest} (期望: 0)`);
        
        if (easyBest === 15 && standardBest === 0) {
            console.log("✅ 轻松模式测试通过");
        } else {
            console.log("❌ 轻松模式测试失败");
        }
    }

    /**
     * 检查localStorage存储键冲突
     */
    public checkStorageKeys() {
        console.log("\n🔍 [存储检查] 检查localStorage存储键");
        
        // 列出所有相关的存储键
        const keys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.includes('BestScore') || key.includes('TopScores'))) {
                keys.push(key);
            }
        }
        
        console.log("找到的存储键:", keys);
        
        // 检查是否有重复或冲突的键
        const bestScoreKeys = keys.filter(k => k.includes('BestScore'));
        const topScoresKeys = keys.filter(k => k.includes('TopScores'));
        
        console.log("最高分键:", bestScoreKeys);
        console.log("前三分键:", topScoresKeys);
        
        // 检查每个模式的存储
        for (let mode = 0; mode <= 5; mode++) {
            const expectedBestKey = `BestScore_Mode_${mode}`;
            const expectedTopKey = `TopScores_Mode_${mode}`;
            
            const hasBestKey = bestScoreKeys.includes(expectedBestKey);
            const hasTopKey = topScoresKeys.includes(expectedTopKey);
            
            console.log(`模式${mode}: BestScore键存在=${hasBestKey}, TopScores键存在=${hasTopKey}`);
        }
    }

    onDestroy() {
        console.log("=== GameModeDebugger 已停止 ===");
        this.unscheduleAllCallbacks();
    }
}
