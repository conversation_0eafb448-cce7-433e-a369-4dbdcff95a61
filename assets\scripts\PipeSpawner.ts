import { _decorator, Component, instantiate, math, Node, Prefab } from 'cc';
import { Pipe } from './Pipe';
import { MovingPipe } from './MovingPipe';
import { GameDifficulty } from './GameDifficulty';
import { GameManager } from './GameManager';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
import { WindChallengeManager } from './WindChallengeManager';
const { ccclass, property } = _decorator;

@ccclass('PipeSpawner')
export class PipeSpawner extends Component {

    @property(Prefab)
    pipePrefab:Prefab = null;

    @property
    spawnRate:number = 3; // 基础生成间隔（轻松难度）

    // 不同难度的生成间隔倍率
    private readonly SPAWN_RATE_MULTIPLIER_EASY: number = 1.0;
    private readonly SPAWN_RATE_MULTIPLIER_NORMAL: number = 0.833; // 1/1.2
    private readonly SPAWN_RATE_MULTIPLIER_HARD: number = 0.667;   // 1/1.5

    private timer:number = 2;
    private _isSpawning:boolean = false;
    private _currentSpawnRate:number = 3; // 当前实际使用的生成间隔
    private _isWindMode:boolean = false; // 是否为大风吹模式
    private _isDistanceMode:boolean = false; // 是否为距离控制模式

    // 距离控制参数
    private readonly TOTAL_PIPE_DISTANCE: number = 600;    // 相邻管道的总距离
    private _totalDistanceTraveled: number = 0;  // 总移动距离
    private _nextPipeDistance: number = 300;     // 下次生成管道的距离（第一个管道在300距离处）

    // 背景管理器设置的标记
    public _needsTimerAdjustment: boolean = false; // 是否需要调整timer
    public _backgroundSet: number = 1; // 背景套装编号

    update(deltaTime: number) {
        // 检查游戏是否暂停或结束，暂停时不生成管道
        const gameManager = GameManager.inst();
        if (gameManager && gameManager.isPaused()) {
            return; // 暂停或游戏结束时不生成管道
        }

        // 大风吹模式下不使用定时器生成
        if(this._isWindMode) return;

        // 距离控制模式下处理距离更新
        if (this._isDistanceMode) {
            this.updateDistanceMode(deltaTime);
            return;
        }

        if(this._isSpawning==false)return;
        this.timer += deltaTime;
        if(this.timer > this._currentSpawnRate){
            this.timer = 0;
            this.spawnPipe();
        }
    }

    /**
     * 生成管道的通用方法
     */
    private spawnPipe(): void {
        const pipeInst = instantiate(this.pipePrefab);
        this.node.addChild(pipeInst);
        const p = this.node.getWorldPosition();
        pipeInst.setWorldPosition(p);
        const y = math.randomRangeInt(-230,330);

        const pLoca = pipeInst.getPosition();
        pipeInst.setPosition(pLoca.x,y);

        // 根据当前难度决定是否添加MovingPipe组件
        const currentDifficulty = GameDifficulty.getDifficulty();

        // 根据难度添加MovingPipe组件
        if (currentDifficulty === GameDifficulty.DIFFICULTY_NORMAL ||
            currentDifficulty === GameDifficulty.DIFFICULTY_HARD) {
            // 在标准和困难难度下，添加MovingPipe组件
            // MovingPipe组件内部会处理上下移动的逻辑和概率
            if (!pipeInst.getComponent(MovingPipe)) {
                pipeInst.addComponent(MovingPipe);
            }
        }

        // 在大风吹模式下，设置管道的动态速度
        if (this._isWindMode) {
            const pipeComp = pipeInst.getComponent(Pipe);
            if (pipeComp) {
                (pipeComp as any).setWindMode(true);
            }
        }
    }

    public pause(){
        this._isSpawning=false;
        // 重置timer到初始值，防止在准备阶段意外触发生成
        this.timer = 2;
        // 注意：不再需要禁用Pipe和MovingPipe组件，因为它们会自己检查暂停状态
    }
    public start(){
        this._isSpawning = true;

        // 根据当前难度设置生成间隔
        this.updateSpawnRateByDifficulty();

        // 如果需要调整timer（针对困难难度的第二、第三套背景）
        this.adjustTimerIfNeeded();
    }

    // 根据当前难度更新生成间隔
    private updateSpawnRateByDifficulty() {
        const currentDifficulty = GameDifficulty.getDifficulty();

        // 根据难度设置生成间隔倍率
        let spawnRateMultiplier = this.SPAWN_RATE_MULTIPLIER_EASY;

        switch(currentDifficulty) {
            case GameDifficulty.DIFFICULTY_NORMAL:
                spawnRateMultiplier = this.SPAWN_RATE_MULTIPLIER_NORMAL;
                break;
            case GameDifficulty.DIFFICULTY_HARD:
                spawnRateMultiplier = this.SPAWN_RATE_MULTIPLIER_HARD;
                break;
        }

        // 计算实际生成间隔
        this._currentSpawnRate = this.spawnRate * spawnRateMultiplier;

        console.log(`管道生成间隔设置为: ${this._currentSpawnRate.toFixed(2)}秒 (难度: ${currentDifficulty})`);
    }

    /**
     * 如果需要，调整timer（仅在游戏开始时调用，确保不会在准备阶段触发管道生成）
     */
    private adjustTimerIfNeeded(): void {
        // 只有在标记需要调整且当前正在生成时才调整
        if (this._needsTimerAdjustment && this._isSpawning) {
            const currentDifficulty = GameDifficulty.getDifficulty();

            // 只在困难难度下进行调整
            if (currentDifficulty === GameDifficulty.DIFFICULTY_HARD &&
                (this._backgroundSet === 2 || this._backgroundSet === 3)) {

                // 困难难度下的生成间隔是 3 * 0.667 = 2.001秒
                // 我们将timer提前一个间隔，即设置为接近生成间隔的值
                const hardDifficultyInterval = 3 * 0.667; // 2.001秒
                this.timer = hardDifficultyInterval - 0.1; // 提前0.1秒确保在金币之前生成

                console.log(`调整背景套装${this._backgroundSet}的困难难度管道生成时机: timer=${this.timer.toFixed(2)}`);
            }

            // 清除标记，避免重复调整
            this._needsTimerAdjustment = false;
        }
    }

    /**
     * 启动大风吹模式
     */
    public startWindMode(): void {
        this._isWindMode = true;
        this._isSpawning = true;
        // 如果需要调整timer（针对困难难度的第二、第三套背景）
        this.adjustTimerIfNeeded();
        console.log("管道生成器：启动大风吹模式（距离控制）");
    }

    /**
     * 强制生成一个管道（用于大风吹模式的距离控制）
     */
    public forceSpawnPipe(): void {
        if ((this._isWindMode || this._isDistanceMode) && this._isSpawning) {
            this.spawnPipe();
            console.log("管道生成器：强制生成管道（距离控制模式）");
        }
    }

    /**
     * 启动距离控制模式
     */
    public startDistanceMode(): void {
        this._isDistanceMode = true;
        this._isSpawning = true;
        this.resetDistanceTracking();
        // 如果需要调整timer（针对困难难度的第二、第三套背景）
        this.adjustTimerIfNeeded();
        console.log("管道生成器：启动距离控制模式");
    }

    /**
     * 恢复距离控制模式（不重置距离追踪）
     */
    public resumeDistanceMode(): void {
        this._isDistanceMode = true;
        this._isSpawning = true;
        console.log("管道生成器：恢复距离控制模式（保持距离追踪）");
    }

    /**
     * 恢复大风吹模式（不重置状态）
     */
    public resumeWindMode(): void {
        this._isWindMode = true;
        this._isSpawning = true;
        console.log("管道生成器：恢复大风吹模式（保持状态）");
    }

    /**
     * 更新距离控制模式
     */
    private updateDistanceMode(deltaTime: number): void {
        if (!this._isSpawning) return;

        // 检查游戏状态，只有在Gaming状态下才更新距离
        const gameManager = GameManager.inst();
        if (!gameManager || gameManager.isPaused()) {
            return; // 暂停时不更新距离
        }

        // 获取当前移动速度
        const currentSpeed = gameManager.getCurrentMoveSpeed();

        // 累加移动距离
        this._totalDistanceTraveled += currentSpeed * deltaTime;

        // 检查是否需要生成管道
        this.checkPipeSpawn();
    }

    /**
     * 检查是否需要生成管道
     */
    private checkPipeSpawn(): void {
        if (this._totalDistanceTraveled >= this._nextPipeDistance) {
            // 生成管道
            this.spawnPipe();

            // 更新距离记录
            this._nextPipeDistance = this._totalDistanceTraveled + this.TOTAL_PIPE_DISTANCE;

            console.log(`距离控制生成管道 - 距离: ${this._totalDistanceTraveled.toFixed(2)}, 下次管道距离: ${this._nextPipeDistance.toFixed(2)}`);
        }
    }

    /**
     * 重置距离追踪
     */
    public resetDistanceTracking(): void {
        this._totalDistanceTraveled = 0;
        this._nextPipeDistance = 300;  // 第一个管道在300距离处
        console.log("管道生成器：距离追踪已重置");
    }

    /**
     * 获取是否为距离控制模式
     */
    public isDistanceMode(): boolean {
        return this._isDistanceMode;
    }

    onDestroy() {
        // 取消所有调度器
        this.unscheduleAllCallbacks();

        // 清理所有管道节点
        if (this.node && this.node.isValid) {
            const pipes = this.node.children.slice();
            pipes.forEach(pipe => {
                if (pipe && pipe.isValid) {
                    pipe.destroy();
                }
            });
        }

        // 重置状态
        this._isSpawning = false;
        this._isWindMode = false;
        this._isDistanceMode = false;
        this._totalDistanceTraveled = 0;
        this._nextPipeDistance = 300;

        console.log("PipeSpawner: 组件已销毁，资源已清理");
    }

}


