import { _decorator, Component, Node } from 'cc';
import { GameData, BirdType } from './GameData';
import { GameManager } from './GameManager';
const { ccclass, property } = _decorator;

/**
 * 企鹅特殊能力测试辅助类
 * 用于在开发阶段测试企鹅特殊复活功能
 */
@ccclass('PenguinTestHelper')
export class PenguinTestHelper extends Component {

    start() {
        // 在控制台输出测试方法
        console.log("=== 企鹅特殊能力测试辅助 ===");
        console.log("可用的测试方法:");
        console.log("- PenguinTestHelper.testPenguinSpecialRevive() // 测试企鹅特殊复活逻辑");
        console.log("- PenguinTestHelper.switchToPenguin() // 切换到企鹅角色");
        console.log("- PenguinTestHelper.resetPenguinState() // 重置企鹅状态");
        console.log("- PenguinTestHelper.checkPenguinStatus() // 检查企鹅状态");
        console.log("- PenguinTestHelper.testPhysicsReset() // 测试物理状态重置");
    }

    /**
     * 测试企鹅特殊复活逻辑
     */
    public static testPenguinSpecialRevive(): void {
        console.log("=== 测试企鹅特殊复活逻辑 ===");
        
        // 1. 切换到企鹅
        GameData.setSelectedBirdType(BirdType.PENGUIN);
        console.log("1. 已切换到企鹅角色");
        
        // 2. 检查是否可以使用特殊复活
        const canRevive = GameData.canPenguinSpecialRevive();
        console.log(`2. 企鹅可以使用特殊复活: ${canRevive}`);
        
        // 3. 模拟游戏状态
        GameData.addScore(5);
        GameData.addCoin(3);
        console.log(`3. 模拟游戏状态 - 分数: ${GameData.getScore()}, 金币: ${GameData.getSessionCoins()}`);
        
        // 4. 触发企鹅特殊复活
        if (canRevive) {
            GameData.setPenguinSpecialReviveState(GameData.getScore(), GameData.getSessionCoins());
            console.log("4. 已触发企鹅特殊复活");
        }
        
        // 5. 检查状态
        console.log(`5. 企鹅特殊复活状态: ${GameData.isPenguinSpecialRevive()}`);
        console.log(`6. 企鹅已使用特殊复活: ${GameData.hasPenguinUsedSpecialRevive()}`);
        
        // 6. 应用复活状态
        GameData.applyReviveState();
        console.log("7. 已应用复活状态");
        
        // 7. 检查第二次是否还能复活
        const canReviveAgain = GameData.canPenguinSpecialRevive();
        console.log(`8. 企鹅第二次可以使用特殊复活: ${canReviveAgain}`);
        
        console.log("=== 测试完成 ===");
    }

    /**
     * 切换到企鹅角色
     */
    public static switchToPenguin(): void {
        GameData.setSelectedBirdType(BirdType.PENGUIN);
        console.log("已切换到企鹅角色");
        this.checkPenguinStatus();
    }

    /**
     * 重置企鹅状态
     */
    public static resetPenguinState(): void {
        GameData.resetPenguinSpecialRevive();
        GameData.clearPenguinSpecialReviveState();
        GameData.resetScore();
        GameData.resetSessionCoins();
        console.log("已重置企鹅状态");
        this.checkPenguinStatus();
    }

    /**
     * 检查企鹅状态
     */
    public static checkPenguinStatus(): void {
        console.log("=== 企鹅状态检查 ===");
        console.log(`当前选择的小鸟: ${GameData.getBirdName(GameData.getSelectedBirdType())}`);
        console.log(`是否是企鹅: ${GameData.getSelectedBirdType() === BirdType.PENGUIN}`);
        console.log(`企鹅可以使用特殊复活: ${GameData.canPenguinSpecialRevive()}`);
        console.log(`企鹅已使用特殊复活: ${GameData.hasPenguinUsedSpecialRevive()}`);
        console.log(`企鹅特殊复活状态: ${GameData.isPenguinSpecialRevive()}`);
        console.log(`当前分数: ${GameData.getScore()}`);
        console.log(`当前本局金币: ${GameData.getSessionCoins()}`);
        console.log("==================");
    }

    /**
     * 模拟企鹅死亡场景
     */
    public static simulatePenguinDeath(): void {
        console.log("=== 模拟企鹅死亡场景 ===");
        
        // 确保是企鹅
        if (GameData.getSelectedBirdType() !== BirdType.PENGUIN) {
            console.log("当前不是企鹅角色，先切换到企鹅");
            GameData.setSelectedBirdType(BirdType.PENGUIN);
        }
        
        // 设置一些游戏数据
        GameData.addScore(10);
        GameData.addCoin(5);
        console.log(`死亡前状态 - 分数: ${GameData.getScore()}, 金币: ${GameData.getSessionCoins()}`);
        
        // 检查是否可以特殊复活
        if (GameData.canPenguinSpecialRevive()) {
            console.log("企鹅第一次死亡，触发特殊复活");
            GameData.setPenguinSpecialReviveState(GameData.getScore(), GameData.getSessionCoins());
            console.log("特殊复活已触发，应该直接回到准备状态而不显示游戏结束界面");
        } else {
            console.log("企鹅第二次死亡，正常显示游戏结束界面");
        }
        
        this.checkPenguinStatus();
    }

    /**
     * 测试物理状态重置
     */
    public static testPhysicsReset(): void {
        console.log("=== 测试物理状态重置 ===");

        const gameManager = GameManager.inst();
        if (!gameManager || !gameManager.bird) {
            console.error("无法获取GameManager或Bird实例");
            return;
        }

        console.log("1. 测试小鸟安全重置状态方法");
        gameManager.bird.safeResetBirdState(-200, 0);

        console.log("2. 测试小鸟强制重置物理状态方法");
        gameManager.bird.forceResetPhysicsState();

        console.log("3. 测试企鹅特殊复活状态重置");
        // 设置企鹅特殊复活状态并触发重置
        GameData.setPenguinSpecialReviveState(10, 5);
        gameManager.scheduleOnce(() => {
            gameManager.transitionToReadyState();
            console.log("企鹅特殊复活状态重置完成");
        }, 0.1);

        console.log("=== 物理状态重置测试完成 ===");
    }
}
