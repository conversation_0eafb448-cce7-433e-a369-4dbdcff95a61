import { _decorator, Component, Node, Button, Label } from 'cc';
import { GameData } from '../GameData';
import { GameManager } from '../GameManager';
import { BackgroundManager } from '../BackgroundManager';
const { ccclass, property } = _decorator;

/**
 * 背景商店管理器 - 处理背景的购买和使用逻辑
 */
@ccclass('BackgroundShopManager')
export class BackgroundShopManager extends Component {

    // 草原背景节点（默认拥有）
    @property(Node)
    grasslandBackground: Node = null;
    @property(Button)
    grasslandUseButton: Button = null;

    // 冰川背景节点
    @property(Node)
    glacierBackground: Node = null;
    @property(Button)
    glacierPurchaseButton: Button = null;
    @property(Button)
    glacierUseButton: Button = null;
    @property(Label)
    glacierPriceLabel: Label = null;
    @property(Node)
    glacierCoinIcon: Node = null;
    @property(Node)
    glacierCoinLackSprite: Node = null;

    // 湖泊背景节点
    @property(Node)
    lakeBackground: Node = null;
    @property(Button)
    lakePurchaseButton: Button = null;
    @property(Button)
    lakeUseButton: Button = null;
    @property(Label)
    lakePriceLabel: Label = null;
    @property(Node)
    lakeCoinIcon: Node = null;
    @property(Node)
    lakeCoinLackSprite: Node = null;

    // 背景价格配置
    private readonly GLACIER_PRICE = 10000;
    private readonly LAKE_PRICE = 10000;

    // 背景ID映射
    private readonly BACKGROUND_GRASSLAND = 1;
    private readonly BACKGROUND_GLACIER = 2;
    private readonly BACKGROUND_LAKE = 3;



    start() {
        this.initializeBackgrounds();
        this.setupButtonEvents();
    }

    /**
     * 初始化背景状态
     */
    private initializeBackgrounds(): void {
        // 设置价格标签
        if (this.glacierPriceLabel) {
            this.glacierPriceLabel.string = this.GLACIER_PRICE.toString();
        }
        if (this.lakePriceLabel) {
            this.lakePriceLabel.string = this.LAKE_PRICE.toString();
        }

        // 更新所有背景的显示状态
        this.updateBackgroundStates();
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        // 草原使用按钮
        if (this.grasslandUseButton) {
            this.grasslandUseButton.node.on(Button.EventType.CLICK, () => {
                this.useBackground(this.BACKGROUND_GRASSLAND);
            }, this);
        }

        // 冰川购买和使用按钮
        if (this.glacierPurchaseButton) {
            this.glacierPurchaseButton.node.on(Button.EventType.CLICK, () => {
                this.purchaseBackground(this.BACKGROUND_GLACIER, this.GLACIER_PRICE);
            }, this);
        }
        if (this.glacierUseButton) {
            this.glacierUseButton.node.on(Button.EventType.CLICK, () => {
                this.useBackground(this.BACKGROUND_GLACIER);
            }, this);
        }

        // 湖泊购买和使用按钮
        if (this.lakePurchaseButton) {
            this.lakePurchaseButton.node.on(Button.EventType.CLICK, () => {
                this.purchaseBackground(this.BACKGROUND_LAKE, this.LAKE_PRICE);
            }, this);
        }
        if (this.lakeUseButton) {
            this.lakeUseButton.node.on(Button.EventType.CLICK, () => {
                this.useBackground(this.BACKGROUND_LAKE);
            }, this);
        }
    }

    /**
     * 更新所有背景的显示状态
     */
    private updateBackgroundStates(): void {
        this.updateBackgroundState(this.BACKGROUND_GRASSLAND);
        this.updateBackgroundState(this.BACKGROUND_GLACIER);
        this.updateBackgroundState(this.BACKGROUND_LAKE);
    }

    /**
     * 更新指定背景的显示状态
     */
    private updateBackgroundState(backgroundId: number): void {
        const isPurchased = GameData.isBackgroundPurchased(backgroundId);
        const isSelected = GameData.getSelectedBackground() === backgroundId;

        switch (backgroundId) {
            case this.BACKGROUND_GRASSLAND:
                // 草原背景默认拥有，只显示使用按钮
                if (this.grasslandUseButton) {
                    this.grasslandUseButton.node.active = true;
                    this.grasslandUseButton.interactable = !isSelected;
                    // 如果已选择，可以改变按钮文本或样式
                    const grasslandLabel = this.grasslandUseButton.node.getComponentInChildren(Label);
                    if (grasslandLabel) {
                        grasslandLabel.string = isSelected ? "使用中" : "使用";
                    }
                }
                break;

            case this.BACKGROUND_GLACIER:
                this.updatePurchasableBackground(
                    isPurchased, isSelected,
                    this.glacierPurchaseButton, this.glacierUseButton,
                    this.glacierPriceLabel, this.glacierCoinIcon
                );
                break;

            case this.BACKGROUND_LAKE:
                this.updatePurchasableBackground(
                    isPurchased, isSelected,
                    this.lakePurchaseButton, this.lakeUseButton,
                    this.lakePriceLabel, this.lakeCoinIcon
                );
                break;
        }
    }

    /**
     * 更新可购买背景的状态
     */
    private updatePurchasableBackground(
        isPurchased: boolean, isSelected: boolean,
        purchaseButton: Button, useButton: Button,
        priceLabel: Label, coinIcon: Node
    ): void {
        if (isPurchased) {
            // 已购买：隐藏购买按钮和价格，显示使用按钮
            if (purchaseButton) purchaseButton.node.active = false;
            if (priceLabel) priceLabel.node.active = false;
            if (coinIcon) coinIcon.active = false;
            if (useButton) {
                useButton.node.active = true;
                useButton.interactable = !isSelected;
                const useLabel = useButton.node.getComponentInChildren(Label);
                if (useLabel) {
                    useLabel.string = isSelected ? "使用中" : "使用";
                }
            }
        } else {
            // 未购买：显示购买按钮和价格，隐藏使用按钮
            if (purchaseButton) purchaseButton.node.active = true;
            if (priceLabel) priceLabel.node.active = true;
            if (coinIcon) coinIcon.active = true;
            if (useButton) useButton.node.active = false;
        }
    }

    /**
     * 购买背景
     */
    private purchaseBackground(backgroundId: number, price: number): void {
        console.log(`尝试购买背景 ${backgroundId}，价格 ${price}`);

        // 检查金币是否足够
        const totalCoins = GameData.getTotalCoins();
        if (totalCoins < price) {
            console.log(`金币不足，需要 ${price} 金币，当前只有 ${totalCoins} 金币`);
            this.showCoinLackSprite(backgroundId);
            return;
        }

        if (GameData.purchaseBackground(backgroundId, price)) {
            console.log(`成功购买背景 ${backgroundId}`);
            // 更新UI状态
            this.updateBackgroundState(backgroundId);
            // 可以添加购买成功的音效或动画
        } else {
            console.log(`购买背景 ${backgroundId} 失败`);
            this.showCoinLackSprite(backgroundId);
        }
    }

    /**
     * 使用背景
     */
    private useBackground(backgroundId: number): void {
        console.log(`使用背景 ${backgroundId}`);

        // 检查是否已购买
        if (!GameData.isBackgroundPurchased(backgroundId)) {
            console.log(`背景 ${backgroundId} 未购买，无法使用`);
            return;
        }

        // 设置为当前选择的背景
        GameData.setSelectedBackground(backgroundId);

        // 在游戏场景中切换背景
        this.switchGameBackground(backgroundId);

        // 更新所有背景的UI状态
        this.updateBackgroundStates();

        console.log(`成功切换到背景 ${backgroundId}`);
    }

    /**
     * 在游戏场景中切换背景
     */
    private switchGameBackground(backgroundId: number): void {
        // 在商店场景中，我们只保存选择，不直接切换游戏背景
        // 游戏背景会在下次进入Game场景时根据保存的选择自动切换
        console.log(`背景选择已保存，将在下次进入游戏时应用背景 ${backgroundId}`);
    }

    /**
     * 显示金币不足提示
     */
    private showCoinLackSprite(backgroundId: number): void {
        let coinLackSprite: Node = null;

        switch (backgroundId) {
            case this.BACKGROUND_GLACIER:
                coinLackSprite = this.glacierCoinLackSprite;
                break;
            case this.BACKGROUND_LAKE:
                coinLackSprite = this.lakeCoinLackSprite;
                break;
            default:
                console.log("未知背景ID，无法显示金币不足提示");
                return;
        }

        if (coinLackSprite) {
            coinLackSprite.active = true;
            console.log(`显示背景 ${backgroundId} 金币不足提示`);

            // 2秒后自动隐藏
            this.scheduleOnce(() => {
                if (coinLackSprite && coinLackSprite.isValid) {
                    coinLackSprite.active = false;
                    console.log(`隐藏背景 ${backgroundId} 金币不足提示`);
                }
            }, 2.0);
        } else {
            console.error(`背景 ${backgroundId} 金币不足提示节点未设置！`);
        }
    }

    /**
     * 外部调用：刷新所有背景状态（比如从其他界面返回时）
     */
    public refreshBackgroundStates(): void {
        this.updateBackgroundStates();
    }

    onDestroy() {
        // 清理事件监听
        if (this.grasslandUseButton && this.grasslandUseButton.node) {
            this.grasslandUseButton.node.off(Button.EventType.CLICK);
        }
        if (this.glacierPurchaseButton && this.glacierPurchaseButton.node) {
            this.glacierPurchaseButton.node.off(Button.EventType.CLICK);
        }
        if (this.glacierUseButton && this.glacierUseButton.node) {
            this.glacierUseButton.node.off(Button.EventType.CLICK);
        }
        if (this.lakePurchaseButton && this.lakePurchaseButton.node) {
            this.lakePurchaseButton.node.off(Button.EventType.CLICK);
        }
        if (this.lakeUseButton && this.lakeUseButton.node) {
            this.lakeUseButton.node.off(Button.EventType.CLICK);
        }
    }
}
