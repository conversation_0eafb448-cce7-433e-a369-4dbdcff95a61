<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>剪贴板测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 剪贴板功能测试</h1>
        
        <div class="test-section">
            <h3>📋 环境检测</h3>
            <button onclick="checkEnvironment()">检测环境</button>
            <div id="env-result" class="result info"></div>
        </div>

        <div class="test-section">
            <h3>🔧 复制测试</h3>
            <input type="text" id="test-text" value="wa6u15" placeholder="输入要复制的文本">
            <br>
            <button onclick="testCopy()">测试复制</button>
            <button onclick="testCopyWithCallbacks()">测试复制（带回调）</button>
            <div id="copy-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 API可用性检查</h3>
            <button onclick="checkAPIs()">检查所有API</button>
            <div id="api-result" class="result info"></div>
        </div>

        <div class="test-section">
            <h3>📝 控制台日志</h3>
            <button onclick="clearLogs()">清空日志</button>
            <button onclick="showLogs()">显示日志</button>
            <div id="log-result" class="result info"></div>
        </div>
    </div>

    <script>
        // 模拟微信环境（如果需要）
        if (!window.wx && window.location.search.includes('mock=wx')) {
            window.wx = {
                setClipboardData: function(options) {
                    console.log('模拟微信API调用:', options);
                    // 模拟失败
                    setTimeout(() => {
                        if (options.fail) {
                            options.fail({error: 'mock_error', errMsg: '模拟的微信API失败'});
                        }
                    }, 100);
                },
                showToast: function(options) {
                    console.log('模拟Toast:', options);
                    alert('Toast: ' + options.title);
                },
                showModal: function(options) {
                    console.log('模拟Modal:', options);
                    alert('Modal: ' + options.content);
                    if (options.success) {
                        options.success({confirm: true, cancel: false});
                    }
                }
            };
        }

        // 日志收集
        const logs = [];
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            logs.push(new Date().toLocaleTimeString() + ': ' + args.join(' '));
            originalConsoleLog.apply(console, arguments);
        };

        function checkEnvironment() {
            const result = document.getElementById('env-result');
            let info = '';
            
            info += `用户代理: ${navigator.userAgent}\n`;
            info += `是否HTTPS: ${window.location.protocol === 'https:'}\n`;
            info += `是否微信环境: ${typeof wx !== 'undefined'}\n`;
            info += `navigator.clipboard: ${!!navigator.clipboard}\n`;
            info += `window.isSecureContext: ${window.isSecureContext}\n`;
            info += `document.execCommand: ${typeof document.execCommand}\n`;
            
            result.textContent = info;
            result.className = 'result info';
        }

        function testCopy() {
            const text = document.getElementById('test-text').value;
            const result = document.getElementById('copy-result');
            
            result.textContent = '正在测试复制...';
            result.className = 'result info';
            
            // 这里需要引入我们的WeChatClipboard类
            // 由于这是独立的测试页面，我们模拟其行为
            testClipboardFunction(text, result);
        }

        function testCopyWithCallbacks() {
            const text = document.getElementById('test-text').value;
            const result = document.getElementById('copy-result');
            
            result.textContent = '正在测试复制（带回调）...';
            result.className = 'result info';
            
            testClipboardFunction(text, result, 
                () => {
                    result.textContent += '\n✅ 成功回调被调用';
                    result.className = 'result success';
                },
                (error) => {
                    result.textContent += '\n❌ 失败回调被调用: ' + error;
                    result.className = 'result error';
                }
            );
        }

        function testClipboardFunction(text, resultElement, onSuccess, onFail) {
            console.log('开始复制测试:', text);
            
            // 方法1: 微信API
            if (typeof wx !== 'undefined' && wx.setClipboardData) {
                console.log('尝试微信API');
                wx.setClipboardData({
                    data: text,
                    success: () => {
                        console.log('微信API成功');
                        resultElement.textContent = '✅ 微信API复制成功';
                        resultElement.className = 'result success';
                        if (onSuccess) onSuccess();
                    },
                    fail: (err) => {
                        console.log('微信API失败:', err);
                        resultElement.textContent = '❌ 微信API失败，尝试其他方法...\n错误: ' + JSON.stringify(err);
                        resultElement.className = 'result error';
                        
                        // 尝试其他方法
                        tryOtherMethods(text, resultElement, onSuccess, onFail);
                    }
                });
                return;
            }
            
            // 直接尝试其他方法
            tryOtherMethods(text, resultElement, onSuccess, onFail);
        }

        function tryOtherMethods(text, resultElement, onSuccess, onFail) {
            // 方法2: 现代API
            if (navigator.clipboard && window.isSecureContext) {
                console.log('尝试现代API');
                navigator.clipboard.writeText(text).then(() => {
                    console.log('现代API成功');
                    resultElement.textContent += '\n✅ 现代API复制成功';
                    resultElement.className = 'result success';
                    if (onSuccess) onSuccess();
                }).catch((err) => {
                    console.log('现代API失败:', err);
                    resultElement.textContent += '\n❌ 现代API失败: ' + err.message;
                    tryLegacyMethod(text, resultElement, onSuccess, onFail);
                });
                return;
            }
            
            // 直接尝试传统方法
            tryLegacyMethod(text, resultElement, onSuccess, onFail);
        }

        function tryLegacyMethod(text, resultElement, onSuccess, onFail) {
            console.log('尝试传统API');
            try {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.position = "fixed";
                textArea.style.left = "-999999px";
                textArea.style.top = "-999999px";
                textArea.style.opacity = "0";
                
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (successful) {
                    console.log('传统API成功');
                    resultElement.textContent += '\n✅ 传统API复制成功';
                    resultElement.className = 'result success';
                    if (onSuccess) onSuccess();
                } else {
                    console.log('传统API失败');
                    resultElement.textContent += '\n❌ 传统API失败';
                    resultElement.className = 'result error';
                    if (onFail) onFail('所有方法都失败');
                }
            } catch (error) {
                console.log('传统API异常:', error);
                resultElement.textContent += '\n❌ 传统API异常: ' + error.message;
                resultElement.className = 'result error';
                if (onFail) onFail('传统API异常');
            }
        }

        function checkAPIs() {
            const result = document.getElementById('api-result');
            let info = '';
            
            info += `微信API: ${typeof wx !== 'undefined' && typeof wx.setClipboardData === 'function'}\n`;
            info += `现代API: ${!!(navigator.clipboard && window.isSecureContext)}\n`;
            info += `传统API: ${typeof document.execCommand === 'function'}\n`;
            info += `任何可用: ${(typeof wx !== 'undefined' && typeof wx.setClipboardData === 'function') || !!(navigator.clipboard && window.isSecureContext) || typeof document.execCommand === 'function'}\n`;
            
            result.textContent = info;
            result.className = 'result info';
        }

        function clearLogs() {
            logs.length = 0;
            document.getElementById('log-result').textContent = '日志已清空';
        }

        function showLogs() {
            const result = document.getElementById('log-result');
            result.textContent = logs.join('\n') || '暂无日志';
        }

        // 页面加载时自动检测环境
        window.onload = function() {
            checkEnvironment();
            checkAPIs();
        };
    </script>
</body>
</html>
