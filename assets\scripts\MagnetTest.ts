import { _decorator, Component, Node } from 'cc';
import { ItemManager, ItemType } from './ItemManager';
import { GameData } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 吸金石功能测试类
 * 用于验证吸金石的各种功能是否正常工作
 */
@ccclass('MagnetTest')
export class MagnetTest extends Component {

    start() {
        // 延迟执行测试，确保其他系统已初始化
        this.scheduleOnce(() => {
            this.runAllTests();
        }, 1.0);
    }

    /**
     * 运行所有测试
     */
    private runAllTests(): void {
        console.log("=== 开始吸金石功能测试 ===");

        this.testBasicFunctions();
        this.testPurchaseLogic();
        this.testActivationLogic();
        this.testCollectionDistance();
        this.testGameEndConsumption();
        this.testPersistentActivation();

        console.log("=== 吸金石功能测试完成 ===");
    }

    /**
     * 测试基础功能
     */
    private testBasicFunctions(): void {
        console.log("\n--- 测试1: 基础功能测试 ---");

        // 测试道具配置
        const price = ItemManager.getItemPrice(ItemType.MAGNET);
        const name = ItemManager.getItemName(ItemType.MAGNET);
        const description = ItemManager.getItemDescription(ItemType.MAGNET);

        console.log(`道具名称: ${name}`);
        console.log(`道具价格: ${price}`);
        console.log(`道具描述: ${description}`);

        // 验证初始状态
        const initialCount = ItemManager.getItemCount(ItemType.MAGNET);
        const initialActive = ItemManager.isItemActive(ItemType.MAGNET);
        const initialDistance = ItemManager.getCoinCollectionDistance();

        console.log(`初始数量: ${initialCount}`);
        console.log(`初始激活状态: ${initialActive}`);
        console.log(`初始收集距离: ${initialDistance}`);

        // 验证配置正确性
        if (price === 50 && name === "吸金石" && initialDistance === 50) {
            console.log("✅ 基础功能测试通过");
        } else {
            console.error("❌ 基础功能测试失败");
        }
    }

    /**
     * 测试购买逻辑
     */
    private testPurchaseLogic(): void {
        console.log("\n--- 测试2: 购买逻辑测试 ---");

        // 保存当前金币数
        const originalCoins = GameData.getTotalCoins();
        console.log(`当前金币数: ${originalCoins}`);

        // 测试金币不足的情况
        if (originalCoins < 50) {
            console.log("测试金币不足情况...");
            const success = ItemManager.purchaseItem(ItemType.MAGNET);
            if (!success) {
                console.log("✅ 金币不足时购买失败，符合预期");
            } else {
                console.error("❌ 金币不足时购买成功，不符合预期");
            }
        }

        // 设置足够的金币进行购买测试
        localStorage.setItem("TotalCoins", "100");
        console.log("设置金币数为100，测试购买...");

        const beforeCount = ItemManager.getItemCount(ItemType.MAGNET);
        const success = ItemManager.purchaseItem(ItemType.MAGNET);
        const afterCount = ItemManager.getItemCount(ItemType.MAGNET);
        const afterCoins = GameData.getTotalCoins();

        console.log(`购买前数量: ${beforeCount}`);
        console.log(`购买结果: ${success}`);
        console.log(`购买后数量: ${afterCount}`);
        console.log(`购买后金币: ${afterCoins}`);

        if (success && afterCount === beforeCount + 1 && afterCoins === 50) {
            console.log("✅ 购买逻辑测试通过");
        } else {
            console.error("❌ 购买逻辑测试失败");
        }

        // 恢复原始金币数
        localStorage.setItem("TotalCoins", originalCoins.toString());
    }

    /**
     * 测试激活逻辑
     */
    private testActivationLogic(): void {
        console.log("\n--- 测试3: 激活逻辑测试 ---");

        // 确保有道具可用
        ItemManager.setItemCount(ItemType.MAGNET, 1);

        // 测试激活
        console.log("测试激活吸金石...");
        ItemManager.activateItemEffect(ItemType.MAGNET);

        const isActive = ItemManager.isItemActive(ItemType.MAGNET);
        const isMagnetActive = ItemManager.isMagnetActive();
        const distance = ItemManager.getCoinCollectionDistance();

        console.log(`激活状态: ${isActive}`);
        console.log(`内存中激活状态: ${isMagnetActive}`);
        console.log(`收集距离: ${distance}`);

        if (isActive && isMagnetActive && distance === 400) {
            console.log("✅ 激活功能测试通过");
        } else {
            console.error("❌ 激活功能测试失败");
        }

        // 测试禁用
        console.log("测试禁用吸金石...");
        ItemManager.deactivateItemEffect(ItemType.MAGNET);

        const isActiveAfter = ItemManager.isItemActive(ItemType.MAGNET);
        const isMagnetActiveAfter = ItemManager.isMagnetActive();
        const distanceAfter = ItemManager.getCoinCollectionDistance();

        console.log(`禁用后激活状态: ${isActiveAfter}`);
        console.log(`禁用后内存中激活状态: ${isMagnetActiveAfter}`);
        console.log(`禁用后收集距离: ${distanceAfter}`);

        if (!isActiveAfter && !isMagnetActiveAfter && distanceAfter === 50) {
            console.log("✅ 禁用功能测试通过");
        } else {
            console.error("❌ 禁用功能测试失败");
        }
    }

    /**
     * 测试收集距离功能
     */
    private testCollectionDistance(): void {
        console.log("\n--- 测试4: 收集距离测试 ---");

        // 测试默认距离
        ItemManager.deactivateItemEffect(ItemType.MAGNET);
        const defaultDistance = ItemManager.getCoinCollectionDistance();
        console.log(`默认收集距离: ${defaultDistance}`);

        // 测试激活后距离
        ItemManager.activateItemEffect(ItemType.MAGNET);
        const magnetDistance = ItemManager.getCoinCollectionDistance();
        console.log(`吸金石激活后距离: ${magnetDistance}`);

        if (defaultDistance === 50 && magnetDistance === 300) {
            console.log("✅ 收集距离测试通过");
        } else {
            console.error("❌ 收集距离测试失败");
        }
    }

    /**
     * 测试游戏结束时的永久性逻辑
     */
    private testGameEndConsumption(): void {
        console.log("\n--- 测试5: 游戏结束永久性测试 ---");

        // 设置测试环境：有1个吸金石并激活
        ItemManager.setItemCount(ItemType.MAGNET, 1);
        ItemManager.activateItemEffect(ItemType.MAGNET);

        console.log("游戏结束前状态:");
        console.log(`- 数量: ${ItemManager.getItemCount(ItemType.MAGNET)}`);
        console.log(`- 激活: ${ItemManager.isItemActive(ItemType.MAGNET)}`);

        // 模拟游戏结束
        ItemManager.consumeActiveItems();

        console.log("游戏结束后状态:");
        console.log(`- 数量: ${ItemManager.getItemCount(ItemType.MAGNET)}`);
        console.log(`- 激活: ${ItemManager.isItemActive(ItemType.MAGNET)}`);

        const finalCount = ItemManager.getItemCount(ItemType.MAGNET);
        const finalActive = ItemManager.isItemActive(ItemType.MAGNET);

        if (finalCount === 1 && finalActive) {
            console.log("✅ 游戏结束永久性测试通过（吸金石不被消耗，保持激活）");
        } else {
            console.error("❌ 游戏结束永久性测试失败");
        }
    }

    /**
     * 测试永久激活逻辑
     */
    private testPersistentActivation(): void {
        console.log("\n--- 测试6: 永久激活逻辑测试 ---");

        // 设置测试环境：有1个吸金石并激活
        ItemManager.setItemCount(ItemType.MAGNET, 1);
        ItemManager.activateItemEffect(ItemType.MAGNET);

        console.log("初始状态:");
        console.log(`- 数量: ${ItemManager.getItemCount(ItemType.MAGNET)}`);
        console.log(`- 激活: ${ItemManager.isItemActive(ItemType.MAGNET)}`);

        // 模拟游戏结束（吸金石不被消耗）
        ItemManager.consumeActiveItems();

        console.log("游戏结束后:");
        console.log(`- 数量: ${ItemManager.getItemCount(ItemType.MAGNET)}`);
        console.log(`- 激活: ${ItemManager.isItemActive(ItemType.MAGNET)}`);

        // 模拟下一局游戏开始
        ItemManager.resetAllEffects();

        console.log("下一局游戏开始:");
        console.log(`- 数量: ${ItemManager.getItemCount(ItemType.MAGNET)}`);
        console.log(`- 激活: ${ItemManager.isItemActive(ItemType.MAGNET)}`);
        console.log(`- 收集距离: ${ItemManager.getCoinCollectionDistance()}`);

        const finalCount = ItemManager.getItemCount(ItemType.MAGNET);
        const finalActive = ItemManager.isItemActive(ItemType.MAGNET);
        const finalDistance = ItemManager.getCoinCollectionDistance();

        if (finalCount === 1 && finalActive && finalDistance === 300) {
            console.log("✅ 永久激活逻辑测试通过");
        } else {
            console.error("❌ 永久激活逻辑测试失败");
        }
    }

    /**
     * 公开的测试方法，可以在控制台中调用
     */
    public static runQuickTest(): void {
        console.log("=== 快速吸金石测试（永久道具版本）===");

        // 测试基本功能
        console.log("吸金石价格:", ItemManager.getItemPrice(ItemType.MAGNET), "金币");
        console.log("吸金石名称:", ItemManager.getItemName(ItemType.MAGNET));
        console.log("吸金石描述:", ItemManager.getItemDescription(ItemType.MAGNET));

        // 测试距离功能
        console.log("默认收集距离:", ItemManager.getCoinCollectionDistance());

        // 模拟购买并激活
        ItemManager.setItemCount(ItemType.MAGNET, 1);
        ItemManager.activateItemEffect(ItemType.MAGNET);
        console.log("激活后收集距离:", ItemManager.getCoinCollectionDistance());

        // 测试禁用
        ItemManager.deactivateItemEffect(ItemType.MAGNET);
        console.log("禁用后收集距离:", ItemManager.getCoinCollectionDistance());

        // 测试永久性
        ItemManager.activateItemEffect(ItemType.MAGNET);
        console.log("重新激活后收集距离:", ItemManager.getCoinCollectionDistance());
        console.log("吸金石状态（永久道具）:", ItemManager.getItemCount(ItemType.MAGNET) > 0 ? "已拥有" : "未拥有");

        console.log("=== 快速测试完成 ===");
    }
}
