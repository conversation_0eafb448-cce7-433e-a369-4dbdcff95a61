import { _decorator, Component, Node, Button, ScrollView, Prefab, instantiate, Label, Sprite, SpriteFrame, Color, UITransform, Layout, EventTouch, Vec3, ScrollBar, ImageAsset, Texture2D } from 'cc';
import { GameMode } from '../GameData';
import { WeChatFriendsData, RankItem } from '../Data/WeChatFriendsData';
import { CustomScrollBar } from './CustomScrollBar';
const { ccclass, property } = _decorator;

/**
 * 微信好友排行榜UI管理器
 */
@ccclass('FriendsRankUI')
export class FriendsRankUI extends Component {

    // 关卡按钮容器
    @property(Node)
    levelButtonsContainer: Node = null;

    // 关卡按钮
    @property(Button)
    easyBtn: Button = null;

    @property(Button)
    normalBtn: Button = null;

    @property(Button)
    hardBtn: Button = null;

    @property(Button)
    windBtn: Button = null;

    @property(Button)
    fogBtn: Button = null;

    @property(Button)
    snowBtn: Button = null;

    // 排行榜滚动视图
    @property(ScrollView)
    friendsRankScrollView: ScrollView = null;

    // 排行榜条目预制体
    @property(Prefab)
    friendsRankItemPrefab: Prefab = null;

    // 默认头像
    @property(SpriteFrame)
    defaultAvatarFrame: SpriteFrame = null;

    // 自定义滚动条（可选，如果设置了就使用自定义滚动条）
    @property(CustomScrollBar)
    customScrollBar: CustomScrollBar = null;

    // sharedCanvas 展示用的精灵（可选，如未在编辑器拖拽则运行时创建）
    @property(Sprite)
    private sharedCanvasSprite: Sprite = null;

    private _sharedCanvasTex: SpriteFrame | null = null;
    private _sharedCanvasTimer: number | null = null;

    // 当前选中的关卡
    private currentGameMode: GameMode = GameMode.NORMAL_EASY;

    // 按钮颜色配置
    private readonly SELECTED_COLOR = new Color(100, 200, 100, 255);   // 选中状态：绿色
    private readonly NORMAL_COLOR = new Color(255, 255, 255, 255);     // 普通状态：白色

    // 滚动条拖拽相关
    private isDraggingScrollBar: boolean = false;
    private scrollBarNode: Node = null; // 滚动条节点
    private scrollBarHeight: number = 0; // 滚动条高度
    private lastTouchY: number = 0; // 上次触摸的Y坐标

    onLoad() {
        console.log("FriendsRankUI: 初始化好友排行榜UI");
        this.setupButtonEvents();

        // 根据是否有自定义滚动条来决定使用哪种滚动方式
        // 初始化 sharedCanvas 展示
        this.initSharedCanvasPresenter();
        if (this.customScrollBar) {
            console.log("FriendsRankUI: 使用自定义滚动条");
        } else {
            console.log("FriendsRankUI: 未设置自定义滚动条，跳过滚动条拖拽功能");
            // 注释掉内置滚动条功能，因为我们主要使用CustomScrollBar
            // this.setupScrollBarDrag();
        }
    }

    start() {
        // 默认显示轻松关卡的排行榜
        this.selectLevel(GameMode.NORMAL_EASY);
    }

    onDestroy() {
        // 移除滚动条事件监听
        if (this.scrollBarNode) {
            this.scrollBarNode.off(Node.EventType.TOUCH_START, this.onScrollBarTouchStart, this);
            this.scrollBarNode.off(Node.EventType.TOUCH_MOVE, this.onScrollBarTouchMove, this);
            this.scrollBarNode.off(Node.EventType.TOUCH_END, this.onScrollBarTouchEnd, this);
            this.scrollBarNode.off(Node.EventType.TOUCH_CANCEL, this.onScrollBarTouchEnd, this);
        }
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        // 注意：按钮事件现在通过编辑器配置，这里不需要代码绑定
        // 如果需要代码绑定，使用以下方式：
        /*
        if (this.easyBtn) {
            this.easyBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.NORMAL_EASY), this);
        }
        if (this.normalBtn) {
            this.normalBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.NORMAL_STANDARD), this);
        }
        if (this.hardBtn) {
            this.hardBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.NORMAL_HARD), this);
        }
        if (this.windBtn) {
            this.windBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.CHALLENGE_WIND), this);
        }
        if (this.fogBtn) {
            this.fogBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.CHALLENGE_FOG), this);
        }
        if (this.snowBtn) {
            this.snowBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.CHALLENGE_SNOW), this);
        }
        */
    }

    /**
     * 选择关卡并更新排行榜
     */
    public selectLevel(event?: any, customEventData?: any): void {
        let gameMode: GameMode;

        console.log(`FriendsRankUI: 收到参数 event=${event}, customEventData=${customEventData}, type=${typeof customEventData}`);

        // 处理从编辑器按钮事件传来的参数
        if (customEventData !== undefined && customEventData !== null) {
            // 尝试多种方式解析参数
            let modeValue: number;

            if (typeof customEventData === 'string') {
                modeValue = parseInt(customEventData);
            } else if (typeof customEventData === 'number') {
                modeValue = customEventData;
            } else {
                // 尝试转换为字符串再解析
                modeValue = parseInt(String(customEventData));
            }

            if (!isNaN(modeValue) && modeValue >= 0 && modeValue <= 5) {
                gameMode = modeValue as GameMode;
            } else {
                gameMode = GameMode.NORMAL_EASY;
                console.warn(`FriendsRankUI: 无效的CustomEventData ${customEventData}，使用默认值`);
            }
        } else if (typeof event === 'number') {
            // 直接传入数字
            gameMode = event as GameMode;
        } else {
            // 默认值
            gameMode = GameMode.NORMAL_EASY;
            console.warn(`FriendsRankUI: 无效的参数，使用默认值`);
        }

        console.log(`FriendsRankUI: 选择关卡 ${gameMode} (${this.getGameModeName(gameMode)})`);

        this.currentGameMode = gameMode;
        this.updateButtonStates();
        this.updateRankingList(); // 异步调用，不需要await
    }

    /**
     * 获取游戏模式名称
     */
    private getGameModeName(mode: GameMode): string {
        const names = {
            [GameMode.NORMAL_EASY]: "轻松",
            [GameMode.NORMAL_STANDARD]: "标准",
            [GameMode.NORMAL_HARD]: "困难",
            [GameMode.CHALLENGE_WIND]: "大风吹",
            [GameMode.CHALLENGE_FOG]: "大雾起",
            [GameMode.CHALLENGE_SNOW]: "大雪飘"
        };
        return names[mode] || "未知";
    }

    /**
     * 更新按钮状态
     */
    private updateButtonStates(): void {
        const buttons = [
            { btn: this.easyBtn, mode: GameMode.NORMAL_EASY },
            { btn: this.normalBtn, mode: GameMode.NORMAL_STANDARD },
            { btn: this.hardBtn, mode: GameMode.NORMAL_HARD },
            { btn: this.windBtn, mode: GameMode.CHALLENGE_WIND },
            { btn: this.fogBtn, mode: GameMode.CHALLENGE_FOG },
            { btn: this.snowBtn, mode: GameMode.CHALLENGE_SNOW }
        ];

        buttons.forEach(({ btn, mode }) => {
            if (btn) {
                const sprite = btn.getComponent(Sprite);
                if (sprite) {
                    sprite.color = mode === this.currentGameMode ? this.SELECTED_COLOR : this.NORMAL_COLOR;
                }
            }
        });
    }

    /**
     * 更新排行榜列表
     */
    private async updateRankingList(): Promise<void> {
        if (!this.friendsRankScrollView || !this.friendsRankItemPrefab) {
            console.error("FriendsRankUI: ScrollView或ItemPrefab未设置");
            return;
        }

        console.log("FriendsRankUI: 🔄 开始更新好友排行榜列表（每次重新获取好友数据）");

        try {
            // 🔧 新增：每次打开好友排行榜都上传当前玩家数据到开放数据域（通过开放数据域消息机制）
            console.log("FriendsRankUI: 📤 上传当前玩家数据到开放数据域");
            // 兼容：内部会回退到主域直接 setUserCloudStorage
            (WeChatFriendsData.instance as any).syncScoresToOpenDataContext?.();

            // 🔧 修复：每次打开好友排行榜时重新获取好友记录
            await WeChatFriendsData.instance.refreshFriendsData();

            // 刷新玩家分数，确保排行榜数据是最新的
            WeChatFriendsData.instance.refreshPlayerScores();

            // 触发开放数据域绘制 sharedCanvas（并在右侧保留现有列表渲染，便于过渡验证）
            try {
                const openCtx = (wx as any).getOpenDataContext?.();
                if (openCtx) {
                    const dpr = (wx as any).getSystemInfoSync?.()?.pixelRatio || 2;
                    // 宽度按1.1倍传递给开放数据域，以配合其内部1.1倍宽度绘制
                    const width = Math.floor((this.sharedCanvasSprite?.node.getComponent(UITransform)?.width || 720) * 1.1);
                    const height = Math.floor(this.sharedCanvasSprite?.node.getComponent(UITransform)?.height || 1280);
                    openCtx.postMessage({ type: 'DRAW_FRIENDS_RANK', mode: this.currentGameMode, width, height, dpr });

                    // 使用开放数据域渲染时，隐藏主域ScrollView的内容与滚动条，避免遮挡
                    const content = this.friendsRankScrollView.content;
                    if (content) {
                        content.removeAllChildren();
                        content.active = false;
                    }
                    const sb = this.friendsRankScrollView.verticalScrollBar;
                    if (sb) sb.node.active = false;

                    // 直接返回，后续不再创建主域条目
                    return;
                }
            } catch {}

            // 清空现有列表
            const content = this.friendsRankScrollView.content;
            if (content) {
                content.removeAllChildren();
            }

            // 获取排行榜数据（现在是最新的好友数据）
            const rankingData = WeChatFriendsData.instance.getFriendsRanking(this.currentGameMode, 100);

            console.log(`FriendsRankUI: ✅ 获取到 ${rankingData.length} 条最新好友排行榜数据`);

            // 创建排行榜条目（恢复同步创建，确保数据完整性）
            rankingData.forEach((rankItem) => {
                this.createRankItem(rankItem);
            });

            // 调整content高度以适应所有条目
            this.adjustContentHeight(rankingData.length);

            // 重置滚动位置到顶部
            this.scheduleOnce(() => {
                if (this.friendsRankScrollView) {
                    this.friendsRankScrollView.scrollToTop(0.1);
                }

                // 如果使用自定义滚动条，更新把手大小
                if (this.customScrollBar) {
                    this.customScrollBar.updateHandleSize();
                }
            }, 0.1);

        } catch (error) {
            console.error("FriendsRankUI: ❌ 重新获取好友数据失败，使用缓存数据", error);

            // 如果获取最新数据失败，使用缓存数据作为降级方案
            // 但仍然要上传当前玩家数据到开放数据域
            console.log("FriendsRankUI: 📤 降级模式下仍上传当前玩家数据");
            WeChatFriendsData.instance.syncScoresToWeChatCloudStorage();

            WeChatFriendsData.instance.refreshPlayerScores();

            // 清空现有列表
            const content = this.friendsRankScrollView.content;
            if (content) {
                content.removeAllChildren();
            }

            // 获取缓存的排行榜数据
            const rankingData = WeChatFriendsData.instance.getFriendsRanking(this.currentGameMode, 100);

            console.log(`FriendsRankUI: 📋 使用缓存数据，共 ${rankingData.length} 条排行榜数据`);

            // 创建排行榜条目
            rankingData.forEach((rankItem) => {
                this.createRankItem(rankItem);
            });

            // 调整content高度以适应所有条目
            this.adjustContentHeight(rankingData.length);

            // 重置滚动位置到顶部
            this.scheduleOnce(() => {
                if (this.friendsRankScrollView) {
                    this.friendsRankScrollView.scrollToTop(0.1);
                }

                // 如果使用自定义滚动条，更新把手大小
                if (this.customScrollBar) {
                    this.customScrollBar.updateHandleSize();
                }
            }, 0.1);
        }
    }

    /**
     * 创建排行榜条目
     */
    private createRankItem(rankItem: RankItem): void {
        if (!this.friendsRankItemPrefab || !this.friendsRankScrollView) {
            return;
        }

        const itemNode = instantiate(this.friendsRankItemPrefab);
        if (!itemNode) {
            console.error("FriendsRankUI: 无法实例化排行榜条目预制体");
            return;
        }

        // 设置排行榜条目数据
        this.setupRankItemData(itemNode, rankItem);

        // 添加到滚动视图内容
        this.friendsRankScrollView.content.addChild(itemNode);
    }

    /**
     * 设置排行榜条目数据
     */
    private setupRankItemData(itemNode: Node, rankItem: RankItem): void {
        // 设置排名
        const rankLabel = itemNode.getChildByName("RankLabel")?.getComponent(Label);
        if (rankLabel) {
            rankLabel.string = rankItem.rank.toString();

            // 前三名使用特殊颜色
            if (rankItem.rank === 1) {
                rankLabel.color = new Color(255, 215, 0, 255); // 金色
            } else if (rankItem.rank === 2) {
                rankLabel.color = new Color(192, 192, 192, 255); // 银色
            } else if (rankItem.rank === 3) {
                rankLabel.color = new Color(205, 127, 50, 255); // 铜色
            } else {
                rankLabel.color = new Color(255, 255, 255, 255); // 白色
            }
        }

        // 设置头像（这里使用默认头像，实际项目中应该加载微信头像）
        const avatarSprite = itemNode.getChildByName("AvatarSprite")?.getComponent(Sprite);
        if (avatarSprite && this.defaultAvatarFrame) {
            avatarSprite.spriteFrame = this.defaultAvatarFrame;
        }

        // 设置昵称
        const nameLabel = itemNode.getChildByName("NameLabel")?.getComponent(Label);
        if (nameLabel) {
            nameLabel.string = rankItem.friend.nickname;

            // 如果是自己，使用特殊颜色
            if (rankItem.friend.id === "self") {
                nameLabel.color = new Color(100, 255, 100, 255); // 绿色
            } else {
                nameLabel.color = new Color(255, 255, 255, 255); // 白色
            }
        }

        // 设置分数
        const scoreLabel = itemNode.getChildByName("ScoreLabel")?.getComponent(Label);
        if (scoreLabel) {
            scoreLabel.string = rankItem.score.toString();
        }
    }

    /**
     * 刷新排行榜数据
     */
    public async refreshRanking(): Promise<void> {
        console.log("FriendsRankUI: 刷新排行榜数据");

        try {
            await WeChatFriendsData.instance.refreshFriendsData();
            await this.updateRankingList(); // 现在是异步的
            console.log("FriendsRankUI: 排行榜数据刷新完成");
        } catch (error) {
            console.error("FriendsRankUI: 刷新排行榜数据失败", error);
        }
    }

    /**
     * 获取当前选中的游戏模式
     */
    public getCurrentGameMode(): GameMode {
        return this.currentGameMode;
    }

    /**
     * 调整ScrollView内容高度
     */
    private adjustContentHeight(itemCount: number): void {
        if (!this.friendsRankScrollView || !this.friendsRankScrollView.content) {
            return;
        }

        const content = this.friendsRankScrollView.content;

        // 等待一帧，确保所有条目都已创建完成
        this.scheduleOnce(() => {
            this.calculateAndSetContentHeight(content, itemCount);
        }, 0);
    }

    /**
     * 计算并设置实际的content高度
     */
    private calculateAndSetContentHeight(content: Node, itemCount: number): void {
        if (content.children.length === 0) {
            console.warn("FriendsRankUI: 没有子节点，无法计算高度");
            return;
        }

        // 获取第一个条目的实际高度
        const firstItem = content.children[0];
        const firstItemTransform = firstItem.getComponent(UITransform);
        const actualItemHeight = firstItemTransform?.height || 60;

        // 检查Layout组件的间距设置
        const layout = content.getComponent(Layout);
        const actualSpacing = layout?.spacingY || 0;

        // 计算实际需要的总高度（添加少量缓冲）
        const totalHeight = itemCount * actualItemHeight + (itemCount - 1) * actualSpacing + 20; // 添加20像素缓冲

        // 获取ScrollView的高度
        const scrollViewTransform = this.friendsRankScrollView.node.getComponent(UITransform);
        const scrollViewHeight = scrollViewTransform?.height || 400;

        // 设置content高度：取实际内容高度和ScrollView高度的较大值
        const finalHeight = Math.max(totalHeight, scrollViewHeight);

        // 设置content的高度
        const contentTransform = content.getComponent(UITransform);
        if (contentTransform) {
            contentTransform.height = finalHeight;
            console.log(`FriendsRankUI: 实际条目高度=${actualItemHeight}, 间距=${actualSpacing}, 总高度=${totalHeight}, 最终高度=${finalHeight}`);
        }
    }

    /**
     * 设置滚动条拖拽功能
     */
    private setupScrollBarDrag(): void {
        if (!this.friendsRankScrollView || !this.friendsRankScrollView.verticalScrollBar) {
            console.warn("FriendsRankUI: ScrollView或VerticalScrollBar未设置");
            return;
        }

        // 获取滚动条节点
        this.scrollBarNode = this.friendsRankScrollView.verticalScrollBar.node;
        if (!this.scrollBarNode) {
            console.warn("FriendsRankUI: ScrollBar节点未找到");
            return;
        }

        // 获取滚动条高度
        const scrollBarTransform = this.scrollBarNode.getComponent(UITransform);
        if (scrollBarTransform) {
            this.scrollBarHeight = scrollBarTransform.height;
        } else {
            console.warn("FriendsRankUI: 无法获取ScrollBar的UITransform");
            return;
        }

        // 为滚动条节点添加触摸事件
        this.scrollBarNode.on(Node.EventType.TOUCH_START, this.onScrollBarTouchStart, this);
        this.scrollBarNode.on(Node.EventType.TOUCH_MOVE, this.onScrollBarTouchMove, this);
        this.scrollBarNode.on(Node.EventType.TOUCH_END, this.onScrollBarTouchEnd, this);
        this.scrollBarNode.on(Node.EventType.TOUCH_CANCEL, this.onScrollBarTouchEnd, this);

        console.log(`FriendsRankUI: 滚动条拖拽功能已设置，滚动条高度: ${this.scrollBarHeight}`);
    }

    /**
     * 滚动条触摸开始
     */
    private onScrollBarTouchStart(event: EventTouch): void {
        this.isDraggingScrollBar = true;

        // 记录触摸开始的Y坐标
        this.lastTouchY = event.getUILocation().y;

        // 阻止事件传播到ScrollView
        event.propagationStopped = true;

        console.log("FriendsRankUI: 开始滚动条操作");
    }

    /**
     * 滚动条触摸移动
     */
    private onScrollBarTouchMove(event: EventTouch): void {
        if (!this.isDraggingScrollBar || !this.friendsRankScrollView) {
            return;
        }

        // 阻止事件传播
        event.propagationStopped = true;

        // 获取当前触摸位置
        const currentTouchY = event.getUILocation().y;

        // 计算Y轴移动距离
        const deltaY = currentTouchY - this.lastTouchY;

        // 如果移动距离太小，忽略（避免抖动）
        if (Math.abs(deltaY) < 2) {
            return;
        }

        // 将移动距离转换为滚动进度变化
        // 使用更小的敏感度，让拖拽更精确
        const sensitivity = 0.5; // 调整这个值来控制拖拽敏感度
        const progressDelta = -deltaY / this.scrollBarHeight * sensitivity;

        // 获取当前滚动进度
        const maxScrollOffset = this.friendsRankScrollView.getMaxScrollOffset();
        const currentScrollOffset = this.friendsRankScrollView.getScrollOffset();

        let currentProgress = 0;
        if (maxScrollOffset.y > 0) {
            currentProgress = currentScrollOffset.y / maxScrollOffset.y;
        }

        // 计算新的滚动进度
        let newProgress = currentProgress + progressDelta;
        newProgress = Math.max(0, Math.min(1, newProgress));

        // 应用新的滚动进度
        this.friendsRankScrollView.scrollToPercentVertical(newProgress, 0);

        // 更新上次触摸位置
        this.lastTouchY = currentTouchY;

        // 调试信息（可以注释掉以减少日志输出）
        // console.log(`FriendsRankUI: 滚动进度: ${(newProgress * 100).toFixed(1)}%`);
    }

    /**
     * 滚动条触摸结束
     */
    private onScrollBarTouchEnd(event: EventTouch): void {
        if (!this.isDraggingScrollBar) {
            // 如果不是拖拽状态，说明是点击操作
            this.handleScrollBarClick(event);
        }

        this.isDraggingScrollBar = false;
        this.lastTouchY = 0;

        // 阻止事件传播
        event.propagationStopped = true;

        console.log("FriendsRankUI: 滚动条操作结束");
    }

    /**
     * 处理滚动条点击事件（点击滚动条直接跳转到对应位置）
     */
    private handleScrollBarClick(event: EventTouch): void {
        if (!this.friendsRankScrollView || !this.scrollBarNode) {
            return;
        }

        // 获取滚动条的UITransform
        const scrollBarTransform = this.scrollBarNode.getComponent(UITransform);
        if (!scrollBarTransform) {
            return;
        }

        // 获取点击位置
        const touchPos = event.getUILocation();

        // 将触摸位置转换为滚动条的本地坐标
        const touchLocalPos = new Vec3();
        scrollBarTransform.convertToNodeSpaceAR(new Vec3(touchPos.x, touchPos.y, 0), touchLocalPos);

        // 计算滚动条的范围
        const halfHeight = this.scrollBarHeight / 2;
        const maxY = halfHeight;
        const minY = -halfHeight;

        // 计算滚动进度 (0-1)，注意Y轴方向（上方为0，下方为1）
        let progress = (maxY - touchLocalPos.y) / (maxY - minY);
        progress = Math.max(0, Math.min(1, progress));

        // 设置ScrollView的滚动进度（带动画效果）
        this.friendsRankScrollView.scrollToPercentVertical(progress, 0.3);
        console.log(`FriendsRankUI: 点击滚动条，跳转到进度: ${(progress * 100).toFixed(1)}%`);
    }

    /**
     * 初始化 sharedCanvas 展示逻辑
     */
    private initSharedCanvasPresenter(): void {
        if (typeof wx === 'undefined' || !(wx as any).getOpenDataContext) {
            console.log('FriendsRankUI: 非微信环境或不支持开放数据域，跳过sharedCanvas');
            return;
        }

        // 确保有展示用的 Sprite
        if (!this.sharedCanvasSprite) {
            const spriteNode = new Node('SharedCanvasSprite');
            const sprite = spriteNode.addComponent(Sprite);

            // 优先将 sharedCanvas 放到 ScrollView 的 view 节点下，这样会被遮罩裁剪，且不会遮挡六个关卡按钮
            const viewNode = this.friendsRankScrollView?.content?.parent || this.node;
            viewNode.addChild(spriteNode);
            this.sharedCanvasSprite = sprite;

            // 尺寸与布局：向右移2像素，宽度变为1.2倍
            const targetTrans = viewNode.getComponent(UITransform);
            const uiTrans = spriteNode.addComponent(UITransform);
            const w = Math.max(100, Math.floor((targetTrans?.width || 540) - 20));
            const h = Math.max(100, Math.floor((targetTrans?.height || 560) - 20));
            // 宽度变为1.2倍
            const widerW = Math.floor(w * 1.2);
            uiTrans.setContentSize(widerW, h);
            // 向右偏移2像素
            spriteNode.setPosition(2, 0, 0);

            // 放到同级子节点的最底层，避免遮挡内容
            spriteNode.setSiblingIndex(0);
        }

        // 启动刷新定时器（这里使用 schedule，进入界面时刷新）
        this.startSharedCanvasRenderLoop();
    }

    /**
     * 向开放数据域请求绘制，并定时同步 sharedCanvas 到 Sprite 纹理
     */
    private startSharedCanvasRenderLoop(): void {
        try {
            const openCtx = (wx as any).getOpenDataContext();
            if (!openCtx) {
                console.warn('FriendsRankUI: 未获取到开放数据域上下文');
                return;
            }

            // 请求开放数据域绘制当前模式的排行榜
            const dpr = (wx as any).getSystemInfoSync?.()?.pixelRatio || 2;
            // 传递实际的sprite宽度给开放数据域
            const width = Math.floor(this.sharedCanvasSprite?.node.getComponent(UITransform)?.width || 720);
            const height = Math.floor(this.sharedCanvasSprite?.node.getComponent(UITransform)?.height || 1280);
            openCtx.postMessage({ type: 'DRAW_FRIENDS_RANK', mode: this.currentGameMode, width, height, dpr });

            // 从开放数据域获取 sharedCanvas 引用
            const sharedCanvas = (openCtx as any).canvas || (wx as any).getSharedCanvas?.();
            if (!sharedCanvas) {
                console.warn('FriendsRankUI: 未获取到 sharedCanvas 对象');
                return;
            }

            // 定时把 sharedCanvas 绘制到 SpriteFrame
            if (this._sharedCanvasTimer != null) {
                this.unschedule(this._sharedCanvasTimer as any);
                this._sharedCanvasTimer = null;
            }

            // 每0.3秒刷新一次，避免过高频率
            const refresh = () => {
                try {
                    // Cocos 3.x 可以用 ImageAsset + SpriteFrame 刷新
                    // @ts-ignore
                    const imageAsset = new ImageAsset(sharedCanvas);
                    const sf = SpriteFrame.createWithImage(imageAsset);
                    this.sharedCanvasSprite.spriteFrame = sf;
                    this._sharedCanvasTex = sf;
                } catch (e) {
                    console.warn('FriendsRankUI: 刷新sharedCanvas失败', e);
                }
            };

            refresh();

            this.schedule(refresh, 0.3);
            // 保存一个标记以便 onDestroy 时清理（schedule 无需保存 ID，unschedule(refresh) 即可）
            this._sharedCanvasTimer = 1 as any;

        } catch (e) {
            console.error('FriendsRankUI: 启动sharedCanvas渲染循环失败', e);
        }
    }

    onDisable(): void {
        // 关闭界面时停止刷新，避免资源浪费
        if (this._sharedCanvasTimer != null) {
            // 通过引用函数取消调度
            try {
                // 这里我们没有保存 refresh 引用，保险起见取消所有定时任务（或可保留引用进行精准取消）
                this.unscheduleAllCallbacks();
            } catch {}
            this._sharedCanvasTimer = null;
        }
    }

    /**
     * 外部调用：显示指定关卡的排行榜
     */
    public showLevelRanking(mode: GameMode): void {
        this.selectLevel(mode);
    }

    onEnable(): void {
        // 界面重新显示时，重启 sharedCanvas 渲染循环，确保根据当前模式刷新
        try {
            if (typeof wx !== 'undefined' && (wx as any).getOpenDataContext) {
                this.startSharedCanvasRenderLoop();
            }
        } catch {}
    }
}
