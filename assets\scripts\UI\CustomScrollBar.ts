import { _decorator, Component, Node, UITransform, EventTouch, Vec3, ScrollView, Sprite, Color } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 自定义滚动条组件
 * 提供更精确的拖拽控制
 */
@ccclass('CustomScrollBar')
export class CustomScrollBar extends Component {
    
    @property(ScrollView)
    targetScrollView: ScrollView = null;
    
    @property(Node)
    scrollBarTrack: Node = null; // 滚动条轨道
    
    @property(Node)
    scrollBarHandle: Node = null; // 滚动条把手
    
    @property
    handleMinHeight: number = 30; // 把手最小高度
    
    @property
    trackPadding: number = 5; // 轨道内边距

    private isDragging: boolean = false;
    private dragStartY: number = 0;
    private handleStartY: number = 0;
    private trackHeight: number = 0;
    private handleHeight: number = 0;
    private originalHandleColor: Color = null; // 保存Handle的原始颜色
    private lastLoggedProgress: number = null; // 上次记录的进度，用于减少日志输出

    onLoad() {
        this.setupScrollBar();
    }

    start() {
        // 延迟一帧确保所有组件都已初始化
        this.scheduleOnce(() => {
            this.initializeHandlePosition();
            this.updateScrollBar();
        }, 0);
    }

    onDestroy() {
        this.removeEvents();
    }

    /**
     * 设置滚动条
     */
    private setupScrollBar(): void {
        if (!this.scrollBarTrack || !this.scrollBarHandle || !this.targetScrollView) {
            console.warn("CustomScrollBar: 必要的组件未设置");
            return;
        }

        // 获取轨道高度
        const trackTransform = this.scrollBarTrack.getComponent(UITransform);
        if (trackTransform) {
            this.trackHeight = trackTransform.height - this.trackPadding * 2;
            console.log(`CustomScrollBar: 轨道实际高度=${trackTransform.height}, 有效高度=${this.trackHeight}, 内边距=${this.trackPadding}`);
        } else {
            console.warn("CustomScrollBar: 无法获取Track的UITransform");
            return;
        }

        // 保存Handle的原始颜色
        const handleSprite = this.scrollBarHandle.getComponent(Sprite);
        if (handleSprite) {
            this.originalHandleColor = handleSprite.color.clone();
            console.log(`CustomScrollBar: 保存Handle原始颜色 RGB(${this.originalHandleColor.r}, ${this.originalHandleColor.g}, ${this.originalHandleColor.b})`);
        }

        // 添加事件监听
        this.addEvents();

        // 监听ScrollView的滚动事件
        if (this.targetScrollView.node) {
            this.targetScrollView.node.on('scrolling', this.onScrollViewScrolling, this);
        }

        console.log("CustomScrollBar: 自定义滚动条已设置");
    }

    /**
     * 添加事件监听
     */
    private addEvents(): void {
        // 把手拖拽事件
        this.scrollBarHandle.on(Node.EventType.TOUCH_START, this.onHandleTouchStart, this);
        this.scrollBarHandle.on(Node.EventType.TOUCH_MOVE, this.onHandleTouchMove, this);
        this.scrollBarHandle.on(Node.EventType.TOUCH_END, this.onHandleTouchEnd, this);
        this.scrollBarHandle.on(Node.EventType.TOUCH_CANCEL, this.onHandleTouchEnd, this);

        // 轨道点击事件
        this.scrollBarTrack.on(Node.EventType.TOUCH_START, this.onTrackTouchStart, this);
    }

    /**
     * 移除事件监听
     */
    private removeEvents(): void {
        // 检查scrollBarHandle是否存在且有效
        if (this.scrollBarHandle && this.scrollBarHandle.isValid) {
            this.scrollBarHandle.off(Node.EventType.TOUCH_START, this.onHandleTouchStart, this);
            this.scrollBarHandle.off(Node.EventType.TOUCH_MOVE, this.onHandleTouchMove, this);
            this.scrollBarHandle.off(Node.EventType.TOUCH_END, this.onHandleTouchEnd, this);
            this.scrollBarHandle.off(Node.EventType.TOUCH_CANCEL, this.onHandleTouchEnd, this);
        }

        // 检查scrollBarTrack是否存在且有效
        if (this.scrollBarTrack && this.scrollBarTrack.isValid) {
            this.scrollBarTrack.off(Node.EventType.TOUCH_START, this.onTrackTouchStart, this);
        }

        // 检查targetScrollView是否存在且有效
        if (this.targetScrollView && this.targetScrollView.isValid && this.targetScrollView.node && this.targetScrollView.node.isValid) {
            this.targetScrollView.node.off('scrolling', this.onScrollViewScrolling, this);
        }
    }

    /**
     * 把手开始拖拽
     */
    private onHandleTouchStart(event: EventTouch): void {
        // 检查把手是否存在且有效
        if (!this.scrollBarHandle || !this.scrollBarHandle.isValid) {
            return;
        }

        this.isDragging = true;
        this.dragStartY = event.getUILocation().y;
        this.handleStartY = this.scrollBarHandle.position.y;

        // 改变把手颜色表示拖拽状态
        const handleSprite = this.scrollBarHandle.getComponent(Sprite);
        if (handleSprite) {
            const dragColor = new Color(150, 150, 150, 255);
            handleSprite.color = dragColor;
            console.log(`CustomScrollBar: 设置Handle拖拽颜色 RGB(${dragColor.r}, ${dragColor.g}, ${dragColor.b})`);
        } else {
            console.warn("CustomScrollBar: Handle节点没有Sprite组件，无法改变颜色");
        }

        event.propagationStopped = true;
        console.log("CustomScrollBar: 开始拖拽把手");
    }

    /**
     * 把手拖拽移动
     */
    private onHandleTouchMove(event: EventTouch): void {
        // 检查拖拽状态和必要组件
        if (!this.isDragging || !this.targetScrollView || !this.targetScrollView.isValid ||
            !this.scrollBarHandle || !this.scrollBarHandle.isValid) {
            return;
        }

        event.propagationStopped = true;

        const currentY = event.getUILocation().y;
        const deltaY = currentY - this.dragStartY;

        // 计算新的把手实际位置（包含偏移量）
        let newHandleActualY = this.handleStartY + deltaY;

        // 计算逻辑坐标系统的范围（不包含偏移量）
        const availableRange = this.trackHeight - this.handleHeight;
        const maxY = availableRange / 2;
        const minY = -availableRange / 2;

        // 将实际位置转换为逻辑位置（加回135偏移量）
        let newHandleLogicalY = newHandleActualY + 135;

        // 在逻辑坐标系统中限制范围
        newHandleLogicalY = Math.max(minY, Math.min(maxY, newHandleLogicalY));

        // 转换回实际位置（减去135偏移量）
        newHandleActualY = newHandleLogicalY - 135;

        console.log(`CustomScrollBar: 逻辑Y=${newHandleLogicalY.toFixed(1)}, 实际Y=${newHandleActualY.toFixed(1)}, 范围=[${minY.toFixed(1)}, ${maxY.toFixed(1)}]`);

        // 更新把手位置（使用实际坐标）
        this.scrollBarHandle.setPosition(this.scrollBarHandle.position.x, newHandleActualY, 0);

        // 拖拽Handle时恢复直观的逻辑：向上拖拽=更高排名，向下拖拽=更低排名
        // Handle在顶部(maxY)时progress=0（第一名），Handle在底部(minY)时progress=1（最后一名）
        const totalRange = maxY - minY;
        let progress = 0;
        if (totalRange > 0) {
            // 使用逻辑坐标计算进度
            progress = (newHandleLogicalY - minY) / totalRange;
        }
        const clampedProgress = Math.max(0, Math.min(1, progress));

        // 应用到ScrollView
        this.targetScrollView.scrollToPercentVertical(clampedProgress, 0);

        console.log(`CustomScrollBar: 拖拽Handle 逻辑Y=${newHandleLogicalY.toFixed(1)}, 实际Y=${newHandleActualY.toFixed(1)}, Progress=${(clampedProgress*100).toFixed(1)}%`);
    }

    /**
     * 把手拖拽结束
     */
    private onHandleTouchEnd(event: EventTouch): void {
        this.isDragging = false;

        // 检查把手是否存在且有效
        if (!this.scrollBarHandle || !this.scrollBarHandle.isValid) {
            return;
        }

        // 恢复把手到原始颜色
        const handleSprite = this.scrollBarHandle.getComponent(Sprite);
        if (handleSprite && this.originalHandleColor) {
            handleSprite.color = this.originalHandleColor.clone();
            console.log(`CustomScrollBar: 恢复Handle颜色到原始颜色 RGB(${this.originalHandleColor.r}, ${this.originalHandleColor.g}, ${this.originalHandleColor.b})`);
        } else if (handleSprite) {
            // 如果没有保存原始颜色，使用默认的浅灰色
            handleSprite.color = new Color(200, 200, 200, 255);
            console.log("CustomScrollBar: 恢复Handle颜色到默认浅灰色");
        }

        event.propagationStopped = true;
        console.log("CustomScrollBar: 结束拖拽把手");
    }

    /**
     * 轨道点击事件
     */
    private onTrackTouchStart(event: EventTouch): void {
        // 检查拖拽状态和必要组件
        if (this.isDragging || !this.targetScrollView || !this.targetScrollView.isValid ||
            !this.scrollBarTrack || !this.scrollBarTrack.isValid) {
            return;
        }

        // 获取点击位置
        const touchPos = event.getUILocation();
        const trackTransform = this.scrollBarTrack.getComponent(UITransform);
        if (!trackTransform) {
            return;
        }

        // 转换为轨道本地坐标
        const localPos = new Vec3();
        trackTransform.convertToNodeSpaceAR(new Vec3(touchPos.x, touchPos.y, 0), localPos);

        // 点击轨道时使用与拖拽一致的直观逻辑
        const maxY = (this.trackHeight - this.handleHeight) / 2;
        const minY = -(this.trackHeight - this.handleHeight) / 2;
        const totalRange = maxY - minY;

        let progress = 0;
        if (totalRange > 0) {
            // 点击轨道的直观逻辑：点击顶部时progress=0，点击底部时progress=1
            // localPos.y的坐标系中，正值在上方，负值在下方
            // 点击顶部(正值)应该对应progress=0（第一名），点击底部(负值)应该对应progress=1（最后一名）
            // 所以公式应该是：progress = (maxY - localPos.y) / totalRange
            progress = (maxY - localPos.y) / totalRange;
        }
        const clampedProgress = Math.max(0, Math.min(1, progress));

        // 应用到ScrollView（带动画）
        this.targetScrollView.scrollToPercentVertical(clampedProgress, 0.3);

        console.log(`CustomScrollBar: 点击轨道位置Y=${localPos.y.toFixed(1)}, 跳转到 ${(clampedProgress * 100).toFixed(1)}%`);
    }

    /**
     * ScrollView滚动时更新滚动条
     */
    private onScrollViewScrolling(): void {
        this.updateScrollBar();
    }

    /**
     * 更新滚动条显示
     */
    public updateScrollBar(): void {
        // 检查所有必要的组件是否存在且有效
        if (!this.targetScrollView || !this.targetScrollView.isValid ||
            !this.scrollBarHandle || !this.scrollBarHandle.isValid ||
            this.isDragging) {
            return;
        }

        // 获取滚动进度
        const maxScrollOffset = this.targetScrollView.getMaxScrollOffset();
        const currentScrollOffset = this.targetScrollView.getScrollOffset();

        let progress = 0;
        if (maxScrollOffset.y > 0) {
            progress = currentScrollOffset.y / maxScrollOffset.y;
        }

        // 计算把手位置范围
        const maxY = (this.trackHeight - this.handleHeight) / 2;
        const minY = -(this.trackHeight - this.handleHeight) / 2;

        // 修复滚动方向：ScrollView的progress和Handle位置需要反向映射
        // 当ScrollView滚动到顶部时(progress=0，显示第1名)，Handle应该在顶部(minY)
        // 当ScrollView滚动到底部时(progress=1，显示最后一名)，Handle应该在底部(maxY)
        // 但是ScrollView的坐标系统中，progress=0时currentScrollOffset.y=0（顶部）
        // progress=1时currentScrollOffset.y=maxScrollOffset.y（底部）
        // 所以需要反向映射：Handle位置 = maxY - progress * (maxY - minY)
        const handleY = maxY - progress * (maxY - minY);

        // 更新把手位置（保持-135偏移量以确保Handle在轨道内）
        this.scrollBarHandle.setPosition(this.scrollBarHandle.position.x, handleY - 135, 0);

        // 减少日志输出频率，只在进度变化较大时输出
        const progressPercent = Math.floor(progress * 10) * 10; // 按10%间隔输出
        if (!this.lastLoggedProgress || Math.abs(progressPercent - this.lastLoggedProgress) >= 10) {
            console.log(`CustomScrollBar: ScrollView进度=${(progress*100).toFixed(1)}%, Handle Y=${(handleY-135).toFixed(1)}, 当前偏移=${currentScrollOffset.y.toFixed(1)}, 最大偏移=${maxScrollOffset.y.toFixed(1)}`);
            this.lastLoggedProgress = progressPercent;
        }
    }

    /**
     * 设置把手高度（根据内容比例）
     */
    public updateHandleSize(): void {
        // 检查所有必要的组件是否存在且有效
        if (!this.targetScrollView || !this.targetScrollView.isValid ||
            !this.scrollBarHandle || !this.scrollBarHandle.isValid) {
            return;
        }

        const content = this.targetScrollView.content;
        const scrollViewTransform = this.targetScrollView.node?.getComponent(UITransform);

        if (!content || !content.isValid || !scrollViewTransform) {
            return;
        }

        const contentTransform = content.getComponent(UITransform);
        if (!contentTransform) {
            return;
        }

        // 计算把手高度比例
        const viewHeight = scrollViewTransform.height;
        const contentHeight = contentTransform.height;

        let handleHeightRatio = viewHeight / contentHeight;
        handleHeightRatio = Math.max(0.1, Math.min(1, handleHeightRatio)); // 限制在10%-100%之间

        // 设置把手高度
        this.handleHeight = Math.max(this.handleMinHeight, this.trackHeight * handleHeightRatio);

        const handleTransform = this.scrollBarHandle.getComponent(UITransform);
        if (handleTransform) {
            handleTransform.height = this.handleHeight;
        }

        console.log(`CustomScrollBar: 更新把手高度 ${this.handleHeight.toFixed(1)}, 轨道高度=${this.trackHeight}, 视图高度=${viewHeight}, 内容高度=${contentHeight}`);
    }

    /**
     * 初始化把手位置（确保在顶部对应第一名）
     */
    private initializeHandlePosition(): void {
        // 检查把手是否存在且有效
        if (!this.scrollBarHandle || !this.scrollBarHandle.isValid) {
            return;
        }

        // 更新把手高度
        this.updateHandleSize();

        // 将把手设置到顶部位置（对应第一名）
        const maxY = (this.trackHeight - this.handleHeight) / 2;
        const minY = -(this.trackHeight - this.handleHeight) / 2;

        // 把手初始位置在顶部：由于updateScrollBar使用反向映射，这里应该使用maxY
        // 当排行榜显示第1名时（ScrollView progress=0），Handle应该在顶部，对应maxY位置
        this.scrollBarHandle.setPosition(this.scrollBarHandle.position.x, maxY - 135, 0);

        console.log(`CustomScrollBar: 初始化把手位置到顶部 Y=${(maxY - 135).toFixed(1)}, 轨道范围: ${minY.toFixed(1)} 到 ${maxY.toFixed(1)}`);
    }
}
