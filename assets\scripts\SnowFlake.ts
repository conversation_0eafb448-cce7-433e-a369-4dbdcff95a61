import { _decorator, Component, Node, Vec3, AudioClip } from 'cc';
import { GameManager } from './GameManager';
import { Bird } from './Bird';
import { AudioMgr } from './AudioMgr';
const { ccclass, property } = _decorator;

/**
 * 雪花组件
 * 控制雪花的移动和与小鸟的碰撞检测
 */
@ccclass('SnowFlake')
export class SnowFlake extends Component {
    // 雪花移动速度
    private horizontalSpeed: number = 0;
    private verticalSpeed: number = 0;
    
    // 碰撞检测半径
    @property
    collisionRadius: number = 50;
    
    // 碰撞音效
    @property(AudioClip)
    collisionSound: AudioClip | null = null;
    
    // 小鸟节点引用
    private _bird: Node | null = null;
    
    // 是否已经被处理（防止重复处理）
    private _processed: boolean = false;

    start() {
        // 确保雪花是激活的
        this.node.active = true;
        
        // 获取小鸟节点引用
        const gameManager = GameManager.inst();
        if (gameManager && gameManager.bird) {
            this._bird = gameManager.bird.node;
        }
        
        console.log("雪花初始化完成: 位置=", this.node.position.x, this.node.position.y);
    }

    /**
     * 初始化雪花
     * @param horizontalSpeed 水平速度
     * @param verticalSpeed 垂直速度
     */
    init(horizontalSpeed: number, verticalSpeed: number) {
        this.horizontalSpeed = horizontalSpeed;
        this.verticalSpeed = verticalSpeed;
    }

    update(deltaTime: number) {
        // 如果已经被处理，停止更新
        if (this._processed) return;

        // 检查游戏是否暂停，暂停时不移动
        const gameManager = GameManager.inst();
        if (gameManager && gameManager.isPaused()) {
            return; // 暂停时不移动
        }

        // 移动雪花
        this.moveSnowFlake(deltaTime);

        // 检测是否离开屏幕
        if (this.checkOutOfBounds()) {
            return; // 如果已经销毁，直接返回
        }

        // 检测与小鸟的碰撞
        this.checkBirdCollision();
    }
    
    /**
     * 移动雪花
     */
    private moveSnowFlake(deltaTime: number) {
        // 移动雪花位置
        const currentPos = this.node.position;
        const newPos = new Vec3(
            currentPos.x - this.horizontalSpeed * deltaTime,
            currentPos.y - this.verticalSpeed * deltaTime,
            currentPos.z
        );
        this.node.setPosition(newPos);
    }
    
    /**
     * 检测与小鸟的碰撞
     */
    private checkBirdCollision() {
        if (!this._bird || !this._bird.isValid) return;
        
        // 获取世界坐标位置
        const birdPos = this._bird.getWorldPosition();
        const snowPos = this.node.getWorldPosition();
        
        // 计算距离
        const dx = birdPos.x - snowPos.x;
        const dy = birdPos.y - snowPos.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        // 如果距离小于碰撞半径，触发碰撞效果
        if (distance < this.collisionRadius) {
            console.log("小鸟碰到雪花！距离:", distance);
            this.onBirdCollision();
        }
    }
    
    /**
     * 处理小鸟碰撞
     */
    private onBirdCollision() {
        // 防止重复处理
        if (this._processed) {
            console.log("雪花已经被处理过，忽略重复碰撞");
            return;
        }
        
        this._processed = true;
        console.log("处理雪花碰撞...");
        
        // 播放碰撞音效
        this.playCollisionSound();
        
        // 使小鸟受到雪花影响
        this.affectBird();
        
        // 立即销毁雪花
        this.destroySnowFlakeImmediately();
    }
    
    /**
     * 播放碰撞音效
     */
    private playCollisionSound() {
        if (this.collisionSound) {
            AudioMgr.inst.playOneShot(this.collisionSound);
            console.log("播放雪花碰撞音效");
        } else {
            console.warn("雪花碰撞音效未设置");
        }
    }
    
    /**
     * 影响小鸟
     */
    private affectBird() {
        const gameManager = GameManager.inst();
        if (gameManager && gameManager.bird) {
            // 调用Bird组件的方法来降低跳跃力度
            gameManager.bird.applySnowEffect();
        }
    }
    
    /**
     * 检测是否离开屏幕
     * @returns 如果已经销毁返回true，否则返回false
     */
    private checkOutOfBounds(): boolean {
        const pos = this.node.position;
        
        // 如果雪花离开屏幕底部或左侧，销毁它
        if (pos.y < -800 || pos.x < -500) {
            this.destroySnowFlakeImmediately();
            return true;
        }
        
        return false;
    }
    
    /**
     * 立即销毁雪花（用于碰撞时）
     */
    private destroySnowFlakeImmediately() {
        // 防止重复销毁
        if (this._processed && this.node && this.node.isValid) {
            console.log("立即销毁雪花...");
            try {
                this.node.destroy();
                console.log("雪花已销毁");
            } catch (error) {
                console.error("销毁雪花时出错:", error);
                // 如果销毁失败，至少隐藏它
                this.node.active = false;
            }
        }
    }
    
    /**
     * 延迟销毁雪花（用于出界时）
     */
    private destroySnowFlake() {
        // 防止重复销毁
        if (this._processed) return;
        this._processed = true;
        
        console.log("销毁雪花...");
        
        // 延迟一帧再销毁，确保安全
        this.scheduleOnce(() => {
            if (this.node && this.node.isValid) {
                try {
                    this.node.destroy();
                    console.log("雪花已销毁");
                } catch (error) {
                    console.error("销毁雪花时出错:", error);
                    // 如果销毁失败，至少隐藏它
                    this.node.active = false;
                }
            }
        }, 0);
    }

    onDestroy() {
        // 取消所有调度器
        this.unscheduleAllCallbacks();

        // 清理引用
        this._bird = null;
        this.collisionSound = null;

        console.log("SnowFlake: 组件已销毁，资源已清理");
    }
}


