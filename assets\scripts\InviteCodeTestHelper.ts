import { _decorator, Component, Node, input, Input, EventKeyboard, KeyCode } from 'cc';
import { InviteCodeManager } from './InviteCodeManager';
const { ccclass, property } = _decorator;

/**
 * 邀请码测试辅助脚本
 * 用于测试时快速恢复邀请码输入功能
 * 
 * 使用方法：
 * 1. 将此脚本添加到任意节点上（建议添加到设置场景的根节点）
 * 2. 按 Fn 键恢复邀请码输入功能
 * 3. 测试完成后可以移除此脚本
 */
@ccclass('InviteCodeTestHelper')
export class InviteCodeTestHelper extends Component {
    
    @property(Node)
    inviteInputPanel: Node = null;
    
    private isListening: boolean = false;
    
    onLoad() {
        console.log("邀请码测试辅助脚本已加载");
        console.log("按 * 键可以恢复邀请码输入功能");
        
        // 自动查找邀请码输入面板
        this.findInviteInputPanel();
        
        // 开始监听键盘事件
        this.startKeyboardListener();
    }
    
    /**
     * 自动查找邀请码输入面板
     */
    private findInviteInputPanel(): void {
        if (this.inviteInputPanel) {
            console.log("邀请码输入面板已手动设置");
            return;
        }
        
        // 尝试自动查找
        const settingsUI = this.node.getComponentInChildren('SettingsUI');
        if (settingsUI) {
            // 通过SettingsUI组件查找
            const settingsUINode = settingsUI.node;
            this.inviteInputPanel = settingsUINode.getChildByName('InviteInputPanel');
            
            if (this.inviteInputPanel) {
                console.log("自动找到邀请码输入面板");
            }
        }
        
        // 如果还没找到，尝试通过名称查找
        if (!this.inviteInputPanel) {
            this.inviteInputPanel = this.findNodeByName(this.node, 'InviteInputPanel');
            if (this.inviteInputPanel) {
                console.log("通过名称查找到邀请码输入面板");
            }
        }
        
        if (!this.inviteInputPanel) {
            console.warn("未找到邀请码输入面板，请手动设置 inviteInputPanel 属性");
        }
    }
    
    /**
     * 递归查找指定名称的节点
     */
    private findNodeByName(parent: Node, name: string): Node | null {
        if (parent.name === name) {
            return parent;
        }
        
        for (let i = 0; i < parent.children.length; i++) {
            const result = this.findNodeByName(parent.children[i], name);
            if (result) {
                return result;
            }
        }
        
        return null;
    }
    
    /**
     * 开始监听键盘事件
     */
    private startKeyboardListener(): void {
        if (this.isListening) return;

        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        this.isListening = true;
        console.log("键盘监听已启动 - 请尝试按任意键测试");

        // 额外的调试信息
        console.log("如果键盘没有响应，请确保:");
        console.log("1. 浏览器窗口处于焦点状态");
        console.log("2. 没有其他输入框处于焦点状态");
        console.log("3. 尝试点击游戏画面后再按键");
    }
    
    /**
     * 停止监听键盘事件
     */
    private stopKeyboardListener(): void {
        if (!this.isListening) return;
        
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        this.isListening = false;
        console.log("键盘监听已停止");
    }
    
    /**
     * 键盘按下事件处理
     */
    private onKeyDown(event: EventKeyboard): void {
        // 调试：显示按下的键
        console.log(`按键检测: keyCode=${event.keyCode}, key="${String.fromCharCode(event.keyCode)}"`);

        // 检查是否按下了 * 键（星号键）
        // 尝试多种可能的键码
        if (//event.keyCode === KeyCode.ASTERISK ||
            //event.keyCode === KeyCode.NUMPAD_MULTIPLY ||
            //event.keyCode === 56 && event.shiftKey || // Shift + 8 = *
            event.keyCode === 106) { // 数字键盘的 *
            console.log("检测到 * 键按下，恢复邀请码输入功能");
            this.restoreInviteCodeInput();
        }

        // 临时添加更多测试键，方便调试
        if (event.keyCode === KeyCode.KEY_R) {
            console.log("检测到 R 键按下，恢复邀请码输入功能");
            this.restoreInviteCodeInput();
        }

        // 也可以使用其他键作为快捷键，比如 F2
        if (event.keyCode === KeyCode.F2) {
            console.log("检测到 F2 键按下，重置邀请码系统");
            this.resetInviteCodeSystem();
        }

        // F3 键显示当前状态
        if (event.keyCode === KeyCode.F3) {
            console.log("检测到 F3 键按下，显示邀请码系统状态");
            this.showInviteCodeStatus();
        }

        // 空格键也可以用来恢复（临时测试）
        if (event.keyCode === KeyCode.SPACE) {
            console.log("检测到空格键按下，恢复邀请码输入功能");
            this.restoreInviteCodeInput();
        }

        // 数字键0：完全重置并重新开始计时
        if (event.keyCode === KeyCode.DIGIT_0) {
            console.log("检测到数字键0按下，完全重置系统并重新开始计时");
            this.resetAndRestartTimer();
        }
    }
    
    /**
     * 恢复邀请码输入功能
     */
    private restoreInviteCodeInput(): void {
        if (!this.inviteInputPanel) {
            console.error("邀请码输入面板未找到，无法恢复");
            return;
        }
        
        // 重置邀请码使用状态
        localStorage.removeItem("HasUsedInviteCode");
        
        // 显示输入面板
        this.inviteInputPanel.active = true;
        
        console.log("邀请码输入功能已恢复！");
        console.log("现在可以重新输入邀请码进行测试");
    }
    
    /**
     * 完全重置邀请码系统
     */
    private resetInviteCodeSystem(): void {
        InviteCodeManager.resetInviteSystem();

        if (this.inviteInputPanel) {
            this.inviteInputPanel.active = true;
        }

        console.log("邀请码系统已完全重置！");
    }

    /**
     * 重置系统并重新开始计时
     */
    private resetAndRestartTimer(): void {
        // 完全清除所有数据
        localStorage.removeItem("PlayerInviteCode");
        localStorage.removeItem("FirstDayRegistration");
        localStorage.removeItem("HasUsedInviteCode");
        localStorage.removeItem("RegistrationDate");

        // 重新设置当前时间为注册时间
        const now = Date.now();
        localStorage.setItem("RegistrationDate", now.toString());

        // 显示输入面板
        if (this.inviteInputPanel) {
            this.inviteInputPanel.active = true;
        }

        console.log(`系统已重置，重新开始计时！注册时间: ${now}`);
        console.log("现在有900秒时间进行测试");

        // 显示当前状态
        this.showInviteCodeStatus();
    }
    
    /**
     * 显示邀请码系统状态
     */
    private showInviteCodeStatus(): void {
        const status = InviteCodeManager.getInviteSystemStatus();

        // 获取注册时间信息
        const registrationTimeStr = localStorage.getItem("RegistrationDate");
        let timeInfo = "未设置";
        if (registrationTimeStr) {
            const registrationTime = parseInt(registrationTimeStr);
            const now = Date.now();
            const timeDiff = now - registrationTime;
            const secondsPassed = Math.floor(timeDiff / 1000);
            const remainingSeconds = 900 - secondsPassed;
            timeInfo = `${secondsPassed}秒前注册 (${timeDiff < 900000 ? `还剩${remainingSeconds}秒` : '已超过900秒'})`;
        }

        console.log("=== 邀请码系统状态 ===");
        console.log("玩家邀请码:", status.playerCode);
        console.log("注册时间:", timeInfo);
        console.log("是否首次注册:", status.isFirstDay);
        console.log("是否已使用邀请码:", status.hasUsedCode);
        console.log("是否应该显示输入框:", status.shouldShowInput);
        console.log("输入面板是否激活:", this.inviteInputPanel ? this.inviteInputPanel.active : "未找到");
        console.log("=====================");
    }
    
    /**
     * 组件启用时
     */
    onEnable(): void {
        this.startKeyboardListener();
    }
    
    /**
     * 组件禁用时
     */
    onDisable(): void {
        this.stopKeyboardListener();
    }
    
    /**
     * 组件销毁时
     */
    onDestroy(): void {
        // 停止键盘监听
        this.stopKeyboardListener();

        // 取消所有调度器
        this.unscheduleAllCallbacks();

        // 清理节点引用
        this.inviteInputPanel = null;

        console.log("InviteCodeTestHelper: 组件已销毁，事件监听器已清理");
    }
    
    start() {
        // 显示使用说明
        console.log("=== 邀请码测试辅助脚本使用说明 ===");
        console.log("* 键: 恢复邀请码输入功能（只重置使用状态）");
        console.log("R 键: 恢复邀请码输入功能（临时测试键）");
        console.log("空格键: 恢复邀请码输入功能（临时测试键）");
        console.log("数字键0: 完全重置系统并重新开始900秒计时");
        console.log("F2 键: 完全重置邀请码系统");
        console.log("F3 键: 显示当前邀请码系统状态");
        console.log("================================");
        console.log("注意：请确保浏览器窗口处于焦点状态，并点击游戏画面后再按键");
        console.log("测试时间已改为900秒（15分钟），方便观察自动隐藏功能");
    }
}
