import { _decorator, Component } from 'cc';
import { GameData } from './GameData';
import { CloudDatabaseManager } from './Data/CloudDatabaseManager';
const { ccclass, property } = _decorator;

/**
 * 邀请码管理器
 * 负责生成、验证和管理玩家邀请码
 */
@ccclass('InviteCodeManager')
export class InviteCodeManager extends Component {
    
    // 存储键
    private static readonly PLAYER_INVITE_CODE = "PlayerInviteCode";
    private static readonly FIRST_DAY_REGISTRATION = "FirstDayRegistration";
    private static readonly HAS_USED_INVITE_CODE = "HasUsedInviteCode";
    private static readonly REGISTRATION_DATE = "RegistrationDate";
    private static readonly SIMULATED_PLAYERS = "SimulatedPlayers";
    
    // 邀请奖励金币数
    private static readonly INVITE_REWARD = 2000;

    // 模拟数据库中的有效邀请码（其他玩家的邀请码）
    private static readonly VALID_INVITE_CODES = [
        "AbC123",
        "XyZ789",
        "Test01",
        "Hello1",
        "Game99",
        "Play88",
        "Fun777",
        "Cool66",
        "Best55",
        "Win444",
        "Lucky3",
        "Star22",
        "Moon11",
        "Sun000",
        "Fire99"
    ];

    // 注释：模拟玩家数据已移除，现在从云数据库获取真实数据
    
    /**
     * 获取当前玩家的邀请码
     * 注意：邀请码应该在首次登录时通过云函数生成，这里只是获取已存在的邀请码
     */
    public static getPlayerInviteCode(): string {
        const inviteCode = localStorage.getItem(this.PLAYER_INVITE_CODE);
        if (!inviteCode) {
            console.warn("InviteCodeManager: 邀请码不存在，应该在首次登录时生成");
            return '';
        }
        return inviteCode;
    }

    /**
     * 设置当前玩家的邀请码
     * @param inviteCode 要设置的邀请码
     */
    public static setPlayerInviteCode(inviteCode: string): void {
        localStorage.setItem(this.PLAYER_INVITE_CODE, inviteCode);
        console.log(`设置邀请码: ${inviteCode}`);
    }
    
    /**
     * 生成唯一的邀请码
     * 格式：6位大小写字母和数字混合组合
     */
    private static generateInviteCode(): string {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        // 添加时间戳的后2位确保唯一性，但保持总长度为6位
        const timestamp = Date.now().toString();
        const timestampSuffix = timestamp.slice(-2);

        return result.substring(0, 4) + timestampSuffix;
    }
    
    /**
     * 检查是否是首次注册（一分钟内注册 - 测试用）
     */
    public static isFirstDayRegistration(): boolean {
        const registrationTimeStr = localStorage.getItem(this.REGISTRATION_DATE);
        const now = Date.now();

        if (!registrationTimeStr || registrationTimeStr === "null") {
            // 首次运行，设置注册时间戳
            localStorage.setItem(this.REGISTRATION_DATE, now.toString());
            localStorage.setItem(this.FIRST_DAY_REGISTRATION, "true");
            console.log(`首次注册，设置注册时间: ${now}`);
            return true;
        }

        // 检查是否在注册后24小时内
        const registrationTime = parseInt(registrationTimeStr);

        // 验证时间戳是否有效
        if (isNaN(registrationTime)) {
            console.log("注册时间戳无效，重新设置");
            localStorage.setItem(this.REGISTRATION_DATE, now.toString());
            return true;
        }

        const timeDiff = now - registrationTime;
        const isWithin900Seconds = timeDiff < 86400000; // 86400000毫秒 = 86400秒 = 24小时

        console.log(`注册时间检查: 注册时间=${registrationTime}, 当前时间=${now}, 已过去${Math.floor(timeDiff/1000)}秒, 是否在900秒内: ${isWithin900Seconds}`);

        return isWithin900Seconds;
    }
    
    /**
     * 检查玩家是否已经使用过邀请码
     */
    public static hasUsedInviteCode(): boolean {
        return localStorage.getItem(this.HAS_USED_INVITE_CODE) === "true";
    }
    
    /**
     * 验证并使用邀请码（异步版本）
     * @param inviteCode 输入的邀请码
     * @returns 是否成功使用
     */
    public static async useInviteCode(inviteCode: string): Promise<boolean> {
        // 检查是否已经使用过邀请码
        if (this.hasUsedInviteCode()) {
            console.log("已经使用过邀请码，无法重复使用");
            return false;
        }

        // 检查是否是自己的邀请码（区分大小写）
        const playerCode = this.getPlayerInviteCode();
        if (inviteCode === playerCode) {
            console.log("不能使用自己的邀请码");
            return false;
        }

        // 验证邀请码格式
        if (!this.validateInviteCodeFormat(inviteCode)) {
            console.log("邀请码格式不正确");
            return false;
        }

        // 验证邀请码是否在云数据库中存在
        const isValid = await this.isValidInviteCode(inviteCode);
        if (!isValid) {
            console.log(`邀请码 ${inviteCode} 不存在于数据库中`);
            return false;
        }

        // 标记已使用邀请码
        localStorage.setItem(this.HAS_USED_INVITE_CODE, "true");

        // 给当前玩家发放奖励
        GameData.addCoin(this.INVITE_REWARD);

        // 给邀请者发放奖励（从云数据库）
        await this.giveRewardToInviter(inviteCode);

        console.log(`成功使用邀请码 ${inviteCode}，获得 ${this.INVITE_REWARD} 金币奖励`);

        return true;
    }

    /**
     * 验证并使用邀请码（同步版本，用于兼容现有代码）
     * @param inviteCode 输入的邀请码
     * @returns 是否成功使用
     */
    public static useInviteCodeSync(inviteCode: string): boolean {
        // 异步调用，但不等待结果
        this.useInviteCode(inviteCode).then(result => {
            console.log(`邀请码使用结果: ${result}`);
        }).catch(error => {
            console.error('使用邀请码时出错:', error);
        });

        // 立即返回基本验证结果
        return this.validateInviteCodeFormat(inviteCode) &&
               !this.hasUsedInviteCode() &&
               inviteCode !== this.getPlayerInviteCode();
    }
    
    /**
     * 验证邀请码格式
     * @param inviteCode 邀请码
     * @returns 是否格式正确
     */
    private static validateInviteCodeFormat(inviteCode: string): boolean {
        // 检查长度
        if (inviteCode.length !== 6) {
            return false;
        }

        // 检查是否只包含大小写字母和数字（区分大小写）
        const regex = /^[A-Za-z0-9]+$/;
        return regex.test(inviteCode);
    }

    /**
     * 验证邀请码是否在云数据库中存在（本地数据库查询）
     * @param inviteCode 邀请码
     * @returns 是否为有效的邀请码
     */
    private static async isValidInviteCode(inviteCode: string): Promise<boolean> {
        try {
            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();

            // 直接查询云数据库中是否存在该邀请码（本地操作）
            if (typeof wx !== 'undefined' && wx.cloud && wx.cloud.database) {
                const db = wx.cloud.database();
                const result = await db.collection('players')
                    .where({
                        inviteCode: inviteCode
                    })
                    .count();

                if (result.errMsg === 'collection.count:ok') {
                    return result.total > 0;
                } else {
                    console.error('查询邀请码失败:', result);
                    return false;
                }
            } else {
                console.error('云数据库不可用');
                return false;
            }
        } catch (error) {
            console.error('验证邀请码时出错:', error);
            return false;
        }
    }

    /**
     * 给邀请者发放奖励（从云数据库）
     * @param inviteCode 被使用的邀请码
     */
    private static async giveRewardToInviter(inviteCode: string): Promise<void> {
        try {
            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();

            // 查找拥有该邀请码的玩家
            const players = await cloudDB.getPlayersData(1000, 0);
            const inviterPlayer = players.find(player => player.inviteCode === inviteCode);

            if (inviterPlayer) {
                // 注意：这里只是记录奖励，实际的金币更新需要在云函数中处理
                // 因为小程序端无法直接修改其他玩家的数据
                console.log(`邀请者 ${inviterPlayer.nickname}(${inviteCode}) 应获得 ${this.INVITE_REWARD} 金币奖励`);
                console.log(`当前邀请者金币: ${inviterPlayer.coins}`);

                // 调用云函数来更新邀请者的金币
                try {
                    if (typeof wx !== 'undefined' && wx.cloud && wx.cloud.callFunction) {
                        const result = await wx.cloud.callFunction({
                            name: 'updateInviterReward',
                            data: {
                                inviteCode: inviteCode,
                                reward: this.INVITE_REWARD
                            }
                        });

                        if (result.result && result.result.success) {
                            console.log(`云函数调用成功：邀请者 ${result.result.data.inviterNickname} 获得 ${result.result.data.reward} 金币`);
                        } else {
                            console.error('云函数调用失败:', result.result?.error || '未知错误');
                        }
                    } else {
                        console.warn('微信云函数不可用，无法给邀请者发放奖励');
                    }
                } catch (error) {
                    console.error('调用云函数时出错:', error);
                }

            } else {
                console.log(`未找到邀请码 ${inviteCode} 对应的玩家数据`);
            }
        } catch (error) {
            console.error('给邀请者发放奖励时出错:', error);
            // 云数据库不可用时不发放奖励
        }
    }

    /**
     * 给邀请者发放奖励（本地模拟，备用方案）
     * @param inviteCode 被使用的邀请码
     */
    private static giveRewardToInviterLocal(inviteCode: string): void {
        // 获取模拟玩家数据
        const simulatedPlayers = this.getSimulatedPlayers();

        if (simulatedPlayers[inviteCode]) {
            // 给邀请者增加金币
            simulatedPlayers[inviteCode].coins += this.INVITE_REWARD;

            // 保存更新后的数据
            this.saveSimulatedPlayers(simulatedPlayers);

            const playerName = simulatedPlayers[inviteCode].name;
            const newCoins = simulatedPlayers[inviteCode].coins;

            console.log(`邀请者 ${playerName}(${inviteCode}) 获得 ${this.INVITE_REWARD} 金币奖励，当前金币: ${newCoins}`);
        } else {
            console.log(`未找到邀请码 ${inviteCode} 对应的玩家数据`);
        }
    }

    /**
     * 获取模拟玩家数据（备用方案）
     */
    private static getSimulatedPlayers(): any {
        const savedData = localStorage.getItem(this.SIMULATED_PLAYERS);
        if (savedData) {
            try {
                return JSON.parse(savedData);
            } catch (e) {
                console.log("解析模拟玩家数据失败，使用默认数据");
            }
        }

        // 如果没有保存的数据，创建一些基本的模拟数据
        const defaultData = {
            "AbC123": { name: "玩家Alice", coins: 5000 },
            "XyZ789": { name: "玩家Bob", coins: 3200 },
            "Test01": { name: "玩家Charlie", coins: 4500 },
            "Hello1": { name: "玩家Diana", coins: 2800 },
            "Game99": { name: "玩家Eve", coins: 6100 }
        };
        this.saveSimulatedPlayers(defaultData);
        return defaultData;
    }

    /**
     * 保存模拟玩家数据
     */
    private static saveSimulatedPlayers(data: any): void {
        localStorage.setItem(this.SIMULATED_PLAYERS, JSON.stringify(data));
    }
    
    /**
     * 检查是否应该显示邀请码输入界面
     * 只有首次注册且未使用过邀请码的玩家才显示
     */
    public static shouldShowInviteInput(): boolean {
        return this.isFirstDayRegistration() && !this.hasUsedInviteCode();
    }
    
    /**
     * 获取邀请奖励金币数
     */
    public static getInviteReward(): number {
        return this.INVITE_REWARD;
    }
    
    /**
     * 重置邀请码系统（仅用于测试）
     */
    public static resetInviteSystem(): void {
        localStorage.removeItem(this.PLAYER_INVITE_CODE);
        localStorage.removeItem(this.FIRST_DAY_REGISTRATION);
        localStorage.removeItem(this.HAS_USED_INVITE_CODE);
        localStorage.removeItem(this.REGISTRATION_DATE);
        console.log("邀请码系统已重置");

        // 立即重新初始化，确保数据正确
        const now = Date.now();
        localStorage.setItem(this.REGISTRATION_DATE, now.toString());
        console.log(`重置后重新设置注册时间: ${now}`);
    }
    
    /**
     * 获取所有有效的邀请码列表（用于调试）
     */
    public static getValidInviteCodes(): string[] {
        return this.VALID_INVITE_CODES.slice(); // 返回副本，防止外部修改
    }

    /**
     * 获取模拟玩家信息（用于调试）
     */
    public static getSimulatedPlayerInfo(inviteCode: string): any {
        const players = this.getSimulatedPlayers();
        return players[inviteCode] || null;
    }

    /**
     * 获取所有模拟玩家信息（用于调试）
     */
    public static getAllSimulatedPlayers(): any {
        return this.getSimulatedPlayers();
    }

    /**
     * 重置模拟玩家数据到初始状态
     */
    public static resetSimulatedPlayers(): void {
        const defaultData = JSON.parse(JSON.stringify(this.SIMULATED_PLAYER_DATA));
        this.saveSimulatedPlayers(defaultData);
        console.log("模拟玩家数据已重置到初始状态");
    }

    /**
     * 检查指定邀请码是否有效（用于调试）
     */
    public static checkInviteCodeValidity(inviteCode: string): {
        isValidFormat: boolean,
        isInDatabase: boolean,
        isOwnCode: boolean,
        canUse: boolean
    } {
        const isValidFormat = this.validateInviteCodeFormat(inviteCode);
        const isInDatabase = this.isValidInviteCode(inviteCode);
        const isOwnCode = inviteCode === this.getPlayerInviteCode();
        const canUse = isValidFormat && isInDatabase && !isOwnCode && !this.hasUsedInviteCode();

        return {
            isValidFormat,
            isInDatabase,
            isOwnCode,
            canUse
        };
    }

    /**
     * 获取邀请码系统状态（用于调试）
     */
    public static getInviteSystemStatus(): {
        playerCode: string,
        isFirstDay: boolean,
        hasUsedCode: boolean,
        shouldShowInput: boolean,
        validCodesCount: number
    } {
        return {
            playerCode: this.getPlayerInviteCode(),
            isFirstDay: this.isFirstDayRegistration(),
            hasUsedCode: this.hasUsedInviteCode(),
            shouldShowInput: this.shouldShowInviteInput(),
            validCodesCount: this.VALID_INVITE_CODES.length
        };
    }
}
