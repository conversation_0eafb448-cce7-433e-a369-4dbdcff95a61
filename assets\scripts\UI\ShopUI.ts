import { _decorator, Component, Node, AudioClip, asset<PERSON>anager, director } from 'cc';
import { AudioMgr } from '../AudioMgr';
const { ccclass, property } = _decorator;

@ccclass('ShopUI')
export class ShopUI extends Component {
    @property(Node)
    characterPanel: Node = null;

    @property(Node)
    itemPanel: Node = null;

    @property(Node)
    backgroundPanel: Node = null;

    // 重新使用直接引用，但确保在运行时正确处理
    @property(AudioClip)
    bgAudio: AudioClip = null;

    onLoad() {
        console.log("ShopUI onLoad");

        // 先停止任何正在播放的音乐
        AudioMgr.inst.stop();

        // 检查bgAudio是否已设置
        if (this.bgAudio) {
            console.log("bgAudio已设置，准备播放商店BGM");
            // 确保在场景加载时立即播放商店BGM
            this.playShopBGM();
        } else {
            // 尝试在运行时获取bgAudio（使用主菜单的BGM）
            this.tryGetBgAudio();
        }
    }

    start() {
        // 初始显示角色面板，隐藏其他面板
        this.showCharacterPanel();
    }

    // 尝试在运行时获取bgAudio
    tryGetBgAudio() {
        // 使用主菜单的BGM作为商店BGM
        const HOME_BGM_UUID = "2d384416-f267-40be-9fb8-f091110d84c6";

        try {
            console.log("尝试通过UUID加载商店BGM:", HOME_BGM_UUID);
            assetManager.loadAny({uuid: HOME_BGM_UUID}, (err, asset) => {
                if (err) {
                    console.error("通过UUID加载商店BGM失败:", err);
                    return;
                }

                console.log("通过UUID加载商店BGM成功");
                this.bgAudio = asset as AudioClip;

                // 加载成功后立即播放
                this.playShopBGM();
            });
        } catch (error) {
            console.error("获取bgAudio失败:", error);
        }
    }

    // 播放商店BGM的方法
    playShopBGM() {
        console.log("尝试播放商店BGM");

        // 先停止任何正在播放的音乐
        AudioMgr.inst.stop();

        // 检查bgAudio是否已设置
        if (this.bgAudio) {
            // 延迟一帧再播放，确保音频系统已准备好
            this.scheduleOnce(() => {
                console.log("播放商店BGM");
                AudioMgr.inst.play(this.bgAudio, 0.1);
            }, 0);
        } else {
            console.error("无法播放商店BGM：bgAudio未设置");
        }
    }

    // 显示角色面板
    showCharacterPanel() {
        console.log("显示角色面板");
        this.characterPanel.active = true;
        this.itemPanel.active = false;
        this.backgroundPanel.active = false;
    }

    // 显示道具面板
    showItemPanel() {
        console.log("显示道具面板");
        this.characterPanel.active = false;
        this.itemPanel.active = true;
        this.backgroundPanel.active = false;
    }

    // 显示背景面板
    showBackgroundPanel() {
        console.log("显示背景面板");
        this.characterPanel.active = false;
        this.itemPanel.active = false;
        this.backgroundPanel.active = true;
    }

    // 点击角色按钮
    onCharacterButtonClick() {
        this.showCharacterPanel();
    }

    // 点击道具按钮
    onItemButtonClick() {
        this.showItemPanel();
    }

    // 点击背景按钮
    onBackgroundButtonClick() {
        this.showBackgroundPanel();
    }

    // 点击返回主菜单按钮
    onHomeButtonClick() {
        console.log("ShopUI: 返回主菜单");

        // 🔧 修复：设置场景切换标记，防止触发云端数据同步
        localStorage.setItem('scene_transition_flag', 'shop_to_home');

        // 不再停止音乐，让主菜单场景自己处理BGM的播放
        // 注意：我们不再调用AudioMgr.inst.stop()

        // 加载Home场景
        director.loadScene('Home');
    }
}
