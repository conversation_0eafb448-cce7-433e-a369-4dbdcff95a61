@echo off
echo 正在复制cloudfunctions文件夹到构建输出目录...

if not exist "cloudfunctions" (
    echo 错误：找不到cloudfunctions文件夹
    pause
    exit /b 1
)

if not exist "build\wechatgame" (
    echo 错误：找不到构建输出目录 build\wechatgame
    echo 请先在Cocos Creator中构建微信小游戏项目
    pause
    exit /b 1
)

xcopy "cloudfunctions" "build\wechatgame\cloudfunctions" /E /I /Y

if %errorlevel% equ 0 (
    echo ✅ cloudfunctions文件夹复制成功！
    echo 现在可以在微信开发者工具中打开 build\wechatgame 目录
) else (
    echo ❌ 复制失败，错误代码：%errorlevel%
)

pause
