import { _decorator, Component, Node, Button, Color, Sprite, CCBoolean } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 透明遮罩管理器
 * 用于管理UI面板的透明遮罩，阻止下层UI的交互
 */
@ccclass('MaskManager')
export class MaskManager extends Component {
    @property(Node)
    targetPanel: Node = null; // 目标面板，当面板显示时显示遮罩

    @property(Color)
    maskColor: Color = new Color(0, 0, 0, 100); // 遮罩颜色，默认半透明黑色

    @property(CCBoolean)
    clickToClose: boolean = true; // 是否允许点击遮罩关闭面板

    private sprite: Sprite = null;
    private button: Button = null;

    onLoad() {
        // 获取Sprite组件
        this.sprite = this.getComponent(Sprite);
        if (!this.sprite) {
            console.warn("MaskManager: 未找到Sprite组件");
        }

        // 获取Button组件
        this.button = this.getComponent(Button);
        if (!this.button && this.clickToClose) {
            console.warn("MaskManager: 未找到Button组件，无法响应点击事件");
        }

        // 设置遮罩颜色
        this.setMaskColor(this.maskColor);

        // 初始状态隐藏遮罩
        this.hideMask();
    }

    start() {
        // 监听按钮点击事件
        if (this.button && this.clickToClose) {
            this.button.node.on(Button.EventType.CLICK, this.onMaskClick, this);
        }
    }

    /**
     * 显示遮罩
     */
    public showMask() {
        this.node.active = true;
        console.log("MaskManager: 显示遮罩");
    }

    /**
     * 隐藏遮罩
     */
    public hideMask() {
        this.node.active = false;
        console.log("MaskManager: 隐藏遮罩");
    }

    /**
     * 设置遮罩颜色
     * @param color 颜色值
     */
    public setMaskColor(color: Color) {
        if (this.sprite) {
            this.sprite.color = color;
        }
        this.maskColor = color;
    }

    /**
     * 设置是否允许点击关闭
     * @param enabled 是否启用
     */
    public setClickToClose(enabled: boolean) {
        this.clickToClose = enabled;
        
        if (this.button) {
            if (enabled) {
                this.button.node.on(Button.EventType.CLICK, this.onMaskClick, this);
            } else {
                this.button.node.off(Button.EventType.CLICK, this.onMaskClick, this);
            }
        }
    }

    /**
     * 点击遮罩事件
     */
    private onMaskClick() {
        if (!this.clickToClose) {
            return;
        }

        console.log("MaskManager: 点击遮罩");
        
        // 发送自定义事件，通知父节点处理关闭逻辑
        this.node.emit('mask-clicked');
        
        // 如果设置了目标面板，直接隐藏目标面板和遮罩
        if (this.targetPanel) {
            this.targetPanel.active = false;
            this.hideMask();
        }
    }

    /**
     * 根据目标面板的状态自动显示/隐藏遮罩
     */
    public updateMaskVisibility() {
        if (this.targetPanel) {
            if (this.targetPanel.active) {
                this.showMask();
            } else {
                this.hideMask();
            }
        }
    }

    onDestroy() {
        // 清理事件监听
        if (this.button) {
            this.button.node.off(Button.EventType.CLICK, this.onMaskClick, this);
        }
    }
}
