{"__version__": "1.0.8", "modules": {"cache": {"base": {"_value": true}, "graphcis": {"_value": true}, "gfx-webgl": {"_value": true}, "gfx-webgl2": {"_value": false}, "animation": {"_value": true}, "skeletal-animation": {"_value": false}, "3d": {"_value": false}, "2d": {"_value": true}, "xr": {"_value": false}, "ui": {"_value": true}, "particle": {"_value": false}, "physics": {"_value": false, "_option": "physics-ammo"}, "physics-ammo": {"_value": false}, "physics-cannon": {"_value": false}, "physics-physx": {"_value": false}, "physics-builtin": {"_value": false}, "physics-2d": {"_value": true, "_option": "physics-2d-box2d"}, "physics-2d-box2d": {"_value": false}, "physics-2d-builtin": {"_value": false}, "intersection-2d": {"_value": true}, "primitive": {"_value": false}, "profiler": {"_value": false}, "occlusion-query": {"_value": false}, "geometry-renderer": {"_value": false}, "debug-renderer": {"_value": false}, "particle-2d": {"_value": true}, "audio": {"_value": true}, "video": {"_value": false}, "webview": {"_value": false}, "tween": {"_value": true}, "websocket": {"_value": false}, "websocket-server": {"_value": false}, "terrain": {"_value": false}, "light-probe": {"_value": false}, "tiled-map": {"_value": false}, "spine": {"_value": false}, "dragon-bones": {"_value": false}, "marionette": {"_value": false}, "procedural-animation": {"_value": false}, "custom-pipeline": {"_value": false}}, "includeModules": ["2d", "animation", "audio", "base", "gfx-webgl", "intersection-2d", "particle-2d", "physics-2d-box2d", "tween", "ui"], "noDeprecatedFeatures": {"value": false, "version": ""}, "flags": {}}}