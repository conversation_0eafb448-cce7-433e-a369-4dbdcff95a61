import { _decorator, Component } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 修复总结组件
 * 用于显示和验证所有修复内容
 */
@ccclass('FixSummary')
export class FixSummary extends Component {

    start() {
        this.displayFixSummary();
        this.exposeValidationMethods();
    }

    /**
     * 显示修复总结
     */
    private displayFixSummary() {
        console.log("=== 微信小游戏修复总结 ===");
        console.log("");
        console.log("🔧 修复的问题:");
        console.log("1. 体力值显示为NaN的问题");
        console.log("2. 体力恢复时间显示为NaN:NaN的问题");
        console.log("3. 复制邀请码时的 'i.select is not a function' 错误");
        console.log("");
        console.log("📁 修改的文件:");
        console.log("- assets/scripts/EnergyManager.ts (修改)");
        console.log("- assets/scripts/EnergyDisplay.ts (修改)");
        console.log("- assets/scripts/GameManager.ts (修改)");
        console.log("- assets/scripts/UI/SettingsUI.ts (修改)");
        console.log("");
        console.log("📁 新增的文件:");
        console.log("- assets/scripts/Utils/WeChatGameStorage.ts");
        console.log("- assets/scripts/WeChatGameCompatibility.ts");
        console.log("- assets/scripts/EnergyDebugHelper.ts");
        console.log("");
        console.log("🛠️ 主要修复内容:");
        console.log("1. 创建了安全的存储工具类 WeChatGameStorage");
        console.log("2. 增强了数字解析的安全性，防止NaN值");
        console.log("3. 添加了微信小游戏剪贴板API支持");
        console.log("4. 修复了textArea.select()方法的兼容性问题");
        console.log("5. 添加了全面的错误处理和日志记录");
        console.log("6. 提供了调试工具和兼容性检查");
        console.log("");
        console.log("🎮 微信小游戏特殊处理:");
        console.log("- 使用wx.setClipboardData进行剪贴板操作");
        console.log("- 支持wx存储API作为localStorage的fallback");
        console.log("- 添加了textArea.select()的polyfill");
        console.log("");
        console.log("🔍 可用的调试命令:");
        console.log("- window.energyDebug.checkEnergyStatus() - 检查体力系统");
        console.log("- window.energyDebug.resetEnergySystem() - 重置体力系统");
        console.log("- window.wechatCompatibility.checkCompatibility() - 兼容性检查");
        console.log("");
        console.log("=== 修复完成 ===");
    }

    /**
     * 暴露验证方法
     */
    private exposeValidationMethods() {
        if (typeof window !== 'undefined') {
            (window as any).validateFixes = {
                // 验证体力系统修复
                validateEnergySystem: () => {
                    return this.validateEnergySystem();
                },
                
                // 验证复制功能修复
                validateCopyFunction: () => {
                    return this.validateCopyFunction();
                },
                
                // 验证存储系统修复
                validateStorageSystem: () => {
                    return this.validateStorageSystem();
                },
                
                // 运行所有验证
                runAllValidations: () => {
                    return this.runAllValidations();
                }
            };
            
            console.log("验证方法已暴露到 window.validateFixes");
        }
    }

    /**
     * 验证体力系统修复
     */
    private validateEnergySystem(): boolean {
        console.log("验证体力系统修复...");
        
        try {
            // 检查EnergyManager是否存在
            const EnergyManager = require('./EnergyManager').EnergyManager;
            if (!EnergyManager) {
                console.error("❌ EnergyManager类不存在");
                return false;
            }
            
            // 检查WeChatGameStorage是否存在
            const WeChatGameStorage = require('./Utils/WeChatGameStorage').WeChatGameStorage;
            if (!WeChatGameStorage) {
                console.error("❌ WeChatGameStorage类不存在");
                return false;
            }
            
            // 测试安全的数字获取
            const testNumber = WeChatGameStorage.getNumber("test_key", 42);
            if (isNaN(testNumber)) {
                console.error("❌ WeChatGameStorage.getNumber返回NaN");
                return false;
            }
            
            console.log("✅ 体力系统修复验证通过");
            return true;
        } catch (error) {
            console.error("❌ 体力系统验证失败:", error);
            return false;
        }
    }

    /**
     * 验证复制功能修复
     */
    private validateCopyFunction(): boolean {
        console.log("验证复制功能修复...");
        
        try {
            // 检查微信API是否可用
            const hasWeChatAPI = typeof wx !== 'undefined' && typeof wx.setClipboardData === 'function';
            
            // 检查标准剪贴板API
            const hasStandardAPI = !!(navigator.clipboard && window.isSecureContext);
            
            // 检查execCommand
            const hasExecCommand = typeof document.execCommand === 'function';
            
            if (!hasWeChatAPI && !hasStandardAPI && !hasExecCommand) {
                console.warn("⚠️ 没有可用的剪贴板API");
                return false;
            }
            
            // 测试textArea创建
            const testArea = document.createElement('textarea');
            if (!testArea) {
                console.error("❌ 无法创建textarea元素");
                return false;
            }
            
            console.log("✅ 复制功能修复验证通过");
            console.log(`- 微信API: ${hasWeChatAPI ? '可用' : '不可用'}`);
            console.log(`- 标准API: ${hasStandardAPI ? '可用' : '不可用'}`);
            console.log(`- execCommand: ${hasExecCommand ? '可用' : '不可用'}`);
            return true;
        } catch (error) {
            console.error("❌ 复制功能验证失败:", error);
            return false;
        }
    }

    /**
     * 验证存储系统修复
     */
    private validateStorageSystem(): boolean {
        console.log("验证存储系统修复...");
        
        try {
            // 测试localStorage基本功能
            const testKey = '__validation_test__';
            const testValue = 'test_123';
            
            localStorage.setItem(testKey, testValue);
            const retrieved = localStorage.getItem(testKey);
            localStorage.removeItem(testKey);
            
            if (retrieved !== testValue) {
                console.error("❌ localStorage基本功能异常");
                return false;
            }
            
            // 测试数字存储和解析
            const numberKey = '__number_validation__';
            const numberValue = 456;
            
            localStorage.setItem(numberKey, numberValue.toString());
            const retrievedNumber = localStorage.getItem(numberKey);
            const parsedNumber = parseInt(retrievedNumber || "");
            localStorage.removeItem(numberKey);
            
            if (isNaN(parsedNumber) || parsedNumber !== numberValue) {
                console.error("❌ localStorage数字解析异常");
                return false;
            }
            
            console.log("✅ 存储系统修复验证通过");
            return true;
        } catch (error) {
            console.error("❌ 存储系统验证失败:", error);
            return false;
        }
    }

    /**
     * 运行所有验证
     */
    private runAllValidations(): { [key: string]: boolean } {
        console.log("=== 运行所有修复验证 ===");
        
        const results = {
            energySystem: this.validateEnergySystem(),
            copyFunction: this.validateCopyFunction(),
            storageSystem: this.validateStorageSystem()
        };
        
        const allPassed = Object.values(results).every(result => result);
        
        console.log("");
        console.log("验证结果:");
        console.log(`- 体力系统: ${results.energySystem ? '✅ 通过' : '❌ 失败'}`);
        console.log(`- 复制功能: ${results.copyFunction ? '✅ 通过' : '❌ 失败'}`);
        console.log(`- 存储系统: ${results.storageSystem ? '✅ 通过' : '❌ 失败'}`);
        console.log("");
        console.log(`总体结果: ${allPassed ? '✅ 所有修复验证通过' : '❌ 部分验证失败'}`);
        
        return results;
    }
}
