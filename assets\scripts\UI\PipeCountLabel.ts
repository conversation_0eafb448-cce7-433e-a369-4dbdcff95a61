import { _decorator, Component, Label, Node } from 'cc';
import { GameData, BirdType } from '../GameData';
import { AlbatrossInvincibilityManager } from '../AlbatrossInvincibilityManager';
const { ccclass, property } = _decorator;

/**
 * 管道计数标签组件
 * 用于显示信天翁的管道通过计数（0-14）
 */
@ccclass('PipeCountLabel')
export class PipeCountLabel extends Component {
    
    @property(Label)
    label: Label = null;
    
    start() {
        // 确保Label组件已设置
        if (!this.label) {
            this.label = this.getComponent(Label);
            if (!this.label) {
                console.error("PipeCountLabel错误: 未找到Label组件!");
                return;
            }
        }
        
        // 初始化显示
        this.updateDisplay();
    }
    
    update() {
        // 定期更新显示（确保与管理器同步）
        this.updateDisplay();
    }
    
    /**
     * 更新管道计数显示
     */
    public updateDisplay(): void {
        const selectedBirdType = GameData.getSelectedBirdType();
        
        // 只有选择信天翁时才显示
        if (selectedBirdType === BirdType.ALBATROSS) {
            this.showLabel();
            
            const invincibilityManager = AlbatrossInvincibilityManager.getInstance();
            if (invincibilityManager && this.label) {
                // 显示逻辑现在完全由AlbatrossInvincibilityManager处理
                // 包括正常计数和无敌倒计时
                if (invincibilityManager.isInvincible()) {
                    // 无敌状态：显示倒计时
                    const remainingSeconds = Math.ceil(invincibilityManager.getRemainingInvincibilityTime());
                    this.label.string = remainingSeconds.toString();
                } else {
                    // 正常状态：显示管道计数
                    const currentCount = invincibilityManager.getCurrentPipeCount();
                    this.label.string = `${currentCount}/15`;
                }
            }
        } else {
            this.hideLabel();
        }
    }
    
    /**
     * 显示标签
     */
    private showLabel(): void {
        this.node.active = true;
    }

    /**
     * 隐藏标签
     */
    private hideLabel(): void {
        this.node.active = false;
    }
    
    /**
     * 强制更新显示（外部调用）
     */
    public forceUpdate(): void {
        this.updateDisplay();
    }
}
