import { _decorator, Component, Node, tween, Vec3, Color, Sprite } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 信天翁无敌特效组件
 * 提供闪烁、发光等视觉效果来表示无敌状态
 */
@ccclass('InvincibilityEffect')
export class InvincibilityEffect extends Component {
    
    @property(Node)
    targetNode: Node = null; // 要应用特效的目标节点（通常是小鸟节点）
    
    @property
    blinkDuration: number = 0.1; // 闪烁间隔时间
    
    @property
    glowIntensity: number = 1; // 发光强度（缩放倍数，1.0表示无缩放变化，大于1.0会有大小变换）
    
    private _originalScale: Vec3 = new Vec3();
    private _originalColor: Color = new Color();
    private _isEffectActive: boolean = false;
    private _blinkTween: any = null;
    private _glowTween: any = null;
    
    start() {
        if (this.targetNode) {
            // 保存原始缩放和颜色
            this._originalScale = this.targetNode.scale.clone();
            const sprite = this.targetNode.getComponent(Sprite);
            if (sprite) {
                this._originalColor = sprite.color.clone();
            }
        }
        
        // 初始状态下隐藏特效
        this.node.active = false;
    }
    
    /**
     * 开始无敌特效
     */
    public startEffect(): void {
        if (this._isEffectActive) {
            return;
        }

        this._isEffectActive = true;
        this.node.active = true;

        console.log("开始信天翁无敌特效");

        // 开始闪烁效果
        this.startBlinkEffect();

        // 完全禁用发光效果（大小变换），只保留闪烁效果
        // this.startGlowEffect();
        console.log("已禁用大小变换效果，只保留闪烁效果");
    }
    
    /**
     * 停止无敌特效
     */
    public stopEffect(): void {
        if (!this._isEffectActive) {
            return;
        }
        
        this._isEffectActive = false;
        this.node.active = false;
        
        console.log("停止信天翁无敌特效");
        
        // 停止所有缓动
        this.stopAllTweens();
        
        // 恢复原始状态
        this.restoreOriginalState();
    }
    
    /**
     * 开始闪烁效果
     */
    private startBlinkEffect(): void {
        if (!this.targetNode) return;
        
        const sprite = this.targetNode.getComponent(Sprite);
        if (!sprite) return;
        
        // 创建闪烁缓动
        this._blinkTween = tween(sprite)
            .to(this.blinkDuration, { color: new Color(255, 255, 255, 100) }) // 变透明
            .to(this.blinkDuration, { color: this._originalColor }) // 恢复原色
            .union()
            .repeatForever()
            .start();
    }
    
    /**
     * 开始发光效果
     */
    private startGlowEffect(): void {
        if (!this.targetNode) return;

        // 如果发光强度接近1.0，跳过缩放动画（避免无意义的抖动）
        if (Math.abs(this.glowIntensity - 1.0) < 0.01) {
            console.log("发光强度接近1.0，跳过缩放动画");
            return;
        }

        // 创建缩放发光效果
        this._glowTween = tween(this.targetNode)
            .to(0.3, { scale: this._originalScale.clone().multiplyScalar(this.glowIntensity) })
            .to(0.3, { scale: this._originalScale })
            .union()
            .repeatForever()
            .start();
    }
    
    /**
     * 停止所有缓动
     */
    private stopAllTweens(): void {
        if (this._blinkTween) {
            this._blinkTween.stop();
            this._blinkTween = null;
        }

        // 发光缓动已禁用，但保留代码结构以防将来需要
        if (this._glowTween) {
            this._glowTween.stop();
            this._glowTween = null;
        }
    }
    
    /**
     * 恢复原始状态
     */
    private restoreOriginalState(): void {
        if (!this.targetNode) return;
        
        // 恢复原始缩放
        this.targetNode.scale = this._originalScale.clone();
        
        // 恢复原始颜色
        const sprite = this.targetNode.getComponent(Sprite);
        if (sprite) {
            sprite.color = this._originalColor.clone();
        }
    }
    
    onDestroy() {
        this.stopAllTweens();
    }
}
