import { _decorator, Component, Node, input, Input, KeyCode } from 'cc';
import { GameManager } from './GameManager';
const { ccclass, property } = _decorator;

/**
 * 暂停功能测试脚本
 * 可以通过键盘P键来测试暂停/继续功能
 */
@ccclass('PauseTest')
export class PauseTest extends Component {

    protected onLoad(): void {
        // 监听键盘事件
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    protected onDestroy(): void {
        // 清理事件监听
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    private onKeyDown(event: any): void {
        const gameManager = GameManager.inst();
        if (!gameManager) return;

        switch (event.keyCode) {
            case KeyCode.KEY_P:
                // P键切换暂停/继续
                if (gameManager.isPaused()) {
                    console.log("PauseTest: 通过P键继续游戏");
                    gameManager.resumeGame();
                } else {
                    console.log("PauseTest: 通过P键暂停游戏");
                    gameManager.pauseGame();
                }
                break;
        }
    }
}
