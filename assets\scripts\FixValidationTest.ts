import { _decorator, Component, Node } from 'cc';
import { GameData } from './GameData';
import { EnergyManager } from './EnergyManager';
const { ccclass, property } = _decorator;

/**
 * 修复验证测试脚本
 * 用于验证银牌铜牌显示和体力消耗修复是否有效
 */
@ccclass('FixValidationTest')
export class FixValidationTest extends Component {

    start() {
        console.log("=== 修复验证测试开始 ===");
        this.scheduleOnce(() => {
            this.runValidationTests();
        }, 1.0); // 延迟1秒执行，确保其他组件初始化完成
    }

    /**
     * 运行所有验证测试
     */
    private runValidationTests() {
        this.testMedalDisplayFix();
        this.testEnergyConsumptionFix();
        console.log("=== 修复验证测试完成 ===");
    }

    /**
     * 测试奖牌显示修复
     */
    private testMedalDisplayFix() {
        console.log("\n--- 验证奖牌显示修复 ---");

        // 保存当前记录
        const originalRecords = GameData.getAllGameRecords();
        console.log("保存当前游戏记录");

        // 清空所有记录
        GameData.clearAllGameRecords();
        console.log("已清空所有游戏记录");

        // 检查清空后的记录
        const clearedRecords = GameData.getAllGameRecords();
        let allZero = true;
        for (const modeName in clearedRecords) {
            if (clearedRecords.hasOwnProperty(modeName)) {
                const record = clearedRecords[modeName];
                if (record.bestScore > 0) {
                    allZero = false;
                    break;
                }
            }
        }

        if (allZero) {
            console.log("✅ 游戏记录已成功清空，所有分数为0");
            console.log("📝 修复说明：LeaderboardUI.updateMedalsDisplay方法已修改");
            console.log("   - 金银铜牌现在始终显示（active = true）");
            console.log("   - 不再根据分数是否大于0来决定显示状态");
            console.log("   - 请在游戏中打开排行榜->个人记录，检查奖牌是否正常显示");
        } else {
            console.error("❌ 游戏记录清空失败");
        }

        // 恢复原始记录（可选）
        // 这里不恢复，让用户可以测试奖牌显示
        console.log("💡 提示：请手动打开排行榜面板查看奖牌显示效果");
    }

    /**
     * 测试体力消耗修复
     */
    private testEnergyConsumptionFix() {
        console.log("\n--- 验证体力消耗修复 ---");

        const energyManager = EnergyManager.getInstance();
        if (!energyManager) {
            console.error("❌ 未找到EnergyManager实例，无法测试体力消耗");
            return;
        }

        const currentEnergy = energyManager.getCurrentEnergy();
        console.log(`当前体力: ${currentEnergy}/${energyManager.getMaxEnergy()}`);

        if (currentEnergy >= 4) {
            console.log("✅ 体力充足，可以进行游戏");
            console.log("📝 修复说明：GameManager体力消耗逻辑已修复");
            console.log("   - 在transitionToReadyState中重置_energyConsumed标志");
            console.log("   - 在transitionToGamingState中添加详细调试日志");
            console.log("   - 每局游戏只消耗一次体力，新局游戏重新消耗");
            console.log("   - 复活状态下不消耗体力");
            console.log("💡 提示：请开始一局游戏，观察控制台日志确认体力正常消耗");
        } else {
            console.log(`⚠️ 当前体力不足(${currentEnergy}/4)，无法测试体力消耗`);
            console.log("💡 提示：请等待体力恢复后再测试，或手动设置体力值");
        }

        // 输出体力恢复信息
        const timeUntilRecover = energyManager.getTimeUntilNextRecover();
        if (timeUntilRecover > 0) {
            const minutes = Math.floor(timeUntilRecover / 60000);
            const seconds = Math.floor((timeUntilRecover % 60000) / 1000);
            console.log(`下次体力恢复时间: ${minutes}分${seconds}秒`);
        } else if (currentEnergy < energyManager.getMaxEnergy()) {
            console.log("体力即将恢复");
        } else {
            console.log("体力已满");
        }
    }

    /**
     * 手动测试体力消耗（可从控制台调用）
     */
    public static testEnergyConsumption() {
        console.log("=== 手动测试体力消耗 ===");
        
        const energyManager = EnergyManager.getInstance();
        if (!energyManager) {
            console.error("未找到EnergyManager实例");
            return;
        }

        const beforeEnergy = energyManager.getCurrentEnergy();
        console.log(`消耗前体力: ${beforeEnergy}`);

        const success = energyManager.consumeEnergy();
        const afterEnergy = energyManager.getCurrentEnergy();

        console.log(`消耗后体力: ${afterEnergy}`);
        console.log(`消耗成功: ${success}`);

        if (success) {
            console.log(`✅ 体力消耗成功: ${beforeEnergy} -> ${afterEnergy}`);
        } else {
            console.log(`❌ 体力消耗失败: 体力不足`);
        }
    }

    /**
     * 手动清空游戏记录（可从控制台调用）
     */
    public static clearGameRecords() {
        console.log("=== 手动清空游戏记录 ===");
        GameData.clearAllGameRecords();
        console.log("✅ 所有游戏记录已清空");
        console.log("💡 请打开排行榜->个人记录查看奖牌显示效果");
    }

    /**
     * 显示当前状态（可从控制台调用）
     */
    public static showCurrentStatus() {
        console.log("=== 当前状态 ===");
        
        // 显示体力状态
        const energyManager = EnergyManager.getInstance();
        if (energyManager) {
            const currentEnergy = energyManager.getCurrentEnergy();
            const maxEnergy = energyManager.getMaxEnergy();
            const timeUntilRecover = energyManager.getTimeUntilNextRecover();
            
            console.log(`体力: ${currentEnergy}/${maxEnergy}`);
            
            if (timeUntilRecover > 0) {
                const minutes = Math.floor(timeUntilRecover / 60000);
                const seconds = Math.floor((timeUntilRecover % 60000) / 1000);
                console.log(`下次恢复: ${minutes}分${seconds}秒`);
            } else if (currentEnergy < maxEnergy) {
                console.log("体力即将恢复");
            } else {
                console.log("体力已满");
            }
        }

        // 显示游戏记录状态
        const records = GameData.getAllGameRecords();
        let hasRecords = false;
        let totalBestScore = 0;
        
        for (const modeName in records) {
            if (records.hasOwnProperty(modeName)) {
                const record = records[modeName];
                if (record.bestScore > 0) {
                    hasRecords = true;
                    totalBestScore += record.bestScore;
                }
            }
        }

        console.log(`游戏记录: ${hasRecords ? '有记录' : '无记录'}`);
        if (hasRecords) {
            console.log(`总最高分: ${totalBestScore}`);
        }
    }
}
