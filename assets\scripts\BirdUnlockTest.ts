import { _decorator, Component, Node } from 'cc';
import { BirdType, GameData } from './GameData';
const { ccclass, property } = _decorator;

@ccclass('BirdUnlockTest')
export class BirdUnlockTest extends Component {

    start() {
        this.runTests();
    }

    private runTests() {
        console.log("\n=== 小鸟解锁系统测试开始 ===");
        
        this.testInitialState();
        this.testBirdPurchase();
        this.testBirdPrices();
        this.testPurchasedBirdsList();
        
        console.log("=== 小鸟解锁系统测试完成 ===\n");
    }

    /**
     * 测试初始状态
     */
    private testInitialState() {
        console.log("\n--- 测试1: 初始状态测试 ---");
        
        // 清除之前的数据
        localStorage.removeItem("PurchasedBirds");
        
        const purchasedBirds = GameData.getPurchasedBirds();
        console.log("初始已购买小鸟:", purchasedBirds);
        
        // 应该只有普通小鸟
        if (purchasedBirds.length === 1 && purchasedBirds[0] === BirdType.NORMAL) {
            console.log("✓ 初始状态正确：只有普通小鸟");
        } else {
            console.error("✗ 初始状态错误");
        }
        
        // 测试各小鸟的购买状态
        console.log("普通小鸟已购买:", GameData.isBirdPurchased(BirdType.NORMAL));
        console.log("金色小鸟已购买:", GameData.isBirdPurchased(BirdType.GOLD));
        console.log("企鹅已购买:", GameData.isBirdPurchased(BirdType.PENGUIN));
    }

    /**
     * 测试小鸟购买
     */
    private testBirdPurchase() {
        console.log("\n--- 测试2: 小鸟购买测试 ---");
        
        // 设置足够的金币
        localStorage.setItem("TotalCoins", "1000");
        
        const initialCoins = GameData.getTotalCoins();
        console.log("初始金币:", initialCoins);
        
        // 尝试购买金色小鸟
        const goldPrice = GameData.getBirdPrice(BirdType.GOLD);
        console.log("金色小鸟价格:", goldPrice);
        
        const purchaseResult = GameData.purchaseBird(BirdType.GOLD);
        const coinsAfterPurchase = GameData.getTotalCoins();
        
        console.log("购买结果:", purchaseResult);
        console.log("购买后金币:", coinsAfterPurchase);
        console.log("金色小鸟已购买:", GameData.isBirdPurchased(BirdType.GOLD));
        
        if (purchaseResult && coinsAfterPurchase === initialCoins - goldPrice) {
            console.log("✓ 购买功能正常");
        } else {
            console.error("✗ 购买功能异常");
        }
        
        // 尝试重复购买
        const repeatPurchase = GameData.purchaseBird(BirdType.GOLD);
        console.log("重复购买结果:", repeatPurchase);
        
        if (!repeatPurchase) {
            console.log("✓ 重复购买防护正常");
        } else {
            console.error("✗ 重复购买防护失败");
        }
    }

    /**
     * 测试小鸟价格
     */
    private testBirdPrices() {
        console.log("\n--- 测试3: 小鸟价格测试 ---");
        
        const prices = [
            { bird: BirdType.NORMAL, expectedPrice: 0 },
            { bird: BirdType.GOLD, expectedPrice: 100 },
            { bird: BirdType.PENGUIN, expectedPrice: 150 },
            { bird: BirdType.ALBATROSS, expectedPrice: 200 },
            { bird: BirdType.WOODPECKER, expectedPrice: 250 }
        ];
        
        prices.forEach(({ bird, expectedPrice }) => {
            const actualPrice = GameData.getBirdPrice(bird);
            const birdName = GameData.getBirdName(bird);
            console.log(`${birdName} 价格: ${actualPrice} (期望: ${expectedPrice})`);
            
            if (actualPrice === expectedPrice) {
                console.log(`✓ ${birdName} 价格正确`);
            } else {
                console.error(`✗ ${birdName} 价格错误`);
            }
        });
    }

    /**
     * 测试已购买小鸟列表
     */
    private testPurchasedBirdsList() {
        console.log("\n--- 测试4: 已购买小鸟列表测试 ---");
        
        // 购买企鹅
        const penguinPurchase = GameData.purchaseBird(BirdType.PENGUIN);
        console.log("购买企鹅结果:", penguinPurchase);
        
        const purchasedBirds = GameData.getPurchasedBirds();
        console.log("当前已购买小鸟:", purchasedBirds);
        
        // 应该包含普通小鸟、金色小鸟和企鹅
        const expectedBirds = [BirdType.NORMAL, BirdType.GOLD, BirdType.PENGUIN];
        const allExpectedPresent = expectedBirds.every(bird => 
            purchasedBirds.indexOf(bird) !== -1
        );
        
        if (allExpectedPresent && purchasedBirds.length === expectedBirds.length) {
            console.log("✓ 已购买小鸟列表正确");
        } else {
            console.error("✗ 已购买小鸟列表错误");
        }
    }

    /**
     * 测试金币不足情况
     */
    private testInsufficientCoins() {
        console.log("\n--- 测试5: 金币不足测试 ---");
        
        // 设置较少的金币
        localStorage.setItem("TotalCoins", "50");
        
        const initialCoins = GameData.getTotalCoins();
        console.log("当前金币:", initialCoins);
        
        // 尝试购买信天翁（价格200）
        const albatrossPrice = GameData.getBirdPrice(BirdType.ALBATROSS);
        console.log("信天翁价格:", albatrossPrice);
        
        const purchaseResult = GameData.purchaseBird(BirdType.ALBATROSS);
        const coinsAfterAttempt = GameData.getTotalCoins();
        
        console.log("购买结果:", purchaseResult);
        console.log("尝试购买后金币:", coinsAfterAttempt);
        
        if (!purchaseResult && coinsAfterAttempt === initialCoins) {
            console.log("✓ 金币不足防护正常");
        } else {
            console.error("✗ 金币不足防护失败");
        }
    }
}
