import { _decorator, Component, Node, Label, Sprite, SpriteFrame, Color, LabelOutline } from 'cc';
import { RankItem } from '../Data/WeChatFriendsData';
const { ccclass, property } = _decorator;

/**
 * 好友排行榜条目组件
 */
@ccclass('FriendsRankItem')
export class FriendsRankItem extends Component {
    
    @property(Label)
    rankLabel: Label = null;

    @property(Sprite)
    avatarSprite: Sprite = null;

    @property(Label)
    nameLabel: Label = null;

    @property(Label)
    scoreLabel: Label = null;

    @property(Node)
    itemBackground: Node = null;

    // 颜色配置
    private readonly RANK_COLORS = {
        1: new Color(255, 215, 0, 255),   // 金色
        2: new Color(192, 192, 192, 255), // 银色
        3: new Color(205, 127, 50, 255),  // 铜色
        default: new Color(255, 255, 255, 255) // 白色
    };

    private readonly SELF_COLOR = new Color(100, 255, 100, 255); // 自己的颜色：绿色
    private readonly NORMAL_COLOR = new Color(255, 255, 255, 255); // 普通颜色：白色

    onLoad() {
        // 自动获取子节点组件
        this.autoGetComponents();
    }

    /**
     * 自动获取子节点组件
     */
    private autoGetComponents(): void {
        if (!this.rankLabel) {
            const rankNode = this.node.getChildByName("RankLabel");
            if (rankNode) {
                this.rankLabel = rankNode.getComponent(Label);
            }
        }

        if (!this.avatarSprite) {
            const avatarNode = this.node.getChildByName("AvatarSprite");
            if (avatarNode) {
                this.avatarSprite = avatarNode.getComponent(Sprite);
            }
        }

        if (!this.nameLabel) {
            const nameNode = this.node.getChildByName("NameLabel");
            if (nameNode) {
                this.nameLabel = nameNode.getComponent(Label);
            }
        }

        if (!this.scoreLabel) {
            const scoreNode = this.node.getChildByName("ScoreLabel");
            if (scoreNode) {
                this.scoreLabel = scoreNode.getComponent(Label);
            }
        }

        if (!this.itemBackground) {
            this.itemBackground = this.node.getChildByName("ItemBackground");
        }
    }

    /**
     * 设置排行榜条目数据
     */
    public setRankData(rankItem: RankItem, defaultAvatar?: SpriteFrame): void {
        if (!rankItem) {
            console.error("FriendsRankItem: rankItem为空");
            return;
        }

        // 设置排名
        this.setRank(rankItem.rank);

        // 设置头像
        this.setAvatar(rankItem.friend.avatarUrl, defaultAvatar);

        // 设置昵称
        this.setNickname(rankItem.friend.nickname, rankItem.friend.id === "self");

        // 设置分数
        this.setScore(rankItem.score);

        // 设置背景样式（如果是自己）
        this.setBackgroundStyle(rankItem.friend.id === "self");
    }

    /**
     * 设置排名
     */
    private setRank(rank: number): void {
        if (this.rankLabel) {
            this.rankLabel.string = rank.toString();

            // 根据排名设置颜色
            if (rank <= 3) {
                this.rankLabel.color = this.RANK_COLORS[rank] || this.RANK_COLORS.default;
            } else {
                this.rankLabel.color = this.RANK_COLORS.default;
            }

            // 添加黑色描边效果
            this.addLabelOutline(this.rankLabel);
        }
    }

    /**
     * 设置头像
     */
    private setAvatar(avatarUrl: string, defaultAvatar?: SpriteFrame): void {
        if (this.avatarSprite) {
            // 在实际项目中，这里应该加载微信头像
            // 现在使用默认头像
            if (defaultAvatar) {
                this.avatarSprite.spriteFrame = defaultAvatar;
            }
            
            // TODO: 实际项目中应该这样加载微信头像
            // this.loadWeChatAvatar(avatarUrl);
        }
    }

    /**
     * 设置昵称
     */
    private setNickname(nickname: string, isSelf: boolean): void {
        if (this.nameLabel) {
            this.nameLabel.string = nickname;

            // 如果是自己，使用特殊颜色
            this.nameLabel.color = isSelf ? this.SELF_COLOR : this.NORMAL_COLOR;

            // 添加黑色描边效果
            this.addLabelOutline(this.nameLabel);
        }
    }

    /**
     * 设置分数
     */
    private setScore(score: number): void {
        if (this.scoreLabel) {
            this.scoreLabel.string = score.toString();

            // 添加黑色描边效果
            this.addLabelOutline(this.scoreLabel);
        }
    }

    /**
     * 设置背景样式
     */
    private setBackgroundStyle(isSelf: boolean): void {
        if (this.itemBackground) {
            const sprite = this.itemBackground.getComponent(Sprite);
            if (sprite) {
                // 如果是自己，可以设置特殊的背景色或透明度
                if (isSelf) {
                    sprite.color = new Color(100, 255, 100, 50); // 淡绿色背景
                } else {
                    sprite.color = new Color(255, 255, 255, 30); // 淡白色背景
                }
            }
        }
    }

    /**
     * 加载微信头像（实际项目中使用）
     * 这里只是示例代码，实际需要根据微信小游戏API实现
     */
    private loadWeChatAvatar(avatarUrl: string): void {
        // TODO: 实际项目中的微信头像加载逻辑
        /*
        if (avatarUrl && this.avatarSprite) {
            // 使用微信小游戏的图片加载API
            wx.downloadFile({
                url: avatarUrl,
                success: (res) => {
                    // 加载图片到Sprite
                    // 这里需要将下载的图片转换为SpriteFrame
                }
            });
        }
        */
        console.log(`FriendsRankItem: 模拟加载头像 ${avatarUrl}`);
    }

    /**
     * 重置条目状态
     */
    public reset(): void {
        if (this.rankLabel) {
            this.rankLabel.string = "";
            this.rankLabel.color = this.NORMAL_COLOR;
        }

        if (this.nameLabel) {
            this.nameLabel.string = "";
            this.nameLabel.color = this.NORMAL_COLOR;
        }

        if (this.scoreLabel) {
            this.scoreLabel.string = "";
        }

        if (this.itemBackground) {
            const sprite = this.itemBackground.getComponent(Sprite);
            if (sprite) {
                sprite.color = new Color(255, 255, 255, 30);
            }
        }
    }

    /**
     * 为Label添加黑色描边效果
     */
    private addLabelOutline(label: Label): void {
        if (!label) return;

        // 检查是否已有描边组件
        let outline = label.getComponent(LabelOutline);
        if (!outline) {
            // 添加描边组件
            outline = label.addComponent(LabelOutline);
        }

        if (outline) {
            // 设置描边属性
            outline.color = new Color(0, 0, 0, 255); // 黑色描边
            outline.width = 2; // 描边宽度
        }
    }

    /**
     * 播放条目动画（可选）
     */
    public playAppearAnimation(): void {
        // TODO: 可以添加条目出现时的动画效果
        // 例如从左侧滑入、淡入等
    }
}
