import { _decorator, Component } from 'cc';
import { CloudDatabaseManager } from './Data/CloudDatabaseManager';
import { GameDataManager } from './Data/GameDataManager';
import { GameData } from './GameData';
const { ccclass } = _decorator;

/**
 * 云数据库调试工具
 * 用于测试和验证云数据库功能
 */
@ccclass('DebugCloudDB')
export class DebugCloudDB extends Component {

    onLoad() {
        // 将调试方法添加到全局window对象，方便在控制台调用
        this.addDebugMethods();
    }

    private addDebugMethods() {
        // 测试金币方法
        (window as any).testCoinMethods = () => {
            console.log("=== 测试金币方法 ===");
            console.log("当前金币 (getCoin):", GameData.getCoin());
            console.log("当前金币 (getTotalCoins):", GameData.getTotalCoins());
            
            GameData.setCoin(5000);
            console.log("设置金币为5000后:");
            console.log("getCoin():", GameData.getCoin());
            console.log("getTotalCoins():", GameData.getTotalCoins());
        };

        // 测试用户数据创建
        (window as any).testUserCreation = async () => {
            console.log("=== 测试用户数据创建 ===");
            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();
            
            const userData = await cloudDB.getMyPlayerData();
            if (userData) {
                console.log("用户数据已存在:", userData);
            } else {
                console.log("用户数据不存在，尝试创建...");
                const success = await cloudDB.createPlayerData("测试玩家" + Date.now(), "");
                console.log("创建结果:", success);
            }
        };

        // 测试模拟数据上传
        (window as any).testMockDataUpload = async () => {
            console.log("=== 测试模拟数据上传 ===");
            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();
            
            console.log("开始上传10条测试数据...");
            const success = await cloudDB.uploadMockPlayersData(10);
            console.log("上传结果:", success);
        };

        // 查询数据库数据
        (window as any).queryDBData = async () => {
            console.log("=== 查询数据库数据 ===");
            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();
            
            const players = await cloudDB.getPlayersData(20, 0);
            console.log(`查询到${players.length}条数据:`, players);
            
            // 检查昵称是否重复
            const nicknames = players.map(p => p.nickname);
            const uniqueNicknames = [...new Set(nicknames)];
            console.log(`昵称总数: ${nicknames.length}, 唯一昵称数: ${uniqueNicknames.length}`);
            
            if (nicknames.length !== uniqueNicknames.length) {
                console.warn("发现重复昵称!");
                const duplicates = nicknames.filter((name, index) => nicknames.indexOf(name) !== index);
                console.log("重复的昵称:", [...new Set(duplicates)]);
            } else {
                console.log("✅ 所有昵称都是唯一的");
            }
        };

        // 清空数据库（谨慎使用）
        (window as any).clearDatabase = async () => {
            console.log("=== 清空数据库 ===");
            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();
            
            const success = await cloudDB.clearPlayersData();
            console.log("清空结果:", success);
        };

        // 测试新的全服排行榜云函数
        (window as any).testGlobalLeaderboard = async () => {
            console.log("=== 测试全服排行榜云函数 ===");
            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();

            // 测试云函数
            await cloudDB.testCloudFunction(GameMode.NORMAL_STANDARD);

            // 测试获取排行榜
            const players = await cloudDB.getGlobalLeaderboard(GameMode.NORMAL_STANDARD, 100);
            console.log(`获取到${players.length}条排行榜数据:`, players.slice(0, 5));
        };

        // 测试邀请码生成
        (window as any).testInviteCodeGeneration = async () => {
            console.log("=== 测试邀请码生成 ===");

            if (typeof wx !== 'undefined' && wx.cloud && wx.cloud.callFunction) {
                const result = await wx.cloud.callFunction({
                    name: 'generateInviteCode',
                    data: {}
                });

                if (result.result && result.result.success) {
                    console.log(`生成的邀请码: ${result.result.inviteCode}`);
                    console.log(`尝试次数: ${result.result.attempts}`);
                } else {
                    console.error('生成邀请码失败:', result.result?.error);
                }
            } else {
                console.error('微信云函数不可用');
            }
        };

        // 手动更新排行榜缓存（更新现有的6条记录）
        (window as any).updateLeaderboardCache = async () => {
            console.log("=== 手动更新排行榜缓存 ===");
            console.log("注意：此操作会更新现有的6条缓存记录，不会创建新记录");

            if (typeof wx !== 'undefined' && wx.cloud && wx.cloud.callFunction) {
                const result = await wx.cloud.callFunction({
                    name: 'updateLeaderboardCache',
                    data: {}
                });

                if (result.result && result.result.success) {
                    console.log(`✅ 更新成功: ${result.result.message}`);
                    console.log(`📊 总玩家数: ${result.result.totalPlayers}`);
                    console.log(`⏰ 更新时间: ${result.result.updateTime}`);
                    console.log('📋 详细结果:', result.result.results);
                } else {
                    console.error('❌ 更新排行榜缓存失败:', result.result?.error);
                }
            } else {
                console.error('❌ 微信云函数不可用');
            }
        };

        // 测试从缓存获取排行榜
        (window as any).testCacheLeaderboard = async () => {
            console.log("=== 测试从缓存获取排行榜 ===");

            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();

            // 获取缓存更新时间
            const updateTimes = await cloudDB.getLeaderboardCacheUpdateTimes();
            console.log('缓存更新时间:', updateTimes);

            // 获取缓存数据
            const cacheData = await cloudDB.getAllGlobalLeaderboardsFromCache();
            Object.keys(cacheData).forEach(mode => {
                console.log(`关卡${mode}: ${cacheData[mode].length}条数据`);
                if (cacheData[mode].length > 0) {
                    console.log(`  第1名: ${cacheData[mode][0].nickname}, 分数: ${cacheData[mode][0].maxScore}`);
                }
            });
        };

        // 检查缓存记录状态
        (window as any).checkCacheStatus = async () => {
            console.log("=== 检查缓存记录状态 ===");

            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();

            try {
                // 直接查询缓存集合
                const result = await cloudDB._db.collection('leaderboard_cache')
                    .field({
                        gameMode: true,
                        updateTime: true,
                        'players.length': true
                    })
                    .get();

                if (result.errMsg === 'collection.get:ok') {
                    console.log(`找到${result.data.length}条缓存记录:`);
                    result.data.forEach((record: any) => {
                        console.log(`  关卡${record.gameMode}: ${record.players?.length || 0}条数据, 更新时间: ${record.updateTime}`);
                    });

                    const missingModes = [];
                    for (let i = 0; i < 6; i++) {
                        if (!result.data.find((r: any) => r.gameMode === i)) {
                            missingModes.push(i);
                        }
                    }

                    if (missingModes.length > 0) {
                        console.warn(`⚠️ 缺少的关卡记录: ${missingModes.join(', ')}`);
                        console.log('💡 建议手动在云数据库中创建缺失的记录，或运行: updateLeaderboardCache()');
                    } else {
                        console.log('✅ 所有关卡的缓存记录都存在');
                        console.log('🔄 定时触发器每分钟会自动更新这些记录');
                    }
                } else {
                    console.error('查询缓存集合失败:', result);
                }
            } catch (error) {
                console.error('检查缓存状态时出错:', error);
            }
        };

        // 完整测试流程
        (window as any).fullTest = async () => {
            console.log("=== 开始完整测试流程 ===");

            // 1. 测试金币方法
            (window as any).testCoinMethods();

            // 2. 初始化GameDataManager
            console.log("\n--- 初始化GameDataManager ---");
            const gameDataManager = GameDataManager.instance;
            const initSuccess = await gameDataManager.initialize();
            console.log("初始化结果:", initSuccess);

            // 3. 查询当前数据
            console.log("\n--- 查询当前数据 ---");
            await (window as any).queryDBData();

            console.log("=== 完整测试流程结束 ===");
        };

        console.log("🔧 调试方法已添加到window对象:");
        console.log("- testCoinMethods(): 测试金币方法");
        console.log("- testUserCreation(): 测试用户创建");
        console.log("- testMockDataUpload(): 测试模拟数据上传");
        console.log("- queryDBData(): 查询数据库数据");
        console.log("- clearDatabase(): 清空数据库");
        console.log("- testGlobalLeaderboard(): 测试全服排行榜云函数");
        console.log("- testInviteCodeGeneration(): 测试邀请码生成");
        console.log("- updateLeaderboardCache(): 手动更新排行榜缓存");
        console.log("- testCacheLeaderboard(): 测试从缓存获取排行榜");
        console.log("- checkCacheStatus(): 检查缓存记录状态");
        console.log("- fullTest(): 完整测试流程");
    }
}
