{"description": "项目配置文件。", "miniprogramRoot": "", "setting": {"urlCheck": true, "postcss": true, "minified": true, "newFeature": false, "enhance": true, "useIsolateContext": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "packNpmRelationList": []}, "compileType": "game", "libVersion": "3.8.10", "appid": "wx9a75f92306014b22", "projectname": "小鸟向前飞", "condition": {}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "cloudfunctionRoot": "cloudfunctions/", "cloudbaseRoot": "cloudbase/", "projectArchitecture": "miniProgram"}