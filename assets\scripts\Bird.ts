import { _decorator, Animation, animation, AudioClip, Collider2D, Component, Contact2DType, Input, input, IPhysics2DContact, Node, RigidBody2D, SkelAnimDataHub, Vec2, Vec3 } from 'cc';
import { Tags } from './Tags';
import { GameManager } from './GameManager';
import { AudioMgr } from './AudioMgr';
import { GameDifficulty } from './GameDifficulty';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
import { GameData } from './GameData';
import { AlbatrossInvincibilityManager } from './AlbatrossInvincibilityManager';
import { WoodpeckerSkillManager } from './WoodpeckerSkillManager';
const { ccclass, property } = _decorator;

@ccclass('Bird')
export class Bird extends Component {

    private rgd2D:RigidBody2D = null;

    @property
    rotateSpeed:number = 30;

    // 不同难度的跳跃力度
    private readonly JUMP_FORCE_EASY: number = 12;
    private readonly JUMP_FORCE_NORMAL: number = 15;
    private readonly JUMP_FORCE_HARD: number = 18;
    
    // 雪花效果的跳跃力度
    private readonly JUMP_FORCE_SNOW_EFFECT: number = 10;
    
    // 雪花效果持续时间（秒）
    private readonly SNOW_EFFECT_DURATION: number = 2.0;
    
    // 雪花效果计时器
    private snowEffectTimer: number = 0;
    
    // 是否受到雪花影响
    private isAffectedBySnow: boolean = false;

    @property(AudioClip)
    clickAudio:AudioClip = null;

    private _canControl:boolean = false;

    onLoad () {
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);

        // 注册单个碰撞体的回调函数
        let collider = this.getComponent(Collider2D);
        if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
            collider.on(Contact2DType.END_CONTACT, this.onEndContact, this);
        }

        this.rgd2D = this.getComponent(RigidBody2D);

        // 根据难度设置重力
        this.applyDifficultySettings();
    }

    // 根据难度设置重力
    private applyDifficultySettings() {
        if (!this.rgd2D) return;

        // 获取当前难度的重力值
        const gravityValue = GameManager.inst().getCurrentGravityValue();

        // 直接设置重力值
        this.rgd2D.gravityScale = gravityValue;

        console.log(`小鸟重力设置为: ${this.rgd2D.gravityScale}`);
    }

    onDestroy () {
        // 移除输入事件监听器
        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);

        // 移除碰撞事件监听器
        let collider = this.getComponent(Collider2D);
        if (collider) {
            collider.off(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
            collider.off(Contact2DType.END_CONTACT, this.onEndContact, this);
        }

        // 取消所有调度器
        this.unscheduleAllCallbacks();

        // 清理静态变量
        Bird.lastScoredPipeId = "";
        Bird.scoreResetTimer = 0;

        console.log("Bird: 组件已销毁，事件监听器已清理");
    }

    onTouchStart(){
        if(this._canControl==false)return;

        // 获取跳跃力度
        let jumpForce = this.getCurrentJumpForce();

        // 应用跳跃力度
        this.rgd2D.linearVelocity = new Vec2(0, jumpForce);

        this.node.angle = 30;
        AudioMgr.inst.playOneShot(this.clickAudio,7.0);
    }
    
    /**
     * 获取当前跳跃力度
     * 根据难度和是否受雪花影响决定
     */
    private getCurrentJumpForce(): number {
        // 如果受到雪花影响，返回降低的跳跃力度
        if (this.isAffectedBySnow) {
            return this.JUMP_FORCE_SNOW_EFFECT;
        }
        
        // 否则根据当前难度返回正常跳跃力度
        const currentDifficulty = GameDifficulty.getDifficulty();
        
        switch(currentDifficulty) {
            case GameDifficulty.DIFFICULTY_NORMAL:
                return this.JUMP_FORCE_NORMAL;
            case GameDifficulty.DIFFICULTY_HARD:
                return this.JUMP_FORCE_HARD;
            default:
                return this.JUMP_FORCE_EASY;
        }
    }

    protected update(dt: number): void {
        if(this._canControl==false)return;

        // 更新小鸟旋转
        this.node.angle -= this.rotateSpeed*dt;
        if(this.node.angle<-60){
            this.node.angle = -60;
        }

        // 更新雪花效果计时器
        this.updateSnowEffect(dt);

        // 更新计分重置计时器
        if (Bird.scoreResetTimer > 0) {
            Bird.scoreResetTimer -= dt;
            if (Bird.scoreResetTimer <= 0) {
                Bird.lastScoredPipeId = "";
                console.log("计分记录已重置，可以计算下一个管道");
            }
        }
    }
    
    /**
     * 更新雪花效果计时器
     */
    private updateSnowEffect(dt: number) {
        // 如果正在受到雪花影响
        if (this.isAffectedBySnow) {
            // 减少计时器
            this.snowEffectTimer -= dt;
            
            // 如果计时器结束，移除效果
            if (this.snowEffectTimer <= 0) {
                this.removeSnowEffect();
            }
        }
    }
    
    /**
     * 应用雪花效果
     * 当小鸟碰到雪花时调用
     */
    public applySnowEffect() {
        console.log("小鸟受到雪花影响，跳跃力度降低！");
        
        // 标记为受到雪花影响
        this.isAffectedBySnow = true;
        
        // 重置计时器（无论是新效果还是刷新已有效果）
        this.snowEffectTimer = this.SNOW_EFFECT_DURATION;
    }
    
    /**
     * 移除雪花效果
     */
    private removeSnowEffect() {
        console.log("雪花效果结束，跳跃力度恢复正常");
        
        // 移除标记
        this.isAffectedBySnow = false;
        this.snowEffectTimer = 0;
    }

    public enableControl(){
        this.getComponent(Animation).enabled=true;
        this._canControl=true;

        // 确保RigidBody2D启用
        if (this.rgd2D && !this.rgd2D.enabled) {
            this.rgd2D.enabled = true;
        }

        // 确保应用正确的难度设置
        this.applyDifficultySettings();
    }
    public disableControl(){
        this.getComponent(Animation).enabled=false;
        this._canControl = false;

        // 延迟禁用RigidBody2D，避免在物理碰撞过程中禁用
        this.scheduleOnce(() => {
            if (this.rgd2D && this.rgd2D.isValid) {
                this.rgd2D.enabled = false;
            }
        }, 0);
    }

    public disableControlNotRGD(){
        this.getComponent(Animation).enabled=false;
        this._canControl = false;
    }

    /**
     * 强制重置小鸟的物理状态（用于企鹅特殊复活等场景）
     */
    public forceResetPhysicsState(): void {
        if (this.rgd2D && this.rgd2D.enabled) {
            // 只在RigidBody2D启用时重置物理状态
            this.rgd2D.linearVelocity = new Vec2(0, 0);
            this.rgd2D.angularVelocity = 0;
            console.log("小鸟物理状态已强制重置：速度归零");
        }

        // 重置小鸟角度（不依赖物理组件）
        this.node.angle = 0;
        console.log("小鸟角度已重置为0");
    }

    /**
     * 安全地重置小鸟位置和物理状态
     */
    public safeResetBirdState(x: number, y: number): void {
        // 重置位置
        this.node.setPosition(x, y, 0);

        // 重置角度
        this.node.angle = 0;

        // 如果物理组件启用，重置物理状态
        if (this.rgd2D && this.rgd2D.enabled) {
            this.rgd2D.linearVelocity = new Vec2(0, 0);
            this.rgd2D.angularVelocity = 0;
        }

        // 重置计分记录
        Bird.lastScoredPipeId = "";
        Bird.scoreResetTimer = 0;

        console.log(`小鸟状态已安全重置: 位置=(${x}, ${y}), 角度=0`);
    }

    onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null){
        console.log("小鸟碰撞: 标签=", otherCollider.tag, "名称=", otherCollider.node.name);

        // 检查信天翁是否处于无敌状态
        const invincibilityManager = AlbatrossInvincibilityManager.getInstance();
        const isInvincible = invincibilityManager && invincibilityManager.isInvincible();

        // 检测是否与管道碰撞
        if(otherCollider.tag === Tags.PIPE){
            // 如果是无敌状态，忽略管道碰撞（不会失败，且会穿过）
            if (isInvincible) {
                console.log("信天翁无敌状态：忽略管道碰撞，穿过管道");
                // 禁用这次碰撞的物理响应
                if (contact) {
                    contact.disabled = true;
                }
                return;
            }

            // 检查是否是啄木鸟且技能可用
            const woodpeckerSkillManager = WoodpeckerSkillManager.getInstance();
            if (woodpeckerSkillManager && woodpeckerSkillManager.tryUseSkill(otherCollider)) {
                console.log("啄木鸟使用技能啄掉管道");
                // 禁用这次碰撞的物理响应，避免死亡
                if (contact) {
                    contact.disabled = true;
                }
                return;
            }

            // 非无敌状态且非啄木鸟技能：正常处理死亡
            this.handleBirdDeath();
        }

        // 检测是否与地面碰撞
        if(otherCollider.tag === Tags.LAND){
            // 如果是无敌状态，不失败但保持物理碰撞
            if (isInvincible) {
                console.log("信天翁无敌状态：地面碰撞不失败，但会被阻挡");
                return;
            }

            // 非无敌状态：正常处理死亡
            this.handleBirdDeath();
        }

        // 金币碰撞现在由金币自己处理，不再需要在这里处理
    }

    /**
     * 处理小鸟死亡逻辑
     * 检查是否是企鹅并且可以使用特殊复活
     */
    private handleBirdDeath(): void {
        // 检查是否是企鹅且可以使用特殊复活
        if (GameData.canPenguinSpecialRevive()) {
            console.log("企鹅特殊复活：第一次死亡，使用特殊复活能力");
            this.triggerPenguinSpecialRevive();
        } else {
            // 正常死亡流程
            console.log("正常死亡流程");
            GameManager.inst().transitionToGameOverState();
        }
    }

    /**
     * 触发企鹅特殊复活
     */
    private triggerPenguinSpecialRevive(): void {
        // 保存当前游戏状态
        const currentScore = GameData.getScore();
        const currentSessionCoins = GameData.getSessionCoins();

        // 设置企鹅特殊复活状态
        GameData.setPenguinSpecialReviveState(currentScore, currentSessionCoins);

        // 延迟一帧执行状态切换，避免在物理碰撞检测过程中修改物理组件
        const gameManager = GameManager.inst();
        if (gameManager) {
            console.log("企鹅特殊复活：延迟切换到游戏准备状态");
            gameManager.scheduleOnce(() => {
                gameManager.transitionToReadyState();
            }, 0);
        } else {
            console.error("无法获取GameManager实例");
        }
    }

    // 添加一个静态变量来防止重复计分
    private static lastScoredPipeId: string = "";
    private static scoreResetTimer: number = 0;

    onEndContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null){
        // 检测是否通过管道中间
        if(otherCollider.tag===Tags.PIPE_MIDDLE){
            // 获取管道的唯一标识
            const pipeId = otherCollider.node.parent ? otherCollider.node.parent.uuid : otherCollider.node.uuid;

            // 检查是否是重复计分
            if (Bird.lastScoredPipeId === pipeId) {
                console.log(`防重复计分：管道${pipeId.substring(0,8)}已计分，忽略`);
                return;
            }

            // 检查是否处于无敌状态
            const invincibilityManager = AlbatrossInvincibilityManager.getInstance();
            const isInvincible = invincibilityManager && invincibilityManager.isInvincible();

            // 记录这次计分的管道ID
            Bird.lastScoredPipeId = pipeId;

            // 设置重置计时器（1秒后清除记录，允许下一个管道计分）
            Bird.scoreResetTimer = 1.0;

            if (isInvincible) {
                // 无敌状态下：只加1分，不计算管道通过
                GameManager.inst().addScore(1);
                console.log(`信天翁无敌状态：通过管道空隙，加1分 (管道ID: ${pipeId.substring(0,8)})`);
            } else {
                // 正常状态：加1分并计算管道通过
                GameManager.inst().addScore(1);
                if (invincibilityManager) {
                    invincibilityManager.onPipePassed();
                }
                console.log(`正常状态：通过管道空隙，加1分 (管道ID: ${pipeId.substring(0,8)})`);
            }
        }
    }

}


