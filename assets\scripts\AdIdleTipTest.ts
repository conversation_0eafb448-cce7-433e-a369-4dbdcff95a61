import { _decorator, Component, Button } from 'cc';
import { AdIdleTipManager } from './AdIdleTipManager';
const { ccclass, property } = _decorator;

/**
 * 广告空闲提示测试组件
 * 用于测试AdIdleTipManager的功能
 */
@ccclass('AdIdleTipTest')
export class AdIdleTipTest extends Component {

    @property(Button)
    testButton: Button = null;

    start() {
        if (this.testButton) {
            this.testButton.node.on(Button.EventType.CLICK, this.onTestButtonClick, this);
        }
    }

    onDestroy() {
        if (this.testButton && this.testButton.isValid) {
            this.testButton.node.off(Button.EventType.CLICK, this.onTestButtonClick, this);
        }
    }

    private onTestButtonClick() {
        console.log("AdIdleTipTest: 测试显示广告空闲提示");
        AdIdleTipManager.showTip();
    }
}
