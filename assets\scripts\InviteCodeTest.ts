import { _decorator, Component, Node } from 'cc';
import { InviteCodeManager } from './InviteCodeManager';
import { GameData } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 邀请码功能测试脚本
 * 用于测试邀请码系统的各种功能
 */
@ccclass('InviteCodeTest')
export class InviteCodeTest extends Component {
    
    start() {
        // 在开发阶段可以启用测试
        if (this.isTestMode()) {
            this.runTests();
        }
    }
    
    /**
     * 检查是否是测试模式
     */
    private isTestMode(): boolean {
        // 可以通过URL参数或其他方式来判断是否是测试模式
        return typeof window !== 'undefined' && 
               window.location && 
               window.location.search.includes('test=invite');
    }
    
    /**
     * 运行邀请码测试
     */
    private runTests(): void {
        console.log("=== 邀请码系统测试开始 ===");
        
        // 测试1: 获取玩家邀请码
        this.testGetPlayerInviteCode();
        
        // 测试2: 测试首次注册检查
        this.testFirstDayRegistration();
        
        // 测试3: 测试邀请码使用
        this.testUseInviteCode();
        
        // 测试4: 测试系统状态
        this.testSystemStatus();
        
        console.log("=== 邀请码系统测试结束 ===");
    }
    
    /**
     * 测试获取玩家邀请码
     */
    private testGetPlayerInviteCode(): void {
        console.log("--- 测试1: 获取玩家邀请码 ---");
        
        const inviteCode = InviteCodeManager.getPlayerInviteCode();
        console.log(`玩家邀请码: ${inviteCode}`);
        console.log(`邀请码长度: ${inviteCode.length}`);
        console.log(`邀请码格式检查: ${/^[A-Za-z0-9]{6}$/.test(inviteCode)}`);

        // 再次获取，应该是同一个邀请码
        const inviteCode2 = InviteCodeManager.getPlayerInviteCode();
        console.log(`再次获取邀请码: ${inviteCode2}`);
        console.log(`邀请码一致性: ${inviteCode === inviteCode2}`);
    }
    
    /**
     * 测试首次注册检查
     */
    private testFirstDayRegistration(): void {
        console.log("--- 测试2: 首次注册检查 ---");
        
        const isFirstDay = InviteCodeManager.isFirstDayRegistration();
        console.log(`是否首次注册: ${isFirstDay}`);
        
        const hasUsedCode = InviteCodeManager.hasUsedInviteCode();
        console.log(`是否已使用邀请码: ${hasUsedCode}`);
        
        const shouldShowInput = InviteCodeManager.shouldShowInviteInput();
        console.log(`是否应该显示输入框: ${shouldShowInput}`);
    }
    
    /**
     * 测试邀请码使用
     */
    private testUseInviteCode(): void {
        console.log("--- 测试3: 邀请码使用测试 ---");
        
        const playerCode = InviteCodeManager.getPlayerInviteCode();
        const beforeCoins = GameData.getTotalCoins();
        console.log(`使用前金币数: ${beforeCoins}`);
        
        // 测试使用自己的邀请码（应该失败）
        console.log("测试使用自己的邀请码:");
        const result1 = InviteCodeManager.useInviteCode(playerCode);
        console.log(`结果: ${result1} (应该是false)`);
        
        // 测试使用无效格式的邀请码（应该失败）
        console.log("测试使用无效格式的邀请码:");
        const result2 = InviteCodeManager.useInviteCode("INVALID");
        console.log(`结果: ${result2} (应该是false)`);
        
        // 测试使用有效的其他邀请码（应该成功，如果还没使用过）
        console.log("测试使用有效的其他邀请码:");
        const validCodes = InviteCodeManager.getValidInviteCodes();
        console.log("数据库中的有效邀请码:", validCodes);
        const testCode = validCodes[0]; // 使用第一个有效邀请码
        const result3 = InviteCodeManager.useInviteCode(testCode);
        console.log(`结果: ${result3}`);
        
        const afterCoins = GameData.getTotalCoins();
        console.log(`使用后金币数: ${afterCoins}`);
        console.log(`金币变化: ${afterCoins - beforeCoins}`);
        
        // 再次尝试使用邀请码（应该失败）
        console.log("再次尝试使用邀请码:");
        const result4 = InviteCodeManager.useInviteCode("XyZ789");
        console.log(`结果: ${result4} (应该是false，因为已经使用过)`);
    }
    
    /**
     * 测试系统状态
     */
    private testSystemStatus(): void {
        console.log("--- 测试4: 系统状态 ---");
        
        const status = InviteCodeManager.getInviteSystemStatus();
        console.log("邀请码系统状态:", status);
        
        console.log(`玩家邀请码: ${status.playerCode}`);
        console.log(`是否首次注册: ${status.isFirstDay}`);
        console.log(`是否已使用邀请码: ${status.hasUsedCode}`);
        console.log(`是否应该显示输入框: ${status.shouldShowInput}`);
        console.log(`有效邀请码数量: ${status.validCodesCount}`);
        console.log(`邀请奖励金币数: ${InviteCodeManager.getInviteReward()}`);

        // 显示所有有效邀请码
        const validCodes = InviteCodeManager.getValidInviteCodes();
        console.log("数据库中的有效邀请码:", validCodes);
    }
    
    /**
     * 重置邀请码系统（用于测试）
     * 可以在浏览器控制台中调用: window.resetInviteSystem()
     */
    public static resetInviteSystem(): void {
        console.log("重置邀请码系统...");
        InviteCodeManager.resetInviteSystem();
        console.log("邀请码系统已重置");
        
        // 重新显示状态
        const status = InviteCodeManager.getInviteSystemStatus();
        console.log("重置后的系统状态:", status);
    }
    
    /**
     * 模拟使用邀请码（用于测试）
     */
    public static simulateUseInviteCode(code: string): boolean {
        console.log(`模拟使用邀请码: ${code}`);
        const result = InviteCodeManager.useInviteCode(code);
        console.log(`使用结果: ${result}`);
        
        if (result) {
            console.log(`获得奖励: ${InviteCodeManager.getInviteReward()} 金币`);
            console.log(`当前总金币: ${GameData.getTotalCoins()}`);
        }
        
        return result;
    }
    
    /**
     * 检查邀请码有效性（用于测试）
     */
    public static checkInviteCode(code: string): void {
        console.log(`=== 检查邀请码: ${code} ===`);
        const result = InviteCodeManager.checkInviteCodeValidity(code);
        console.log(`格式正确: ${result.isValidFormat}`);
        console.log(`在数据库中: ${result.isInDatabase}`);
        console.log(`是自己的邀请码: ${result.isOwnCode}`);
        console.log(`可以使用: ${result.canUse}`);

        // 显示对应的玩家信息
        if (result.isInDatabase) {
            const playerInfo = InviteCodeManager.getSimulatedPlayerInfo(code);
            if (playerInfo) {
                console.log(`对应玩家: ${playerInfo.name}, 当前金币: ${playerInfo.coins}`);
            }
        }
        console.log("========================");
    }

    /**
     * 查看所有模拟玩家状态
     */
    public static showAllPlayers(): void {
        console.log("=== 所有模拟玩家状态 ===");
        const players = InviteCodeManager.getAllSimulatedPlayers();
        for (const code in players) {
            const player = players[code];
            console.log(`${code}: ${player.name} - ${player.coins} 金币`);
        }
        console.log("=====================");
    }

    /**
     * 重置模拟玩家数据
     */
    public static resetPlayers(): void {
        InviteCodeManager.resetSimulatedPlayers();
        console.log("模拟玩家数据已重置");
    }

    onLoad() {
        // 将测试方法暴露到全局，方便在浏览器控制台中调用
        if (typeof window !== 'undefined') {
            (window as any).resetInviteSystem = InviteCodeTest.resetInviteSystem;
            (window as any).simulateUseInviteCode = InviteCodeTest.simulateUseInviteCode;
            (window as any).checkInviteCode = InviteCodeTest.checkInviteCode;
            (window as any).getInviteStatus = () => {
                return InviteCodeManager.getInviteSystemStatus();
            };
            (window as any).getValidCodes = () => {
                return InviteCodeManager.getValidInviteCodes();
            };
            (window as any).showAllPlayers = InviteCodeTest.showAllPlayers;
            (window as any).resetPlayers = InviteCodeTest.resetPlayers;
            console.log("邀请码测试方法已暴露到全局:");
            console.log("- window.resetInviteSystem() - 重置邀请码系统");
            console.log("- window.simulateUseInviteCode(code) - 模拟使用邀请码");
            console.log("- window.checkInviteCode(code) - 检查邀请码有效性");
            console.log("- window.getInviteStatus() - 获取系统状态");
            console.log("- window.getValidCodes() - 获取所有有效邀请码");
            console.log("- window.showAllPlayers() - 查看所有模拟玩家状态");
            console.log("- window.resetPlayers() - 重置模拟玩家数据");
        }
    }
}
