import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

// 小鸟类型枚举
export enum BirdType {
    NORMAL = 0,    // 普通小鸟
    GOLD = 1,      // 金色小鸟
    PENGUIN = 2,   // 企鹅小鸟
    ALBATROSS = 3, // 信天翁小鸟
    WOODPECKER = 4 // 啄木鸟小鸟
}

// 游戏模式枚举
export enum GameMode {
    NORMAL_EASY = 0,     // 普通模式-轻松
    NORMAL_STANDARD = 1, // 普通模式-标准
    NORMAL_HARD = 2,     // 普通模式-困难
    CHALLENGE_WIND = 3,  // 挑战模式-大风吹
    CHALLENGE_FOG = 4,   // 挑战模式-大雾起
    CHALLENGE_SNOW = 5   // 挑战模式-大雪飘
}


export class GameData {

    // 游戏模式相关存储键
    private static readonly BESTSCORE_PREFIX:string = "BestScore_Mode_";
    private static readonly TOP_SCORES_PREFIX:string = "TopScores_Mode_";

    // 其他存储键保持不变
    private static readonly TOTAL_COINS:string = "TotalCoins";
    private static readonly SELECTED_BIRD:string = "SelectedBird";
    private static readonly PURCHASED_BACKGROUNDS:string = "PurchasedBackgrounds";
    private static readonly SELECTED_BACKGROUND:string = "SelectedBackground";
    private static readonly PURCHASED_BIRDS:string = "PurchasedBirds";

    private static _score:number = 0;
    private static _sessionCoins:number = 0; // 本局收集的金币
    private static _selectedBirdType:BirdType = BirdType.NORMAL; // 默认选择普通小鸟
    private static _currentGameMode:GameMode = GameMode.NORMAL_STANDARD; // 当前游戏模式

    // 复活相关数据
    private static _isRevived:boolean = false; // 是否是复活状态
    private static _revivedScore:number = 0; // 复活时保存的分数
    private static _revivedSessionCoins:number = 0; // 复活时保存的本局金币
    private static _sessionReviveCount:number = 0; // 本局已使用的复活次数
    private static readonly MAX_REVIVE_PER_SESSION:number = 3; // 每局最大复活次数（复活币+企鹅+广告）

    // 各种复活方式的使用状态
    private static _reviveCoinUsed:boolean = false; // 复活币是否已使用
    private static _penguinReviveUsed:boolean = false; // 企鹅复活是否已使用
    private static _adReviveUsed:boolean = false; // 广告复活是否已使用

    // 企鹅特殊复活相关数据
    private static _penguinUsedSpecialRevive:boolean = false; // 企鹅是否已使用特殊复活（兼容旧代码）
    private static _isPenguinSpecialRevive:boolean = false; // 是否是企鹅特殊复活状态
    private static _isAdRevive:boolean = false; // 是否是广告复活状态

    // 静态初始化块 - 初始化游戏记录系统
    static {
        console.log("GameData: 初始化游戏记录系统");
        // 检查是否是首次运行，如果是则初始化记录为0
        GameData.initializeGameRecordsIfNeeded();
    }

    public static addScore(count:number=1){
        this._score+=count;
    }

    public static getScore():number{
        return this._score;
    }

    // 设置当前游戏模式
    public static setCurrentGameMode(mode: GameMode): void {
        this._currentGameMode = mode;
        console.log(`设置当前游戏模式: ${this.getGameModeName(mode)}`);
    }

    // 获取当前游戏模式
    public static getCurrentGameMode(): GameMode {
        return this._currentGameMode;
    }

    // 获取游戏模式名称
    public static getGameModeName(mode: GameMode): string {
        switch(mode) {
            case GameMode.NORMAL_EASY: return "普通模式-轻松";
            case GameMode.NORMAL_STANDARD: return "普通模式-标准";
            case GameMode.NORMAL_HARD: return "普通模式-困难";
            case GameMode.CHALLENGE_WIND: return "挑战模式-大风吹";
            case GameMode.CHALLENGE_FOG: return "挑战模式-大雾起";
            case GameMode.CHALLENGE_SNOW: return "挑战模式-大雪飘";
            default: return "未知模式";
        }
    }

    // 根据难度和挑战模式确定游戏模式
    public static determineGameMode(difficulty: number, challengeMode: number): GameMode {
        // 🔧 修复：正确处理NaN值
        // console.log(`🔍 [determineGameMode] 输入参数 - 难度:${difficulty}(${typeof difficulty}), 挑战:${challengeMode}(${typeof challengeMode})`);

        // 处理NaN和无效值
        const safeDifficulty = isNaN(difficulty) ? 1 : difficulty; // 默认标准难度
        const safeChallengeMode = isNaN(challengeMode) ? 0 : challengeMode; // 默认无挑战模式

        // console.log(`🔍 [determineGameMode] 安全参数 - 难度:${safeDifficulty}, 挑战:${safeChallengeMode}`);

        // 如果是挑战模式（必须是有效的非零值）
        if (safeChallengeMode > 0 && !isNaN(safeChallengeMode)) {
            switch(safeChallengeMode) {
                case 1:
                    // console.log(`🔍 [determineGameMode] 返回: CHALLENGE_WIND`);
                    return GameMode.CHALLENGE_WIND; // 大风吹
                case 2:
                    // console.log(`🔍 [determineGameMode] 返回: CHALLENGE_FOG`);
                    return GameMode.CHALLENGE_FOG;  // 大雾起
                case 3:
                    // console.log(`🔍 [determineGameMode] 返回: CHALLENGE_SNOW`);
                    return GameMode.CHALLENGE_SNOW; // 大雪飘
                default:
                    console.log(`🔍 [determineGameMode] 无效挑战模式${safeChallengeMode}，使用标准模式`);
                    return GameMode.NORMAL_STANDARD;
            }
        }

        // 普通模式根据难度确定
        switch(safeDifficulty) {
            case 0:
                // console.log(`🔍 [determineGameMode] 返回: NORMAL_EASY`);
                return GameMode.NORMAL_EASY;     // 轻松
            case 1:
                // console.log(`🔍 [determineGameMode] 返回: NORMAL_STANDARD`);
                return GameMode.NORMAL_STANDARD; // 标准
            case 2:
                // console.log(`🔍 [determineGameMode] 返回: NORMAL_HARD`);
                return GameMode.NORMAL_HARD;     // 困难
            default:
                console.log(`🔍 [determineGameMode] 无效难度${safeDifficulty}，使用标准模式`);
                return GameMode.NORMAL_STANDARD;
        }
    }

    // 获取指定模式的最高分
    public static getBestScore(mode?: GameMode): number {
        const gameMode = mode !== undefined ? mode : this._currentGameMode;
        const key = this.BESTSCORE_PREFIX + gameMode;
        let score = localStorage.getItem(key);
        if(score){
            return parseInt(score);
        }else{
            return 0;
        }
    }

    // 设置指定模式的最高分（云数据库同步用）
    public static setBestScore(mode: GameMode, score: number): void {
        const key = this.BESTSCORE_PREFIX + mode;
        localStorage.setItem(key, score.toString());
        console.log(`设置${this.getGameModeName(mode)}最高分为: ${score}`);
    }

    // 获取指定模式的历史前三高分
    public static getTopScores(mode?: GameMode): number[] {
        const gameMode = mode !== undefined ? mode : this._currentGameMode;
        const key = this.TOP_SCORES_PREFIX + gameMode;
        let topScoresStr = localStorage.getItem(key);

        if (topScoresStr) {
            try {
                const scores = JSON.parse(topScoresStr);
                if (Array.isArray(scores)) {
                    // 🔧 修复：确保数组总是包含3个有效数字
                    const validScores = scores
                        .filter(score => typeof score === 'number' && !isNaN(score))
                        .slice(0, 3); // 只取前3个

                    // 补齐到3个元素
                    while (validScores.length < 3) {
                        validScores.push(0);
                    }

                    return validScores;
                }
            } catch (error) {
                console.error(`GameData: 解析topScores失败 - ${key}:`, error);
            }
        }

        return [0, 0, 0]; // 默认值为三个0
    }

    // 保存分数并更新历史前三高分
    public static saveScore(mode?: GameMode){
        const gameMode = mode !== undefined ? mode : this._currentGameMode;
        let curScore = this.getScore();
        let bestScore = this.getBestScore(gameMode);

        // 🔧 调试日志：详细记录保存过程
        console.log(`🔍 [saveScore] 开始保存分数:`);
        console.log(`  - 传入模式参数: ${mode !== undefined ? mode : 'undefined'}`);
        console.log(`  - 实际使用模式: ${gameMode} (${this.getGameModeName(gameMode)})`);
        console.log(`  - 当前分数: ${curScore}`);
        console.log(`  - 当前最高分: ${bestScore}`);
        console.log(`  - 存储键: BestScore_Mode_${gameMode}, TopScores_Mode_${gameMode}`);

        // 保存最高分
        let isNewRecord = false;
        if(curScore > bestScore){
            const bestScoreKey = this.BESTSCORE_PREFIX + gameMode;
            localStorage.setItem(bestScoreKey, curScore.toString());
            isNewRecord = true;
            console.log(`  - ✅ 新纪录！更新最高分: ${curScore}`);
        } else {
            console.log(`  - 📊 未破纪录，保持最高分: ${bestScore}`);
        }

        // 更新历史前三高分
        let topScores = this.getTopScores(gameMode);
        console.log(`  - 保存前的前三分: [${topScores.join(', ')}]`);

        topScores.push(curScore);
        // 排序（降序）
        topScores.sort((a, b) => b - a);
        // 只保留前三名
        topScores = topScores.slice(0, 3);

        // 保存到本地存储
        const topScoresKey = this.TOP_SCORES_PREFIX + gameMode;
        localStorage.setItem(topScoresKey, JSON.stringify(topScores));

        console.log(`  - 保存后的前三分: [${topScores.join(', ')}]`);
        console.log(`  - ✅ 分数保存完成`);

        console.log(`保存${this.getGameModeName(gameMode)}分数: ${curScore}, 最高分: ${Math.max(curScore, bestScore)}`);

        // 如果创造了新纪录，同步更新排行榜数据
        if (isNewRecord) {
            this.updateLeaderboardData(gameMode, curScore);
        }

        // 🔧 验证保存结果
        const verifyBest = this.getBestScore(gameMode);
        const verifyTop = this.getTopScores(gameMode);
        console.log(`🔍 [验证] ${this.getGameModeName(gameMode)} - 最高分: ${verifyBest}, 前三分: [${verifyTop.join(', ')}]`);
    }

    // 更新排行榜数据（当创造新纪录时调用）
    private static updateLeaderboardData(mode: GameMode, newScore: number): void {
        try {
            // 动态导入WeChatFriendsData，避免循环引用
            import('./Data/WeChatFriendsData').then(module => {
                const WeChatFriendsData = module.WeChatFriendsData;
                if (WeChatFriendsData && WeChatFriendsData.instance) {
                    WeChatFriendsData.instance.updatePlayerScore(mode, newScore);
                    console.log(`GameData: 已同步更新排行榜数据 - 模式: ${this.getGameModeName(mode)}, 新分数: ${newScore}`);
                } else {
                    console.warn("GameData: 无法获取WeChatFriendsData实例，跳过排行榜数据更新");
                }
            }).catch(error => {
                console.error("GameData: 更新排行榜数据失败", error);
            });
        } catch (error) {
            console.error("GameData: 导入WeChatFriendsData失败", error);
        }
    }

    // 获取当前分数在历史前三高分中的排名（0-2表示前三名，-1表示未进入前三）
    public static getCurrentRank(mode?: GameMode): number {
        const gameMode = mode !== undefined ? mode : this._currentGameMode;
        let curScore = this.getScore();
        let topScores = this.getTopScores(gameMode);

        // 创建一个临时数组，包含当前分数和历史前三高分
        let tempScores = [...topScores];
        // 检查当前分数是否已经在数组中
        let found = false;
        for (let i = 0; i < tempScores.length; i++) {
            if (tempScores[i] === curScore) {
                found = true;
                break;
            }
        }
        if (!found) {
            tempScores.push(curScore);
        }

        // 排序（降序）
        tempScores.sort((a, b) => b - a);

        // 找出当前分数在排序后数组中的位置
        const index = tempScores.indexOf(curScore);

        // 如果当前分数在前三名内，返回其排名
        if (index < 3) {
            return index; // 返回排名（0, 1, 2分别对应金、银、铜）
        }

        return -1; // 未进入前三
    }

    public static resetScore(){
        this._score = 0;
    }

    // 复活相关方法
    /**
     * 设置复活状态并保存当前游戏数据
     */
    public static setReviveState(score: number, sessionCoins: number): void {
        this._isRevived = true;
        this._revivedScore = score;
        this._revivedSessionCoins = sessionCoins;
        console.log(`设置复活状态: 分数=${score}, 本局金币=${sessionCoins}`);
    }

    /**
     * 使用复活币（增加复活次数）
     */
    public static useReviveCoin(): void {
        this._sessionReviveCount++;
        this._reviveCoinUsed = true;
        console.log(`使用复活币，本局复活次数: ${this._sessionReviveCount}`);
    }



    /**
     * 检查是否处于复活状态
     */
    public static isRevived(): boolean {
        return this._isRevived;
    }

    /**
     * 获取复活时的分数
     */
    public static getRevivedScore(): number {
        return this._revivedScore;
    }

    /**
     * 获取复活时的本局金币数
     */
    public static getRevivedSessionCoins(): number {
        return this._revivedSessionCoins;
    }

    /**
     * 应用复活状态（恢复分数和金币）
     */
    public static applyReviveState(): void {
        if (this._isRevived || this._isPenguinSpecialRevive || this._isAdRevive) {
            this._score = this._revivedScore;
            this._sessionCoins = this._revivedSessionCoins;
            let reviveType = "复活币复活";
            if (this._isPenguinSpecialRevive) {
                reviveType = "企鹅特殊复活";
            } else if (this._isAdRevive) {
                reviveType = "广告复活";
            }
            console.log(`应用${reviveType}状态: 恢复分数=${this._score}, 恢复本局金币=${this._sessionCoins}`);
        }
    }

    /**
     * 清除复活状态
     */
    public static clearReviveState(): void {
        this._isRevived = false;
        this._revivedScore = 0;
        this._revivedSessionCoins = 0;
        console.log("清除复活状态");
    }

    /**
     * 清除广告复活状态
     */
    public static clearAdReviveState(): void {
        this._isAdRevive = false;
        console.log("清除广告复活状态");
    }

    /**
     * 检查是否可以复活（复活次数是否已达上限）
     */
    public static canRevive(): boolean {
        return this._sessionReviveCount < this.MAX_REVIVE_PER_SESSION;
    }

    /**
     * 检查复活币是否可以使用
     */
    public static canUseReviveCoin(): boolean {
        return !this._reviveCoinUsed && this.canRevive();
    }

    /**
     * 检查广告复活是否可以使用
     */
    public static canUseAdRevive(): boolean {
        return !this._adReviveUsed && this.canRevive();
    }

    /**
     * 获取本局复活次数
     */
    public static getSessionReviveCount(): number {
        return this._sessionReviveCount;
    }

    /**
     * 重置本局复活次数（新游戏开始时调用）
     */
    public static resetSessionReviveCount(): void {
        this._sessionReviveCount = 0;
        console.log("重置本局复活次数");
    }

    // 企鹅特殊复活相关方法
    /**
     * 检查企鹅是否可以使用特殊复活
     */
    public static canPenguinSpecialRevive(): boolean {
        return this._selectedBirdType === BirdType.PENGUIN && !this._penguinUsedSpecialRevive && this.canRevive();
    }

    /**
     * 设置企鹅特殊复活状态
     */
    public static setPenguinSpecialReviveState(score: number, sessionCoins: number): void {
        this._isPenguinSpecialRevive = true;
        this._penguinUsedSpecialRevive = true;
        this._penguinReviveUsed = true;
        this._sessionReviveCount++;
        this._revivedScore = score;
        this._revivedSessionCoins = sessionCoins;
        console.log(`企鹅特殊复活: 分数=${score}, 本局金币=${sessionCoins}, 本局复活次数: ${this._sessionReviveCount}`);
    }

    /**
     * 检查是否是企鹅特殊复活状态
     */
    public static isPenguinSpecialRevive(): boolean {
        return this._isPenguinSpecialRevive;
    }

    /**
     * 清除企鹅特殊复活状态
     */
    public static clearPenguinSpecialReviveState(): void {
        this._isPenguinSpecialRevive = false;
        console.log("清除企鹅特殊复活状态");
    }

    /**
     * 重置企鹅特殊复活使用状态（新局游戏时调用）
     */
    public static resetPenguinSpecialRevive(): void {
        this._penguinUsedSpecialRevive = false;
        this._isPenguinSpecialRevive = false;
        this._penguinReviveUsed = false;
        console.log("重置企鹅特殊复活状态");
    }

    /**
     * 设置广告复活状态
     */
    public static setAdReviveState(score: number, sessionCoins: number): void {
        this._isAdRevive = true;
        this._adReviveUsed = true;
        this._sessionReviveCount++;
        this._revivedScore = score;
        this._revivedSessionCoins = sessionCoins;
        console.log(`广告复活: 分数=${score}, 本局金币=${sessionCoins}, 本局复活次数: ${this._sessionReviveCount}`);
    }

    /**
     * 检查是否是广告复活状态
     */
    public static isAdRevive(): boolean {
        return this._isAdRevive;
    }

    /**
     * 清除广告复活状态
     */
    public static clearAdReviveState(): void {
        this._isAdRevive = false;
        console.log("清除广告复活状态");
    }

    /**
     * 获取企鹅是否已使用特殊复活
     */
    public static hasPenguinUsedSpecialRevive(): boolean {
        return this._penguinUsedSpecialRevive;
    }

    /**
     * 获取广告复活是否已使用
     */
    public static hasAdReviveUsed(): boolean {
        return this._adReviveUsed;
    }

    // 获取总金币数
    public static getTotalCoins(): number {
        let coins = localStorage.getItem(this.TOTAL_COINS);
        if (coins) {
            return parseInt(coins);
        } else {
            return 0;
        }
    }

    // 获取金币数（云数据库同步用）
    public static getCoin(): number {
        return this.getTotalCoins();
    }

    // 设置金币数（云数据库同步用）
    public static setCoin(amount: number): void {
        localStorage.setItem(this.TOTAL_COINS, amount.toString());
        console.log(`设置总金币为: ${amount}`);
    }

    // 添加金币
    public static addCoin(count: number = 1) {
        this._sessionCoins += count;

        // 更新总金币数并保存到本地存储
        const totalCoins = this.getTotalCoins() + count;
        localStorage.setItem(this.TOTAL_COINS, totalCoins.toString());

        // 输出到控制台
        console.log(`收集到金币! 本局金币: ${this._sessionCoins}, 总金币: ${totalCoins}`);
    }

    // 获取本局最终金币数（应用道具效果后）
    public static getFinalSessionCoins(): number {
        // 使用全局变量来获取倍数，避免循环引用
        const multiplier = this.getCurrentCoinMultiplier();
        const finalCoins = this._sessionCoins * multiplier;
        // 四舍五入到整数
        return Math.round(finalCoins);
    }

    // 获取当前金币倍数
    public static getCurrentCoinMultiplier(): number {
        // 从localStorage直接读取激活状态，避免循环引用
        const isDoubleCoinActive = localStorage.getItem("Item_Active_0") === "true";
        const isLuckyDiceActive = localStorage.getItem("Item_Active_3") === "true";

        let multiplier = 1;

        // 关卡基础倍率
        const levelMultiplier = this.getLevelBaseMultiplier();
        multiplier *= levelMultiplier;

        // 幸运骰子倍率（如果激活）
        if (isLuckyDiceActive) {
            const diceMultiplier = this.getLuckyDiceMultiplier();
            multiplier *= diceMultiplier;
        }

        // 金色小鸟倍率
        const goldBirdMultiplier = this.getGoldBirdMultiplier();
        multiplier *= goldBirdMultiplier;

        // 双倍金币卡倍率
        if (isDoubleCoinActive) {
            multiplier *= 2;
        }

        return multiplier;
    }

    // 获取幸运骰子倍率（从localStorage读取）
    public static getLuckyDiceMultiplier(): number {
        const savedMultiplier = localStorage.getItem("LuckyDice_CurrentMultiplier");
        if (savedMultiplier) {
            return parseInt(savedMultiplier);
        }
        return 1; // 如果没有保存的倍率，返回1（表示还没有生成）
    }

    // 设置幸运骰子倍率（游戏结束时生成并保存）
    public static setLuckyDiceMultiplier(multiplier: number): void {
        localStorage.setItem("LuckyDice_CurrentMultiplier", multiplier.toString());
        console.log(`设置幸运骰子倍率: ${multiplier}`);
    }

    // 清除幸运骰子倍率（新游戏开始时调用）
    public static clearLuckyDiceMultiplier(): void {
        localStorage.removeItem("LuckyDice_CurrentMultiplier");
    }

    // 获取关卡基础倍率
    public static getLevelBaseMultiplier(): number {
        const currentMode = this.getCurrentGameMode();

        switch (currentMode) {
            case GameMode.NORMAL_EASY:
                return 1.0; // 轻松难度
            case GameMode.NORMAL_STANDARD:
                return 1.2; // 标准难度
            case GameMode.NORMAL_HARD:
                return 1.5; // 困难难度
            case GameMode.CHALLENGE_WIND:
            case GameMode.CHALLENGE_FOG:
            case GameMode.CHALLENGE_SNOW:
                return 2.0; // 挑战模式
            default:
                return 1.0;
        }
    }

    // 获取金色小鸟倍率
    public static getGoldBirdMultiplier(): number {
        const selectedBirdType = this.getSelectedBirdType();
        // BirdType.GOLD = 1，金色小鸟提供1.5倍金币倍率
        return selectedBirdType === BirdType.GOLD ? 1.5 : 1.0;
    }

    // 获取分离的倍率信息（用于UI显示）
    public static getMultiplierInfo(): {levelBase: number, luckyDice: number, goldBird: number, doubleCoin: number, total: number} {
        const isDoubleCoinActive = localStorage.getItem("Item_Active_0") === "true";
        const isLuckyDiceActive = localStorage.getItem("Item_Active_3") === "true";

        const levelBaseMultiplier = this.getLevelBaseMultiplier();
        const luckyDiceMultiplier = isLuckyDiceActive ? this.getLuckyDiceMultiplier() : 1;
        const goldBirdMultiplier = this.getGoldBirdMultiplier();
        const doubleCoinMultiplier = isDoubleCoinActive ? 2 : 1;

        const totalMultiplier = levelBaseMultiplier * luckyDiceMultiplier * goldBirdMultiplier * doubleCoinMultiplier;
        // 四舍五入到两位小数，避免浮点数精度问题
        const roundedTotal = Math.round(totalMultiplier * 100) / 100;

        return {
            levelBase: levelBaseMultiplier,
            luckyDice: luckyDiceMultiplier,
            goldBird: goldBirdMultiplier,
            doubleCoin: doubleCoinMultiplier,
            total: roundedTotal
        };
    }

    // 应用道具效果到总金币（游戏结束时调用）
    public static applyItemEffectsToTotalCoins(): void {
        const finalSessionCoins = this.getFinalSessionCoins();
        const originalSessionCoins = this._sessionCoins;

        if (finalSessionCoins !== originalSessionCoins) {
            // 有道具效果，需要补偿差额到总金币
            const bonus = finalSessionCoins - originalSessionCoins;
            const currentTotal = this.getTotalCoins();
            const newTotal = currentTotal + bonus;
            localStorage.setItem(this.TOTAL_COINS, newTotal.toString());

            console.log(`应用道具效果: 原始金币=${originalSessionCoins}, 最终金币=${finalSessionCoins}, 总金币=${newTotal}`);
        }
    }

    // 撤销道具效果（复活时调用）
    public static revokeItemEffectsFromTotalCoins(): void {
        const finalSessionCoins = this.getFinalSessionCoins();
        const originalSessionCoins = this._sessionCoins;

        if (finalSessionCoins !== originalSessionCoins) {
            // 有道具效果，需要从总金币中减去最终金币数，但要加回原始金币数
            const currentTotal = this.getTotalCoins();
            const newTotal = currentTotal - finalSessionCoins + originalSessionCoins;
            localStorage.setItem(this.TOTAL_COINS, newTotal.toString());

            console.log(`撤销道具效果: 减去最终金币=${finalSessionCoins}, 加回原始金币=${originalSessionCoins}, 全局金币=${newTotal}`);
        }
    }

    // 获取本局收集的金币数
    public static getSessionCoins(): number {
        return this._sessionCoins;
    }

    // 重置本局金币数（游戏开始时调用）
    public static resetSessionCoins() {
        this._sessionCoins = 0;
        // 清除上一局的幸运骰子倍率
        this.clearLuckyDiceMultiplier();
    }

    /**
     * 🔧 新增：立即同步金币到云端（商店购买时调用）
     * 防止用户通过场景切换钻购买漏洞
     */
    public static syncCoinsToCloudImmediately(coins: number): void {
        try {
            // 动态导入GameDataManager，避免循环引用
            import('./Data/GameDataManager').then(({ GameDataManager }) => {
                const gameDataManager = GameDataManager.instance;
                if (gameDataManager && gameDataManager.isInitialized) {
                    // 立即同步金币到云端
                    gameDataManager.syncCoinsImmediately(coins, 'set').then(success => {
                        if (success) {
                            console.log(`GameData: ✅ 购买后金币已立即同步到云端: ${coins}`);
                        } else {
                            console.error(`GameData: ❌ 购买后金币同步失败: ${coins}`);
                        }
                    }).catch(error => {
                        console.error('GameData: 同步金币到云端时出错', error);
                    });
                } else {
                    console.warn('GameData: GameDataManager未初始化，跳过金币同步');
                }
            }).catch(error => {
                console.error('GameData: 导入GameDataManager失败', error);
            });
        } catch (error) {
            console.error('GameData: 立即同步金币失败', error);
        }
    }

    /**
     * 重置新局游戏状态（包括企鹅特殊复活）
     */
    public static resetNewGameSession(): void {
        this.resetSessionCoins();
        this.resetSessionReviveCount();
        this.resetPenguinSpecialRevive();

        // 重置所有复活状态
        this._reviveCoinUsed = false;
        this._penguinReviveUsed = false;
        this._adReviveUsed = false;
        this._isAdRevive = false;
        this._isRevived = false;
        this._revivedScore = 0;
        this._revivedSessionCoins = 0;

        console.log("重置新局游戏状态");
    }

    // 获取当前选择的小鸟类型
    public static getSelectedBirdType(): BirdType {
        const savedType = localStorage.getItem(this.SELECTED_BIRD);
        if (savedType !== null) {
            return parseInt(savedType);
        }
        return this._selectedBirdType; // 返回默认值
    }

    // 设置选择的小鸟类型
    public static setSelectedBirdType(type: BirdType) {
        this._selectedBirdType = type;
        // 保存到本地存储
        localStorage.setItem(this.SELECTED_BIRD, type.toString());
        console.log(`选择了小鸟类型: ${type}`);
    }

    // 小鸟相关方法
    // 获取已购买的小鸟列表
    public static getPurchasedBirds(): BirdType[] {
        const purchased = localStorage.getItem(this.PURCHASED_BIRDS);
        if (purchased) {
            return JSON.parse(purchased);
        } else {
            // 默认拥有普通小鸟
            return [BirdType.NORMAL];
        }
    }

    // 检查小鸟是否已购买
    public static isBirdPurchased(birdType: BirdType): boolean {
        const purchased = this.getPurchasedBirds();
        return purchased.indexOf(birdType) !== -1;
    }

    // 获取小鸟价格配置
    public static getBirdPrice(birdType: BirdType): number {
        const prices = {
            [BirdType.NORMAL]: 0,      // 普通小鸟免费
            [BirdType.GOLD]: 3000,      // 金色小鸟
            [BirdType.PENGUIN]: 5000,   // 企鹅
            [BirdType.ALBATROSS]: 8000, // 信天翁
            [BirdType.WOODPECKER]: 15000 // 啄木鸟
        };
        return prices[birdType] || 0;
    }

    // 购买小鸟
    public static purchaseBird(birdType: BirdType): boolean {
        // 普通小鸟默认拥有，不需要购买
        if (birdType === BirdType.NORMAL) {
            console.log("普通小鸟默认拥有，无需购买");
            return true;
        }

        // 检查是否已经拥有
        if (this.isBirdPurchased(birdType)) {
            console.log(`小鸟 ${birdType} 已经拥有，无需重复购买`);
            return false;
        }

        const price = this.getBirdPrice(birdType);
        const totalCoins = this.getTotalCoins();

        if (totalCoins >= price) {
            // 扣除金币
            const newTotal = totalCoins - price;
            localStorage.setItem(this.TOTAL_COINS, newTotal.toString());

            // 添加到已购买列表
            const purchased = this.getPurchasedBirds();
            purchased.push(birdType);
            localStorage.setItem(this.PURCHASED_BIRDS, JSON.stringify(purchased));

            console.log(`成功购买小鸟 ${birdType}，花费 ${price} 金币，剩余 ${newTotal} 金币`);

            // 🔧 新增：立即同步金币到云端，防止购买漏洞
            this.syncCoinsToCloudImmediately(newTotal);

            return true;
        } else {
            console.log(`金币不足，无法购买小鸟 ${birdType}，需要 ${price} 金币，当前只有 ${totalCoins} 金币`);
            return false;
        }
    }

    // 获取小鸟名称
    public static getBirdName(birdType: BirdType): string {
        const names = {
            [BirdType.NORMAL]: "普通小鸟",
            [BirdType.GOLD]: "金色小鸟",
            [BirdType.PENGUIN]: "企鹅",
            [BirdType.ALBATROSS]: "信天翁",
            [BirdType.WOODPECKER]: "啄木鸟"
        };
        return names[birdType] || "未知小鸟";
    }

    // 背景相关方法
    // 获取已购买的背景列表
    public static getPurchasedBackgrounds(): number[] {
        const purchased = localStorage.getItem(this.PURCHASED_BACKGROUNDS);
        if (purchased) {
            return JSON.parse(purchased);
        } else {
            // 默认拥有草原背景（背景1）
            return [1];
        }
    }

    // 购买背景
    public static purchaseBackground(backgroundId: number, price: number): boolean {
        const totalCoins = this.getTotalCoins();
        if (totalCoins >= price) {
            // 扣除金币
            const newTotal = totalCoins - price;
            localStorage.setItem(this.TOTAL_COINS, newTotal.toString());

            // 添加到已购买列表
            const purchased = this.getPurchasedBackgrounds();
            if (purchased.indexOf(backgroundId) === -1) {
                purchased.push(backgroundId);
                localStorage.setItem(this.PURCHASED_BACKGROUNDS, JSON.stringify(purchased));
            }

            console.log(`成功购买背景 ${backgroundId}，花费 ${price} 金币，剩余 ${newTotal} 金币`);

            // 🔧 新增：立即同步金币到云端，防止购买漏洞
            this.syncCoinsToCloudImmediately(newTotal);

            return true;
        } else {
            console.log(`金币不足，无法购买背景 ${backgroundId}，需要 ${price} 金币，当前只有 ${totalCoins} 金币`);
            return false;
        }
    }

    // 检查背景是否已购买
    public static isBackgroundPurchased(backgroundId: number): boolean {
        const purchased = this.getPurchasedBackgrounds();
        return purchased.indexOf(backgroundId) !== -1;
    }

    // 获取当前选择的背景
    public static getSelectedBackground(): number {
        const selected = localStorage.getItem(this.SELECTED_BACKGROUND);
        if (selected) {
            return parseInt(selected);
        } else {
            return 1; // 默认选择草原背景
        }
    }

    // 设置选择的背景
    public static setSelectedBackground(backgroundId: number) {
        localStorage.setItem(this.SELECTED_BACKGROUND, backgroundId.toString());
        console.log(`选择了背景: ${backgroundId}`);
    }

    // 重置背景购买状态（仅用于测试）
    public static resetBackgroundPurchases() {
        localStorage.removeItem(this.PURCHASED_BACKGROUNDS);
        localStorage.removeItem(this.SELECTED_BACKGROUND);
        console.log("背景购买状态已重置，只保留草原背景");
    }

    // 初始化游戏记录（仅在首次运行时）
    private static initializeGameRecordsIfNeeded() {
        // 检查是否已经初始化过
        const initFlag = localStorage.getItem("GameRecords_Initialized");

        if (!initFlag) {
            console.log("首次运行，初始化游戏记录为0");
            // 初始化所有模式的记录为0
            for (let mode = 0; mode <= 5; mode++) {
                const bestScoreKey = this.BESTSCORE_PREFIX + mode;
                const topScoresKey = this.TOP_SCORES_PREFIX + mode;

                // 只有在记录不存在时才设置为0
                if (!localStorage.getItem(bestScoreKey)) {
                    localStorage.setItem(bestScoreKey, "0");
                }
                if (!localStorage.getItem(topScoresKey)) {
                    localStorage.setItem(topScoresKey, JSON.stringify([0, 0, 0]));
                }
            }

            // 设置初始化标志
            localStorage.setItem("GameRecords_Initialized", "true");
            console.log("游戏记录初始化完成");
        } else {
            console.log("游戏记录已存在，保持现有记录");
        }
    }

    // 重置所有游戏模式的记录（用于测试）
    public static resetAllGameRecords() {
        for (let mode = 0; mode <= 5; mode++) {
            const bestScoreKey = this.BESTSCORE_PREFIX + mode;
            const topScoresKey = this.TOP_SCORES_PREFIX + mode;

            // 将最高分设为0
            localStorage.setItem(bestScoreKey, "0");
            // 将前三高分设为[0, 0, 0]
            localStorage.setItem(topScoresKey, JSON.stringify([0, 0, 0]));
        }
        console.log("所有游戏模式的记录已重置为0");
    }

    // 完全清除游戏记录系统（包括初始化标志）
    public static clearAllGameRecords() {
        // 清除所有模式的记录
        for (let mode = 0; mode <= 5; mode++) {
            const bestScoreKey = this.BESTSCORE_PREFIX + mode;
            const topScoresKey = this.TOP_SCORES_PREFIX + mode;
            localStorage.removeItem(bestScoreKey);
            localStorage.removeItem(topScoresKey);
        }

        // 清除初始化标志
        localStorage.removeItem("GameRecords_Initialized");
        console.log("所有游戏记录已完全清除，下次启动将重新初始化");
    }

    // 获取所有游戏模式的记录（用于调试）
    public static getAllGameRecords(): {[key: string]: any} {
        const records: {[key: string]: any} = {};

        for (let mode = 0; mode <= 5; mode++) {
            const modeName = this.getGameModeName(mode as GameMode);
            records[modeName] = {
                bestScore: this.getBestScore(mode as GameMode),
                topScores: this.getTopScores(mode as GameMode)
            };
        }

        return records;
    }

    // 打印所有游戏模式的记录（用于调试）
    public static printAllGameRecords() {
        console.log("=== 所有游戏模式记录 ===");
        const records = this.getAllGameRecords();
        // 使用传统的for-in循环替代Object.entries
        for (const modeName in records) {
            if (records.hasOwnProperty(modeName)) {
                const record = records[modeName];
                console.log(`${modeName}: 最高分=${record.bestScore}, 前三高分=[${record.topScores.join(', ')}]`);
            }
        }
        console.log("========================");
    }
}
