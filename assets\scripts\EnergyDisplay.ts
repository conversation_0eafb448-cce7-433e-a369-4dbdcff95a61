import { _decorator, Component, Label } from 'cc';
import { EnergyManager } from './EnergyManager';
const { ccclass, property } = _decorator;

/**
 * 体力值显示组件，负责在UI上显示体力值和恢复时间
 */
@ccclass('EnergyDisplay')
export class EnergyDisplay extends Component {
    @property(Label)
    energyCountLabel: Label = null;

    @property(Label)
    energyRecoverLabel: Label = null;

    // 更新计时器
    private _updateTimer: number = 0;
    // 更新间隔（秒）- 使用更短的间隔以便精确捕捉00:01
    private readonly UPDATE_INTERVAL: number = 0.1;

    start() {
        // 确保Label组件已设置
        if (!this.energyCountLabel) {
            console.error("EnergyDisplay错误: 未找到energyCountLabel组件!");
            return;
        }

        if (!this.energyRecoverLabel) {
            console.error("EnergyDisplay错误: 未找到energyRecoverLabel组件!");
            return;
        }

        // 初始化显示
        this.updateDisplay();

        // 注册体力值变化的回调
        const energyManager = EnergyManager.getInstance();
        if (energyManager) {
            energyManager.addEnergyChangeCallback(this.updateDisplay.bind(this));
        } else {
            console.error("EnergyDisplay错误: 未找到EnergyManager实例!");
        }
        
        // 使用更频繁的定时器来确保UI更新
        this.schedule(this.forceUpdateDisplay, 0.5);
    }

    onDestroy() {
        // 移除回调
        const energyManager = EnergyManager.getInstance();
        if (energyManager) {
            energyManager.removeEnergyChangeCallback(this.updateDisplay.bind(this));
        }
        
        // 取消定时器
        this.unschedule(this.forceUpdateDisplay);
    }

    update(deltaTime: number) {
        // 累加计时器
        this._updateTimer += deltaTime;

        // 每隔一定时间更新一次显示
        if (this._updateTimer >= this.UPDATE_INTERVAL) {
            this._updateTimer = 0;
            this.updateDisplay();
        }
    }

    /**
     * 更新体力值和恢复时间显示
     */
    public updateDisplay() {
        const energyManager = EnergyManager.getInstance();
        if (!energyManager) {
            console.warn("EnergyDisplay: EnergyManager实例不存在");
            return;
        }

        // 更新体力值显示
        if (this.energyCountLabel) {
            const currentEnergy = energyManager.getCurrentEnergy();
            // 检查并处理NaN值
            if (isNaN(currentEnergy) || !isFinite(currentEnergy)) {
                console.error("EnergyDisplay: 检测到无效的体力值:", currentEnergy);
                this.energyCountLabel.string = "100"; // 显示默认值
            } else {
                this.energyCountLabel.string = `${currentEnergy}`;
            }
        }

        // 更新恢复时间显示
        if (this.energyRecoverLabel) {
            const currentEnergy = energyManager.getCurrentEnergy();
            const maxEnergy = energyManager.getMaxEnergy();

            // 如果体力已满，隐藏恢复时间
            if (currentEnergy >= maxEnergy) {
                this.energyRecoverLabel.node.active = false;
            } else {
                // 显示恢复时间
                this.energyRecoverLabel.node.active = true;

                // 获取距离下一次恢复的时间
                const timeUntilNextRecover = energyManager.getTimeUntilNextRecover();

                // 检查并处理NaN值
                if (isNaN(timeUntilNextRecover) || !isFinite(timeUntilNextRecover)) {
                    console.error("EnergyDisplay: 检测到无效的恢复时间:", timeUntilNextRecover);
                    this.energyRecoverLabel.string = "(05:00)"; // 显示默认恢复时间
                    return;
                }

                // 新需求：让倒计时正常从00:01到00:00，然后00:00立即跳转到XX:00
                let formattedTime: string;
                // 计算秒数
                const totalSeconds = Math.round(timeUntilNextRecover / 1000);
                const minutes = Math.floor(totalSeconds / 60);
                const seconds = totalSeconds % 60;

                // 输出调试信息
                //console.log(`倒计时: ${minutes}:${seconds < 10 ? '0' + seconds : seconds}, 毫秒: ${timeUntilNextRecover}, 当前能量: ${currentEnergy}/${maxEnergy}`);

                // 判断是否为00:00（0秒或接近0秒）
                if (totalSeconds <= 0) {
                    // 当倒计时到00:00时
                    if (currentEnergy === maxEnergy - 1) {
                        // 这是最后一点体力，将恢复满，应该隐藏倒计时
                        // 强制触发一次体力恢复检查
                        energyManager.forceCheckEnergyRecover();
                        this.energyRecoverLabel.node.active = false;
                        return;
                    } else {
                        // 不是最后一点体力，强制触发体力恢复并显示下一个周期
                        // 强制触发一次体力恢复检查
                        energyManager.forceCheckEnergyRecover();

                        // 重新获取当前体力值（可能已经恢复了一点）
                        const updatedEnergy = energyManager.getCurrentEnergy();
                        
                        // 获取更新后的恢复时间
                        const updatedTimeUntilNextRecover = energyManager.getTimeUntilNextRecover();
                        
                        // 检查体力恢复是否成功触发
                        if (updatedEnergy > currentEnergy || updatedTimeUntilNextRecover > 0) {
                            // 体力已恢复或恢复时间已更新，使用新的恢复时间
                            formattedTime = EnergyManager.formatTimeRemaining(updatedTimeUntilNextRecover);
                        } else {
                            // 根据RECOVER_MINUTES动态设置下一个周期的时间
                            const minutes = EnergyManager.RECOVER_MINUTES;
                            const minutesStr = minutes < 10 ? "0" + minutes : minutes.toString();
                            formattedTime = `${minutesStr}:00`;
                        }
                    }
                } else {
                    // 正常显示倒计时
                    formattedTime = EnergyManager.formatTimeRemaining(timeUntilNextRecover);
                }

                // 显示格式：(MM:SS)
                this.energyRecoverLabel.string = `(${formattedTime})`;
            }
        }
    }

    /**
     * 强制更新显示
     */
    private forceUpdateDisplay() {
        // 每隔一段时间强制更新一次显示，确保在不同场景中UI都能更新
        this.updateDisplay();
    }
}

