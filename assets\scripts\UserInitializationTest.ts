import { _decorator, Component, Node } from 'cc';
import { WeChatLoginManager } from './WeChatLoginManager';
import { CloudDatabaseManager } from './Data/CloudDatabaseManager';
import { InviteCodeManager } from './InviteCodeManager';

const { ccclass, property } = _decorator;

/**
 * 用户初始化测试
 * 验证用户信息和邀请码的"只初始化一次"逻辑
 */
@ccclass('UserInitializationTest')
export class UserInitializationTest extends Component {

    onLoad() {
        console.log("=== 用户初始化测试开始 ===");
        
        // 延迟执行，确保系统初始化完成
        this.scheduleOnce(() => {
            this.runInitializationTest();
        }, 3.0);
    }

    private async runInitializationTest() {
        console.log("\n🧪 开始用户初始化测试");
        
        // 1. 检查当前登录状态
        await this.checkCurrentLoginStatus();
        
        // 2. 检查云端数据
        await this.checkCloudData();
        
        // 3. 检查邀请码
        await this.checkInviteCode();
        
        // 4. 检查openid问题
        await this.checkOpenIdIssue();
    }

    private async checkCurrentLoginStatus() {
        console.log("\n--- 检查当前登录状态 ---");
        
        const loginManager = WeChatLoginManager.instance;
        const isLoggedIn = loginManager.isLoggedIn();
        const userInfo = loginManager.getUserInfo();
        const openid = loginManager.getOpenid();
        
        console.log(`登录状态: ${isLoggedIn}`);
        console.log(`用户信息:`, userInfo);
        console.log(`OpenID: ${openid}`);
        
        if (userInfo) {
            if (userInfo.nickname === '微信用户') {
                console.log("⚠️ 警告: 使用默认昵称，可能需要重新授权");
            } else {
                console.log("✅ 用户信息完整");
            }
        }
    }

    private async checkCloudData() {
        console.log("\n--- 检查云端数据 ---");
        
        try {
            const cloudDB = CloudDatabaseManager.instance;
            
            // 检查微信上下文
            const wxContext = await cloudDB.getWxContext();
            console.log("微信上下文:", wxContext);
            
            if (wxContext && wxContext.openid) {
                console.log(`✅ 获取到有效openid: ${wxContext.openid}`);
                
                // 检查是否是固定的测试openid
                if (wxContext.openid === "ozC2t7TTuemXQaaYFCQoKcYcVbUQ") {
                    console.log("⚠️ 警告: 这是固定的测试openid，可能在开发环境中");
                }
            } else {
                console.log("❌ 无法获取有效openid");
            }
            
            // 检查云端用户数据
            const cloudUserData = await cloudDB.getCurrentUserData();
            if (cloudUserData) {
                console.log("云端用户数据:", cloudUserData);
                
                if (cloudUserData.nickname && cloudUserData.nickname !== '微信用户') {
                    console.log("✅ 云端有完整用户信息");
                } else {
                    console.log("⚠️ 云端用户信息不完整");
                }
                
                if (cloudUserData.inviteCode) {
                    console.log(`✅ 云端有邀请码: ${cloudUserData.inviteCode}`);
                } else {
                    console.log("⚠️ 云端没有邀请码");
                }
            } else {
                console.log("❌ 云端没有用户数据");
            }
            
        } catch (error) {
            console.error("检查云端数据失败:", error);
        }
    }

    private async checkInviteCode() {
        console.log("\n--- 检查邀请码 ---");
        
        const currentInviteCode = InviteCodeManager.getPlayerInviteCode();
        console.log(`当前邀请码: ${currentInviteCode}`);
        
        if (currentInviteCode && currentInviteCode !== 'UNKNOWN') {
            console.log("✅ 有有效邀请码");
        } else {
            console.log("⚠️ 没有有效邀请码");
        }
    }

    private async checkOpenIdIssue() {
        console.log("\n--- 检查OpenID问题 ---");
        
        try {
            const cloudDB = CloudDatabaseManager.instance;
            
            // 查询所有玩家数据，检查openid分布
            const allPlayers = await cloudDB.getPlayersData(10);
            if (allPlayers && allPlayers.length > 0) {
                console.log(`找到 ${allPlayers.length} 个玩家记录`);
                
                const openidCounts = {};
                allPlayers.forEach(player => {
                    const openid = player._openid || 'undefined';
                    openidCounts[openid] = (openidCounts[openid] || 0) + 1;
                });
                
                console.log("OpenID分布:", openidCounts);
                
                const uniqueOpenids = Object.keys(openidCounts);
                if (uniqueOpenids.length === 1) {
                    console.log("⚠️ 警告: 所有玩家使用相同的openid，这不正常");
                    console.log(`共同的openid: ${uniqueOpenids[0]}`);
                } else {
                    console.log("✅ 玩家有不同的openid");
                }
            } else {
                console.log("没有找到玩家数据");
            }
            
        } catch (error) {
            console.error("检查OpenID问题失败:", error);
        }
    }

    /**
     * 手动触发重新初始化
     */
    public async manualReinitialize() {
        console.log("\n🔄 手动触发重新初始化");
        
        try {
            const loginManager = WeChatLoginManager.instance;
            
            // 清除本地缓存
            if (typeof localStorage !== 'undefined') {
                localStorage.removeItem('wechat_user_info');
                console.log("已清除本地用户信息缓存");
            }
            
            // 重新初始化
            const success = await loginManager.initialize();
            console.log(`重新初始化结果: ${success}`);
            
            // 检查结果
            await this.checkCurrentLoginStatus();
            
        } catch (error) {
            console.error("手动重新初始化失败:", error);
        }
    }

    /**
     * 强制重新授权
     */
    public async forceReauthorize() {
        console.log("\n🔐 强制重新授权");
        
        try {
            const loginManager = WeChatLoginManager.instance;
            
            // 调用授权登录
            const success = await loginManager.authorizeLogin();
            console.log(`强制授权结果: ${success}`);
            
            // 检查结果
            await this.checkCurrentLoginStatus();
            
        } catch (error) {
            console.error("强制重新授权失败:", error);
        }
    }

    /**
     * 检查数据一致性
     */
    public async checkDataConsistency() {
        console.log("\n🔍 检查数据一致性");
        
        const loginManager = WeChatLoginManager.instance;
        const cloudDB = CloudDatabaseManager.instance;
        
        // 本地数据
        const localUserInfo = loginManager.getUserInfo();
        const localInviteCode = InviteCodeManager.getPlayerInviteCode();
        
        console.log("本地用户信息:", localUserInfo);
        console.log("本地邀请码:", localInviteCode);
        
        // 云端数据
        const cloudUserData = await cloudDB.getCurrentUserData();
        console.log("云端用户数据:", cloudUserData);
        
        // 比较一致性
        if (localUserInfo && cloudUserData) {
            const nicknameMatch = localUserInfo.nickname === cloudUserData.nickname;
            const inviteCodeMatch = localInviteCode === cloudUserData.inviteCode;
            
            console.log(`昵称一致性: ${nicknameMatch}`);
            console.log(`邀请码一致性: ${inviteCodeMatch}`);
            
            if (nicknameMatch && inviteCodeMatch) {
                console.log("✅ 本地和云端数据一致");
            } else {
                console.log("⚠️ 本地和云端数据不一致，可能需要同步");
            }
        }
    }

    /**
     * 手动测试
     */
    public manualTest() {
        this.runInitializationTest();
    }
}
