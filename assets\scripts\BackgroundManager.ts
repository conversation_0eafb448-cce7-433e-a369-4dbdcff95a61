import { _decorator, Component, Node } from 'cc';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
import { GameData } from './GameData';
import { GameDifficulty } from './GameDifficulty';
import { MoveBg } from './MoveBg';
import { PipeSpawner } from './PipeSpawner';
import { Pipe } from './Pipe';
import { MovingPipe } from './MovingPipe';
const { ccclass, property } = _decorator;

/**
 * 背景管理器 - 管理三套可切换的背景
 */
@ccclass('BackgroundManager')
export class BackgroundManager extends Component {

    // 第一套背景（当前默认）
    @property(Node)
    bgSet1: Node = null;
    @property(Node)
    pipeSpawnSet1: Node = null;
    @property(Node)
    landSet1: Node = null;

    // 第二套背景
    @property(Node)
    bgSet2: Node = null;
    @property(Node)
    pipeSpawnSet2: Node = null;
    @property(Node)
    landSet2: Node = null;

    // 第三套背景
    @property(Node)
    bgSet3: Node = null;
    @property(Node)
    pipeSpawnSet3: Node = null;
    @property(Node)
    landSet3: Node = null;

    // 当前激活的背景套装 (1, 2, 3)
    private currentBackgroundSet: number = 1;

    // 静态实例
    private static _instance: BackgroundManager = null;

    public static getInstance(): BackgroundManager {
        return this._instance;
    }

    onLoad() {
        BackgroundManager._instance = this;
    }

    onDestroy() {
        // 清理单例引用
        if (BackgroundManager._instance === this) {
            BackgroundManager._instance = null;
        }

        // 取消所有调度器
        this.unscheduleAllCallbacks();

        console.log("BackgroundManager: 组件已销毁，资源已清理");
    }

    start() {
        // 根据玩家选择的背景进行初始化
        const selectedBackground = GameData.getSelectedBackground();
        this.currentBackgroundSet = selectedBackground;

        // 初始化时只设置背景，不启动移动组件
        this.initializeBackgroundSet(selectedBackground);

        console.log(`BackgroundManager初始化完成，当前背景套装: ${selectedBackground}`);
    }

    /**
     * 初始化背景套装（仅设置显示，不启动移动组件）
     * @param setNumber 背景套装编号 (1, 2, 3)
     */
    private initializeBackgroundSet(setNumber: number): void {
        if (setNumber < 1 || setNumber > 3) {
            console.error(`无效的背景套装编号: ${setNumber}`);
            return;
        }

        console.log(`初始化背景套装 ${setNumber}`);

        this.currentBackgroundSet = setNumber;

        // 隐藏所有背景套装
        this.setBackgroundSetActive(1, false);
        this.setBackgroundSetActive(2, false);
        this.setBackgroundSetActive(3, false);

        // 激活指定的背景套装
        this.setBackgroundSetActive(setNumber, true);

        // 设置MoveBg组件的参数
        this.configureMoveBgParameters(setNumber);

        // 确保所有移动组件都处于停止状态
        this.ensureAllComponentsStopped();

        // 处理已存在的管道
        this.handleExistingPipes();

        // 调整第二、第三套背景的管道生成时机
        this.adjustPipeSpawnerTimingForBackgroundSets();

        console.log(`背景初始化完成，当前套装: ${setNumber}`);
    }

    /**
     * 切换到指定的背景套装
     * @param setNumber 背景套装编号 (1, 2, 3)
     */
    public switchToBackgroundSet(setNumber: number): void {
        if (setNumber < 1 || setNumber > 3) {
            console.error(`无效的背景套装编号: ${setNumber}`);
            return;
        }

        console.log(`切换到背景套装 ${setNumber}`);

        // 先暂停所有管道生成器和移动组件
        this.pauseAllBackgroundSets();

        // 清理所有现有的管道
        this.clearAllPipes();

        this.currentBackgroundSet = setNumber;

        // 隐藏所有背景套装
        this.setBackgroundSetActive(1, false);
        this.setBackgroundSetActive(2, false);
        this.setBackgroundSetActive(3, false);

        // 激活指定的背景套装
        this.setBackgroundSetActive(setNumber, true);

        // 设置MoveBg组件的参数
        this.configureMoveBgParameters(setNumber);

        // 确保切换后所有组件都处于正确的状态
        this.ensureAllComponentsStopped();

        // 处理已存在的管道
        this.handleExistingPipes();

        // 调整第二、第三套背景的管道生成时机
        this.adjustPipeSpawnerTimingForBackgroundSets();

        console.log(`背景切换完成，当前套装: ${setNumber}`);
    }

    /**
     * 设置指定背景套装的激活状态
     * @param setNumber 背景套装编号
     * @param active 是否激活
     */
    private setBackgroundSetActive(setNumber: number, active: boolean): void {
        let bgNode: Node = null;
        let pipeSpawnNode: Node = null;
        let landNode: Node = null;

        switch (setNumber) {
            case 1:
                bgNode = this.bgSet1;
                pipeSpawnNode = this.pipeSpawnSet1;
                landNode = this.landSet1;
                break;
            case 2:
                bgNode = this.bgSet2;
                pipeSpawnNode = this.pipeSpawnSet2;
                landNode = this.landSet2;
                break;
            case 3:
                bgNode = this.bgSet3;
                pipeSpawnNode = this.pipeSpawnSet3;
                landNode = this.landSet3;
                break;
        }

        if (bgNode) bgNode.active = active;
        if (pipeSpawnNode) pipeSpawnNode.active = active;
        if (landNode) landNode.active = active;

        console.log(`背景套装 ${setNumber} ${active ? '激活' : '隐藏'}`);
    }

    /**
     * 获取当前激活的背景套装编号
     */
    public getCurrentBackgroundSet(): number {
        return this.currentBackgroundSet;
    }

    /**
     * 获取当前激活的背景移动组件
     */
    public getCurrentBgMoving(): MoveBg | null {
        switch (this.currentBackgroundSet) {
            case 1:
                return this.bgSet1?.getComponent(MoveBg) || null;
            case 2:
                return this.bgSet2?.getComponent(MoveBg) || null;
            case 3:
                return this.bgSet3?.getComponent(MoveBg) || null;
            default:
                return null;
        }
    }

    /**
     * 获取当前激活的地面移动组件
     */
    public getCurrentLandMoving(): MoveBg | null {
        switch (this.currentBackgroundSet) {
            case 1:
                return this.landSet1?.getComponent(MoveBg) || null;
            case 2:
                return this.landSet2?.getComponent(MoveBg) || null;
            case 3:
                return this.landSet3?.getComponent(MoveBg) || null;
            default:
                return null;
        }
    }

    /**
     * 获取当前激活的管道生成器组件
     */
    public getCurrentPipeSpawner(): PipeSpawner | null {
        switch (this.currentBackgroundSet) {
            case 1:
                return this.pipeSpawnSet1?.getComponent(PipeSpawner) || null;
            case 2:
                return this.pipeSpawnSet2?.getComponent(PipeSpawner) || null;
            case 3:
                return this.pipeSpawnSet3?.getComponent(PipeSpawner) || null;
            default:
                return null;
        }
    }

    /**
     * 循环切换到下一套背景
     */
    public switchToNextBackground(): void {
        const nextSet = (this.currentBackgroundSet % 3) + 1;
        this.switchToBackgroundSet(nextSet);
    }

    /**
     * 循环切换到上一套背景
     */
    public switchToPreviousBackground(): void {
        const prevSet = this.currentBackgroundSet === 1 ? 3 : this.currentBackgroundSet - 1;
        this.switchToBackgroundSet(prevSet);
    }

    /**
     * 暂停所有背景套装的组件
     */
    private pauseAllBackgroundSets(): void {
        // 暂停所有管道生成器
        const pipeSpawners = [
            this.pipeSpawnSet1?.getComponent(PipeSpawner),
            this.pipeSpawnSet2?.getComponent(PipeSpawner),
            this.pipeSpawnSet3?.getComponent(PipeSpawner)
        ];

        pipeSpawners.forEach(spawner => {
            if (spawner) {
                spawner.pause();
            }
        });

        // 暂停所有移动组件
        const bgMovers = [
            this.bgSet1?.getComponent(MoveBg),
            this.bgSet2?.getComponent(MoveBg),
            this.bgSet3?.getComponent(MoveBg)
        ];

        const landMovers = [
            this.landSet1?.getComponent(MoveBg),
            this.landSet2?.getComponent(MoveBg),
            this.landSet3?.getComponent(MoveBg)
        ];

        bgMovers.forEach(mover => {
            if (mover) {
                mover.disableMoving();
            }
        });

        landMovers.forEach(mover => {
            if (mover) {
                mover.disableMoving();
            }
        });

        console.log("已暂停所有背景套装的组件");
    }

    /**
     * 清理所有管道
     */
    private clearAllPipes(): void {
        const pipeSpawners = [
            this.pipeSpawnSet1,
            this.pipeSpawnSet2,
            this.pipeSpawnSet3
        ];

        pipeSpawners.forEach(spawner => {
            if (spawner) {
                // 销毁所有子节点（管道）
                const children = spawner.children.slice(); // 创建副本避免遍历时修改数组
                children.forEach(child => {
                    if (child) {
                        child.destroy();
                    }
                });
            }
        });

        console.log("已清理所有管道");
    }

    /**
     * 确保所有组件都处于停止状态
     */
    private ensureAllComponentsStopped(): void {
        // 停止所有管道生成器
        const pipeSpawners = [
            this.pipeSpawnSet1?.getComponent(PipeSpawner),
            this.pipeSpawnSet2?.getComponent(PipeSpawner),
            this.pipeSpawnSet3?.getComponent(PipeSpawner)
        ];

        pipeSpawners.forEach(spawner => {
            if (spawner) {
                spawner.pause();
            }
        });

        // 停止所有移动组件
        const bgMovers = [
            this.bgSet1?.getComponent(MoveBg),
            this.bgSet2?.getComponent(MoveBg),
            this.bgSet3?.getComponent(MoveBg)
        ];

        const landMovers = [
            this.landSet1?.getComponent(MoveBg),
            this.landSet2?.getComponent(MoveBg),
            this.landSet3?.getComponent(MoveBg)
        ];

        bgMovers.forEach(mover => {
            if (mover) {
                mover.disableMoving();
            }
        });

        landMovers.forEach(mover => {
            if (mover) {
                mover.disableMoving();
            }
        });

        console.log("已确保所有组件处于停止状态");
    }

    /**
     * 处理已存在的管道，确保它们处于正确的状态
     * 注意：现在isPaused()方法已经包含Ready状态，所以管道在准备阶段不会移动
     * 但为了保险起见，我们仍然禁用组件
     */
    private handleExistingPipes(): void {
        const pipeSpawners = [
            this.pipeSpawnSet1,
            this.pipeSpawnSet2,
            this.pipeSpawnSet3
        ];

        let handledPipeCount = 0;

        pipeSpawners.forEach((spawner, index) => {
            if (spawner && spawner.active) {
                // 处理该生成器下的所有管道
                const pipes = spawner.children;
                pipes.forEach(pipeNode => {
                    // 禁用Pipe组件（防止水平移动）
                    const pipeComponent = pipeNode.getComponent(Pipe);
                    if (pipeComponent) {
                        pipeComponent.enabled = false;
                        handledPipeCount++;
                    }

                    // 禁用MovingPipe组件（防止垂直移动）
                    const movingPipeComponent = pipeNode.getComponent(MovingPipe);
                    if (movingPipeComponent) {
                        movingPipeComponent.enabled = false;
                    }
                });

                if (pipes.length > 0) {
                    console.log(`背景套装${index + 1}: 禁用了${pipes.length}个管道的移动组件`);
                }
            }
        });

        console.log(`已处理所有已存在的管道，共禁用${handledPipeCount}个管道的移动`);
    }

    /**
     * 调整第二、第三套背景的管道生成时机
     * 解决困难难度下第一枚金币出现在第一列管道之前的问题
     * 注意：这个方法现在只是预留，实际的timer调整会在游戏开始时进行
     */
    private adjustPipeSpawnerTimingForBackgroundSets(): void {
        // 只对第二、第三套背景进行调整
        if (this.currentBackgroundSet === 2 || this.currentBackgroundSet === 3) {
            const currentPipeSpawner = this.getCurrentPipeSpawner();
            if (currentPipeSpawner) {
                // 获取当前难度
                const currentDifficulty = GameDifficulty.getDifficulty();

                // 只在困难难度下进行调整，但不在准备阶段修改timer
                if (currentDifficulty === GameDifficulty.DIFFICULTY_HARD) {
                    // 标记需要调整timer，但不在这里直接修改
                    // timer的调整将在PipeSpawner的start方法中进行
                    (currentPipeSpawner as any)._needsTimerAdjustment = true;
                    (currentPipeSpawner as any)._backgroundSet = this.currentBackgroundSet;

                    console.log(`标记背景套装${this.currentBackgroundSet}需要在游戏开始时调整困难难度管道生成时机`);
                }
            }
        }
    }

    /**
     * 配置MoveBg组件的参数
     * @param setNumber 背景套装编号
     */
    private configureMoveBgParameters(setNumber: number): void {
        let bgNode: Node = null;
        let landNode: Node = null;
        let triggerX: number = -730;
        let offsetX: number = 728;

        switch (setNumber) {
            case 1:
                bgNode = this.bgSet1;
                landNode = this.landSet1;
                triggerX = -730;
                offsetX = 728;
                break;
            case 2:
                bgNode = this.bgSet2;
                landNode = this.landSet2;
                triggerX = -730;
                offsetX = 728;
                break;
            case 3:
                bgNode = this.bgSet3;
                landNode = this.landSet3;
                triggerX = -1120;
                offsetX = 1500;
                break;
        }

        // 配置背景MoveBg组件
        if (bgNode) {
            const bgMoveBg = bgNode.getComponent(MoveBg);
            if (bgMoveBg) {
                bgMoveBg.setLoopParameters(triggerX, offsetX);
                console.log(`背景套装${setNumber}的背景MoveBg参数已设置: trigger=${triggerX}, offset=${offsetX}`);
            }
        }

        // 配置地面MoveBg组件
        if (landNode) {
            const landMoveBg = landNode.getComponent(MoveBg);
            if (landMoveBg) {
                landMoveBg.setLoopParameters(triggerX, offsetX);
                console.log(`背景套装${setNumber}的地面MoveBg参数已设置: trigger=${triggerX}, offset=${offsetX}`);
            }
        }
    }

}
