import { _decorator, Component, Node, Label, Collider2D } from 'cc';
import { GameData, BirdType } from './GameData';
import { InvincibilityEffect } from './InvincibilityEffect';
import { GameManager } from './GameManager';
const { ccclass, property } = _decorator;

/**
 * 信天翁无敌时间管理器
 * 负责管理信天翁每经过15列管道获得1秒无敌时间的功能
 */
@ccclass('AlbatrossInvincibilityManager')
export class AlbatrossInvincibilityManager extends Component {
    
    private static _instance: AlbatrossInvincibilityManager = null;
    
    // 管道计数相关
    private _pipeCount: number = 0; // 当前通过的管道数量 (0-14)
    private readonly PIPES_FOR_INVINCIBILITY: number = 15; // 触发无敌所需的管道数量
    
    // 无敌时间相关
    private _isInvincible: boolean = false; // 是否处于无敌状态
    private _invincibilityTimer: number = 0; // 无敌时间计时器
    private readonly INVINCIBILITY_DURATION: number = 3.0; // 无敌持续时间（秒）

    // 注意：计分逻辑现在由Bird.ts直接处理，使用简单的时间间隔控制


    
    // UI相关
    @property(Label)
    pipeCountLabel: Label = null; // 管道计数显示标签
    
    @property(Node)
    invincibilityEffect: Node = null; // 无敌特效节点
    
    public static getInstance(): AlbatrossInvincibilityManager {
        return this._instance;
    }
    
    onLoad() {
        AlbatrossInvincibilityManager._instance = this;
    }
    
    start() {
        this.resetPipeCount();
        this.updatePipeCountDisplay();
        this.hideInvincibilityEffect();
    }
    
    update(deltaTime: number) {
        // 只有信天翁才处理无敌逻辑
        if (!this.isAlbatrossSelected()) {
            return;
        }

        // 检查游戏是否暂停，暂停时不更新无敌时间
        const gameManager = GameManager.inst();
        if (gameManager && gameManager.isPaused()) {
            return; // 暂停时不更新无敌倒计时
        }

        // 更新无敌时间
        if (this._isInvincible) {
            this._invincibilityTimer -= deltaTime;

            // 实时更新倒计时显示
            this.updatePipeCountDisplay();

            if (this._invincibilityTimer <= 0) {
                this.endInvincibility();
            }
        }
    }
    
    /**
     * 检查当前是否选择了信天翁
     */
    private isAlbatrossSelected(): boolean {
        return GameData.getSelectedBirdType() === BirdType.ALBATROSS;
    }
    
    /**
     * 当信天翁通过管道时调用
     */
    public onPipePassed(): void {
        // 只有信天翁才计数
        if (!this.isAlbatrossSelected()) {
            return;
        }

        // 如果已经处于无敌状态，不计算管道通过（防止重复触发）
        if (this._isInvincible) {
            console.log("信天翁已处于无敌状态，忽略管道通过计数");
            return;
        }

        this._pipeCount++;
        console.log(`信天翁通过管道，当前计数: ${this._pipeCount}`);

        // 检查是否达到无敌条件
        if (this._pipeCount >= this.PIPES_FOR_INVINCIBILITY) {
            this.triggerInvincibility();
            this._pipeCount = 0; // 重置计数
        }

        this.updatePipeCountDisplay();
    }
    
    /**
     * 触发无敌时间
     */
    private triggerInvincibility(): void {
        this._isInvincible = true;
        this._invincibilityTimer = this.INVINCIBILITY_DURATION;

        console.log(`信天翁进入无敌状态，持续时间: ${this.INVINCIBILITY_DURATION}秒`);

        // 无敌状态下的碰撞处理由Bird.ts中的碰撞检测逻辑处理

        // 显示无敌特效
        this.showInvincibilityEffect();
    }
    
    /**
     * 结束无敌时间
     */
    private endInvincibility(): void {
        this._isInvincible = false;
        this._invincibilityTimer = 0;

        console.log("信天翁无敌状态结束");

        // 无敌状态结束，碰撞检测恢复正常

        // 隐藏无敌特效
        this.hideInvincibilityEffect();
    }
    
    /**
     * 检查是否处于无敌状态
     */
    public isInvincible(): boolean {
        return this.isAlbatrossSelected() && this._isInvincible;
    }
    
    /**
     * 重置管道计数
     */
    public resetPipeCount(): void {
        this._pipeCount = 0;
        this._isInvincible = false;
        this._invincibilityTimer = 0;

        // 重置时确保无敌状态被清除

        this.updatePipeCountDisplay();
        this.hideInvincibilityEffect();
        console.log("信天翁管道计数已重置");
    }
    
    /**
     * 更新管道计数显示
     */
    private updatePipeCountDisplay(): void {
        if (this.pipeCountLabel) {
            // 只有信天翁才显示计数
            if (this.isAlbatrossSelected()) {
                this.pipeCountLabel.node.active = true;

                // 如果处于无敌状态，显示倒计时
                if (this._isInvincible) {
                    const remainingSeconds = Math.ceil(this._invincibilityTimer);
                    this.pipeCountLabel.string = remainingSeconds.toString();
                } else {
                    // 正常状态显示管道计数
                    this.pipeCountLabel.string = `${this._pipeCount}/15`;
                }
            } else {
                this.pipeCountLabel.node.active = false;
            }
        }
    }
    
    /**
     * 显示无敌特效
     */
    private showInvincibilityEffect(): void {
        if (this.invincibilityEffect) {
            const effectComponent = this.invincibilityEffect.getComponent(InvincibilityEffect);
            if (effectComponent) {
                effectComponent.startEffect();
            } else {
                // 如果没有特效组件，就简单地显示节点
                this.invincibilityEffect.active = true;
            }
        }
    }

    /**
     * 隐藏无敌特效
     */
    private hideInvincibilityEffect(): void {
        if (this.invincibilityEffect) {
            const effectComponent = this.invincibilityEffect.getComponent(InvincibilityEffect);
            if (effectComponent) {
                effectComponent.stopEffect();
            } else {
                // 如果没有特效组件，就简单地隐藏节点
                this.invincibilityEffect.active = false;
            }
        }
    }
    
    /**
     * 获取当前管道计数
     */
    public getCurrentPipeCount(): number {
        return this._pipeCount;
    }
    
    /**
     * 获取剩余无敌时间
     */
    public getRemainingInvincibilityTime(): number {
        return this._invincibilityTimer;
    }

    // 注意：计分逻辑现在完全由Bird.ts处理，使用简单的时间间隔控制



    onDestroy() {
        if (AlbatrossInvincibilityManager._instance === this) {
            AlbatrossInvincibilityManager._instance = null;
        }
    }
}
