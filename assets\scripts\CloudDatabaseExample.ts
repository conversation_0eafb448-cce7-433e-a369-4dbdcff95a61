import { _decorator, Component, Node, Label, Button } from 'cc';
import { CloudDatabaseManager } from './Data/CloudDatabaseManager';
import { WeChatFriendsData } from './Data/WeChatFriendsData';
import { InviteCodeManager } from './InviteCodeManager';
import { GameMode } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 云数据库使用示例
 * 展示如何在游戏中集成和使用云数据库功能
 */
@ccclass('CloudDatabaseExample')
export class CloudDatabaseExample extends Component {

    @property(Label)
    statusLabel: Label = null;

    @property(Button)
    initButton: Button = null;

    @property(Button)
    loadFriendsButton: Button = null;

    @property(Button)
    testInviteButton: Button = null;

    @property(Button)
    showLeaderboardButton: Button = null;

    private cloudDB: CloudDatabaseManager = null;
    private friendsData: WeChatFriendsData = null;

    onLoad() {
        this.cloudDB = CloudDatabaseManager.instance;
        this.friendsData = WeChatFriendsData.instance;
        this.updateStatus("云数据库示例已加载");
    }

    start() {
        // 绑定按钮事件
        this.initButton?.node.on('click', this.onInitClick, this);
        this.loadFriendsButton?.node.on('click', this.onLoadFriendsClick, this);
        this.testInviteButton?.node.on('click', this.onTestInviteClick, this);
        this.showLeaderboardButton?.node.on('click', this.onShowLeaderboardClick, this);
    }

    private updateStatus(message: string): void {
        console.log(`CloudDatabaseExample: ${message}`);
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
    }

    /**
     * 初始化云数据库
     */
    private async onInitClick(): Promise<void> {
        this.updateStatus("正在初始化云数据库...");
        
        try {
            const success = await this.cloudDB.initialize();
            if (success) {
                this.updateStatus("云数据库初始化成功！");
                
                // 检查是否需要上传初始数据
                const players = await this.cloudDB.getPlayersData(1, 0);
                if (players.length === 0) {
                    this.updateStatus("检测到空数据库，正在上传初始数据...");
                    await this.cloudDB.uploadMockPlayersData(100);
                    this.updateStatus("初始数据上传完成！");
                }
            } else {
                this.updateStatus("云数据库初始化失败！");
            }
        } catch (error) {
            this.updateStatus(`初始化失败: ${error.message}`);
        }
    }

    /**
     * 加载好友数据
     */
    private async onLoadFriendsClick(): Promise<void> {
        this.updateStatus("正在加载好友数据...");
        
        try {
            // 获取好友排行榜数据
            const friendsRanking = this.friendsData.getFriendsRanking(GameMode.NORMAL_STANDARD);
            this.updateStatus(`加载完成！好友排行榜共${friendsRanking.length}人`);
            
            // 显示前3名好友
            console.log("好友排行榜前3名:");
            friendsRanking.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.friend.nickname}: ${item.score}分`);
            });
            
        } catch (error) {
            this.updateStatus(`加载好友数据失败: ${error.message}`);
        }
    }

    /**
     * 测试邀请码功能
     */
    private async onTestInviteClick(): Promise<void> {
        this.updateStatus("正在测试邀请码功能...");
        
        try {
            // 获取当前玩家的邀请码
            const myInviteCode = InviteCodeManager.getPlayerInviteCode();
            console.log(`我的邀请码: ${myInviteCode}`);
            
            // 检查是否可以显示邀请码输入界面
            const shouldShow = InviteCodeManager.shouldShowInviteCodeInput();
            console.log(`是否应该显示邀请码输入: ${shouldShow}`);
            
            // 模拟使用一个邀请码（注意：这只是示例，实际使用时需要用户输入）
            if (!InviteCodeManager.hasUsedInviteCode()) {
                // 从云数据库获取一个有效的邀请码进行测试
                const players = await this.cloudDB.getPlayersData(10, 0);
                const testInviteCode = players.find(p => p.inviteCode !== myInviteCode)?.inviteCode;
                
                if (testInviteCode) {
                    console.log(`测试邀请码: ${testInviteCode}`);
                    const success = await InviteCodeManager.useInviteCode(testInviteCode);
                    this.updateStatus(success ? `成功使用邀请码 ${testInviteCode}` : "邀请码使用失败");
                } else {
                    this.updateStatus("没有找到可用的测试邀请码");
                }
            } else {
                this.updateStatus("已经使用过邀请码，无法重复使用");
            }
            
        } catch (error) {
            this.updateStatus(`邀请码测试失败: ${error.message}`);
        }
    }

    /**
     * 显示排行榜
     */
    private async onShowLeaderboardClick(): Promise<void> {
        this.updateStatus("正在加载排行榜数据...");
        
        try {
            // 获取全服排行榜
            const globalRanking = this.friendsData.getGlobalRanking(GameMode.NORMAL_STANDARD);
            this.updateStatus(`排行榜加载完成！共${globalRanking.length}人`);
            
            // 显示前10名
            console.log("全服排行榜前10名:");
            globalRanking.slice(0, 10).forEach((item, index) => {
                console.log(`${index + 1}. ${item.friend.nickname}: ${item.score}分`);
            });
            
            // 获取当前玩家排名
            const playerRank = this.friendsData.getPlayerRankInGlobal(GameMode.NORMAL_STANDARD);
            if (playerRank !== -1) {
                console.log(`我的排名: 第${playerRank + 1}名`);
            } else {
                console.log("我的排名: 未上榜");
            }
            
        } catch (error) {
            this.updateStatus(`排行榜加载失败: ${error.message}`);
        }
    }

    /**
     * 演示完整的云数据库集成流程
     */
    public async demonstrateFullIntegration(): Promise<void> {
        console.log("=== 开始云数据库集成演示 ===");
        
        try {
            // 1. 初始化云数据库
            console.log("步骤1: 初始化云数据库");
            const initSuccess = await this.cloudDB.initialize();
            if (!initSuccess) {
                throw new Error("云数据库初始化失败");
            }
            
            // 2. 检查并上传初始数据
            console.log("步骤2: 检查数据库状态");
            const existingPlayers = await this.cloudDB.getPlayersData(1, 0);
            if (existingPlayers.length === 0) {
                console.log("数据库为空，上传初始数据...");
                await this.cloudDB.uploadMockPlayersData(50);
            }
            
            // 3. 获取排行榜数据
            console.log("步骤3: 获取排行榜数据");
            const leaderboard = await this.cloudDB.getLeaderboard(GameMode.NORMAL_STANDARD, 10);
            console.log(`获取到${leaderboard.length}条排行榜数据`);
            
            // 4. 测试好友数据
            console.log("步骤4: 测试好友数据");
            const friendsRanking = this.friendsData.getFriendsRanking(GameMode.NORMAL_STANDARD);
            console.log(`好友排行榜: ${friendsRanking.length}人`);
            
            // 5. 测试邀请码系统
            console.log("步骤5: 测试邀请码系统");
            const myCode = InviteCodeManager.getPlayerInviteCode();
            console.log(`我的邀请码: ${myCode}`);
            
            console.log("✅ 云数据库集成演示完成！");
            this.updateStatus("演示完成！所有功能正常");
            
        } catch (error) {
            console.error("演示过程中出错:", error);
            this.updateStatus(`演示失败: ${error.message}`);
        }
    }

    onDestroy() {
        // 清理事件监听
        this.initButton?.node.off('click', this.onInitClick, this);
        this.loadFriendsButton?.node.off('click', this.onLoadFriendsClick, this);
        this.testInviteButton?.node.off('click', this.onTestInviteClick, this);
        this.showLeaderboardButton?.node.off('click', this.onShowLeaderboardClick, this);
    }
}

// 导出全局演示函数
declare global {
    interface Window {
        demonstrateCloudDB: () => Promise<void>;
    }
}

if (typeof window !== 'undefined') {
    window.demonstrateCloudDB = async () => {
        console.log("开始云数据库完整演示...");
        
        // 创建临时实例进行演示
        const demo = new CloudDatabaseExample();
        await demo.demonstrateFullIntegration();
    };
}
