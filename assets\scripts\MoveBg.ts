import { _decorator, Component, Node } from 'cc';
import { GameManager } from './GameManager';
import { WindChallengeManager } from './WindChallengeManager';
const { ccclass, property } = _decorator;

@ccclass('MoveBg')
export class MoveBg extends Component {

    @property(Node)
    target1ToMove:Node = null;
    @property(Node)
    target2ToMove:Node = null;

    // 重新定位的触发点
    @property
    resetTriggerX:number = -730;

    // 重新定位时的偏移距离
    @property
    resetOffsetX:number = 728;

    private moveSpeed:number = 100;
    private _canMoving:boolean = false;

    start() {
        this.moveSpeed = GameManager.inst().getCurrentMoveSpeed();
        console.log(`地板移动速度设置为: ${this.moveSpeed} (根据难度调整)`);
    }

    update(deltaTime: number) {

        if(this._canMoving==false)return;

        // 检查游戏是否暂停，暂停时不移动
        const gameManager = GameManager.inst();
        if (gameManager && gameManager.isPaused()) {
            return; // 暂停时不移动
        }

        // 在大风吹模式下使用动态速度
        let currentSpeed = this.moveSpeed;
        if (WindChallengeManager.isWindMode()) {
            const windManager = WindChallengeManager.getInstance();
            if (windManager) {
                currentSpeed = windManager.getCurrentSpeed();
            }
        }

        const moveDistance = currentSpeed*deltaTime;

        let p1 = this.target1ToMove.getPosition();
        this.target1ToMove.setPosition(p1.x-moveDistance,p1.y);
        let p2 = this.target2ToMove.getPosition();
        this.target2ToMove.setPosition(p2.x-moveDistance,p2.y);


        if(p1.x < this.resetTriggerX){
            p2 = this.target2ToMove.getPosition();
            this.target1ToMove.setPosition(p2.x + this.resetOffsetX, p2.y);
        }
        if(p2.x < this.resetTriggerX){
            p1 = this.target1ToMove.getPosition();
            this.target2ToMove.setPosition(p1.x + this.resetOffsetX, p1.y);
        }

    }
    public enableMoving(){
        this._canMoving=true;
    }
    public disableMoving(){
        this._canMoving = false;
    }

    /**
     * 更新移动速度（用于大风吹模式）
     */
    public updateSpeed(newSpeed: number): void {
        this.moveSpeed = newSpeed;
        console.log(`背景移动速度更新为: ${newSpeed}`);
    }

    /**
     * 设置背景循环移动参数
     * @param triggerX 重新定位的触发点
     * @param offsetX 重新定位时的偏移距离
     */
    public setLoopParameters(triggerX: number, offsetX: number): void {
        this.resetTriggerX = triggerX;
        this.resetOffsetX = offsetX;
        console.log(`MoveBg循环参数设置: triggerX=${this.resetTriggerX}, offsetX=${this.resetOffsetX}`);
    }
}


