import { _decorator } from 'cc';

export class GameDifficulty {
    // 难度常量
    public static readonly DIFFICULTY_EASY: number = 0;
    public static readonly DIFFICULTY_NORMAL: number = 1;
    public static readonly DIFFICULTY_HARD: number = 2;

    // 难度存储键
    private static readonly DIFFICULTY_KEY: string = "GameDifficulty";

    // 默认难度
    private static _currentDifficulty: number = GameDifficulty.DIFFICULTY_NORMAL;

    // 设置难度
    public static setDifficulty(difficulty: number): void {
        this._currentDifficulty = difficulty;
        // 保存到本地存储
        localStorage.setItem(this.DIFFICULTY_KEY, difficulty.toString());
    }

    // 获取当前难度
    public static getDifficulty(): number {
        // 尝试从本地存储获取
        const savedDifficulty = localStorage.getItem(this.DIFFICULTY_KEY);
        if (savedDifficulty !== null) {
            const parsedDifficulty = parseInt(savedDifficulty);
            // 🔧 修复：检查parseInt结果是否有效
            if (!isNaN(parsedDifficulty) && parsedDifficulty >= 0 && parsedDifficulty <= 2) {
                // console.log(`GameDifficulty.getDifficulty(): 从存储获取 ${parsedDifficulty}`);
                return parsedDifficulty;
            } else {
                console.warn(`GameDifficulty.getDifficulty(): 存储值无效 "${savedDifficulty}"，使用默认值`);
                // 清除无效的存储值
                localStorage.removeItem(this.DIFFICULTY_KEY);
            }
        }
        // console.log(`GameDifficulty.getDifficulty(): 使用默认值 ${this._currentDifficulty}`);
        return this._currentDifficulty;
    }
}

