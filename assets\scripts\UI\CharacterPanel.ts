import { _decorator, Component, Node, Button } from 'cc';
import { BirdType, GameData } from '../GameData';
import { BirdCard } from './BirdCard';
const { ccclass, property } = _decorator;

@ccclass('CharacterPanel')
export class CharacterPanel extends Component {
    // 所有角色节点数组（每个节点包含小鸟和购买UI）
    @property([Node])
    characterNodes: Node[] = [];

    // 左右切换按钮
    @property(Node)
    leftButton: Node = null;

    @property(Node)
    rightButton: Node = null;

    // 当前显示的角色索引（商店场景独立，不影响游戏）
    private currentCharacterIndex: number = 0;

    // 小鸟购买卡片数组（自动从characterNodes中获取）
    private birdCards: BirdCard[] = [];

    start() {
        // 商店场景独立初始化，不读取游戏中选择的小鸟
        this.currentCharacterIndex = 0; // 默认显示普通小鸟
        console.log("CharacterPanel: 商店场景初始化，默认显示普通小鸟");

        // 自动获取小鸟购买卡片
        this.collectBirdCards();

        // 初始化小鸟购买卡片
        this.initializeBirdCards();

        // 初始化：显示第一个角色，隐藏其他角色
        this.showCharacterAtIndex(this.currentCharacterIndex);

        // 检查按钮是否存在并有效
        if (this.leftButton && this.leftButton.isValid) {
            // 为左按钮添加点击事件
            const leftBtnComp = this.leftButton.getComponent(Button);
            if (leftBtnComp) {
                leftBtnComp.clickEvents = [];
                // 添加按钮点击事件监听
                this.leftButton.on(Button.EventType.CLICK, this.onLeftButtonClick, this);
            }
        }

        if (this.rightButton && this.rightButton.isValid) {
            // 为右按钮添加点击事件
            const rightBtnComp = this.rightButton.getComponent(Button);
            if (rightBtnComp) {
                rightBtnComp.clickEvents = [];
                // 添加按钮点击事件监听
                this.rightButton.on(Button.EventType.CLICK, this.onRightButtonClick, this);
            }
        }

        console.log("CharacterPanel初始化完成，当前显示角色索引：", this.currentCharacterIndex);
    }

    onDestroy() {
        // 移除事件监听，防止内存泄漏
        if (this.leftButton && this.leftButton.isValid) {
            this.leftButton.off(Button.EventType.CLICK, this.onLeftButtonClick, this);
        }

        if (this.rightButton && this.rightButton.isValid) {
            this.rightButton.off(Button.EventType.CLICK, this.onRightButtonClick, this);
        }

        // 移除小鸟购买卡片事件监听
        this.birdCards.forEach(card => {
            if (card && card.node && card.node.isValid) {
                card.node.off('bird-purchased', this.onBirdPurchased, this);
            }
        });
    }

    // 自动收集小鸟购买卡片
    private collectBirdCards() {
        this.birdCards = [];
        this.characterNodes.forEach(characterNode => {
            if (characterNode) {
                const birdCard = characterNode.getComponent(BirdCard);
                if (birdCard) {
                    this.birdCards.push(birdCard);
                }
            }
        });
        console.log(`CharacterPanel: 找到 ${this.birdCards.length} 个小鸟购买卡片`);
    }

    // 初始化小鸟购买卡片
    private initializeBirdCards() {
        // 为每个小鸟卡片监听购买事件
        this.birdCards.forEach(card => {
            if (card && card.node) {
                card.node.on('bird-purchased', this.onBirdPurchased, this);
            }
        });
    }

    // 小鸟购买成功回调
    private onBirdPurchased(birdType: BirdType) {
        console.log(`CharacterPanel: 小鸟 ${GameData.getBirdName(birdType)} 购买成功`);
        // 更新所有卡片显示
        this.updateAllBirdCards();
    }

    // 更新所有小鸟卡片显示
    private updateAllBirdCards() {
        this.birdCards.forEach(card => {
            if (card) {
                card.updateDisplay();
            }
        });
    }

    // 显示指定索引的角色，隐藏其他角色（商店场景独立，不影响游戏）
    showCharacterAtIndex(index: number) {
        if (this.characterNodes.length === 0) {
            console.error("CharacterPanel: 没有配置角色节点！");
            return;
        }

        // 确保索引在有效范围内
        index = this.normalizeIndex(index);
        this.currentCharacterIndex = index;

        // 激活当前索引的角色，禁用其他角色
        for (let i = 0; i < this.characterNodes.length; i++) {
            this.characterNodes[i].active = (i === index);
        }

        // 注意：商店场景不再保存选择的角色类型到GameData
        console.log("CharacterPanel: 商店场景切换到角色：", index, "（不影响游戏）");
    }

    // 处理索引循环（当索引超出范围时循环到另一端）
    normalizeIndex(index: number): number {
        const length = this.characterNodes.length;
        // 如果索引小于0，则循环到最后一个
        if (index < 0) {
            return length - 1;
        }
        // 如果索引超出范围，则循环到第一个
        if (index >= length) {
            return 0;
        }
        return index;
    }

    // 左按钮点击事件处理
    onLeftButtonClick() {
        console.log("CharacterPanel: 点击左按钮");
        // 切换到上一个角色
        this.showCharacterAtIndex(this.currentCharacterIndex - 1);
    }

    // 右按钮点击事件处理
    onRightButtonClick() {
        console.log("CharacterPanel: 点击右按钮");
        // 切换到下一个角色
        this.showCharacterAtIndex(this.currentCharacterIndex + 1);
    }
}
