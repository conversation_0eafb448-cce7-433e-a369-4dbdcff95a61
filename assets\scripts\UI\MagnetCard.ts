import { _decorator, Component, Node, Button, Label } from 'cc';
import { ItemManager, ItemType } from '../ItemManager';
import { GameData } from '../GameData';
const { ccclass, property } = _decorator;

/**
 * 吸金石UI控制器
 * 负责处理吸金石的购买、使用、显示等UI逻辑
 */
@ccclass('MagnetCard')
export class MagnetCard extends Component {

    @property(Button)
    purchaseButton: Button = null;

    @property(Button)
    useButton: Button = null;

    @property(Button)
    disableButton: Button = null;

    @property(Label)
    priceLabel: Label = null;

    @property(Node)
    coinIcon: Node = null;

    @property(Node)
    insufficientSprite: Node = null;

    start() {
        this.initializeUI();
        this.setupButtonEvents();
        this.updateDisplay();
    }

    onEnable() {
        // 每次节点激活时刷新显示状态
        this.scheduleOnce(() => {
            this.updateDisplay();
        }, 0.1);
    }

    /**
     * 初始化UI
     */
    private initializeUI(): void {
        // 设置价格标签
        if (this.priceLabel) {
            const price = ItemManager.getItemPrice(ItemType.MAGNET);
            this.priceLabel.string = price.toString();
        }

        // 确保使用按钮和禁用按钮位置一致（已在编辑器中设置）
        console.log("MagnetCard UI 初始化完成");
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        console.log("=== 设置吸金石按钮事件 ===");

        // 购买按钮事件
        if (this.purchaseButton) {
            console.log("绑定购买按钮事件");
            this.purchaseButton.node.on(Button.EventType.CLICK, this.onPurchaseClick, this);
        } else {
            console.error("购买按钮引用为空！");
        }

        // 使用按钮事件
        if (this.useButton) {
            console.log("绑定使用按钮事件");
            this.useButton.node.on(Button.EventType.CLICK, this.onUseClick, this);
        } else {
            console.error("使用按钮引用为空！");
        }

        // 禁用按钮事件
        if (this.disableButton) {
            console.log("绑定禁用按钮事件");
            this.disableButton.node.on(Button.EventType.CLICK, this.onDisableClick, this);
        } else {
            console.error("禁用按钮引用为空！");
        }

        // 检查其他节点引用
        if (!this.priceLabel) {
            console.error("价格标签引用为空！");
        }
        if (!this.coinIcon) {
            console.error("金币图标引用为空！");
        }
        if (!this.insufficientSprite) {
            console.error("金币不足提示引用为空！");
        }
    }

    /**
     * 购买按钮点击事件
     */
    private onPurchaseClick(): void {
        console.log("=== 点击购买吸金石 ===");

        const price = ItemManager.getItemPrice(ItemType.MAGNET);
        const totalCoins = GameData.getTotalCoins();

        console.log(`当前金币: ${totalCoins}, 需要金币: ${price}`);

        if (totalCoins >= price) {
            console.log("金币足够，尝试购买...");
            const success = ItemManager.purchaseItem(ItemType.MAGNET);
            if (success) {
                // 购买成功后自动激活永久效果
                ItemManager.activateItemEffect(ItemType.MAGNET);
                this.updateDisplay();
                console.log("购买成功！吸金石已自动激活，永久生效");
            } else {
                console.log("购买失败");
            }
        } else {
            console.log("金币不足，显示提示");
            this.showInsufficientSprite();
        }
    }

    /**
     * 使用按钮点击事件
     */
    private onUseClick(): void {
        console.log("点击使用吸金石");

        const currentCount = ItemManager.getItemCount(ItemType.MAGNET);
        if (currentCount <= 0) {
            console.log("尚未购买吸金石");
            this.showInsufficientSprite();
            return;
        }

        // 激活永久效果
        ItemManager.activateItemEffect(ItemType.MAGNET);
        this.updateDisplay();
        console.log("吸金石已激活！永久生效");
    }

    /**
     * 禁用按钮点击事件
     */
    private onDisableClick(): void {
        console.log("点击禁用吸金石");

        // 取消激活效果
        ItemManager.deactivateItemEffect(ItemType.MAGNET);
        this.updateDisplay();
        console.log("吸金石已禁用！");
    }

    /**
     * 显示金币不足提示
     */
    private showInsufficientSprite(): void {
        if (this.insufficientSprite) {
            this.insufficientSprite.active = true;
            // 2秒后隐藏
            this.scheduleOnce(() => {
                if (this.insufficientSprite && this.insufficientSprite.isValid) {
                    this.insufficientSprite.active = false;
                }
            }, 2.0);
        }
    }

    /**
     * 更新显示
     */
    public updateDisplay(): void {
        // 更新按钮状态
        const currentCount = ItemManager.getItemCount(ItemType.MAGNET);
        const hasPurchased = currentCount > 0;
        const isActive = ItemManager.isItemActive(ItemType.MAGNET);

        // 使用按钮：已购买但未激活时显示
        if (this.useButton) {
            this.useButton.node.active = hasPurchased && !isActive;
        }

        // 禁用按钮：已购买且已激活时显示
        if (this.disableButton) {
            this.disableButton.node.active = hasPurchased && isActive;
        }

        // 购买按钮：只有未购买时显示，始终可点击
        if (this.purchaseButton) {
            this.purchaseButton.node.active = !hasPurchased;
            // 始终保持按钮可点击，在点击时检查金币
            if (!hasPurchased) {
                this.purchaseButton.interactable = true;
            }
        }

        // 价格标签：只有未购买时显示
        if (this.priceLabel) {
            this.priceLabel.node.active = !hasPurchased;
        }

        // 金币图标：只有未购买时显示
        if (this.coinIcon) {
            this.coinIcon.active = !hasPurchased;
        }

        console.log(`吸金石状态更新: 数量=${ItemManager.getItemCount(ItemType.MAGNET)}, 激活=${isActive}`);
    }

    onDestroy() {
        // 清理事件监听
        if (this.purchaseButton && this.purchaseButton.isValid) {
            this.purchaseButton.node.off(Button.EventType.CLICK, this.onPurchaseClick, this);
        }

        if (this.useButton && this.useButton.isValid) {
            this.useButton.node.off(Button.EventType.CLICK, this.onUseClick, this);
        }

        if (this.disableButton && this.disableButton.isValid) {
            this.disableButton.node.off(Button.EventType.CLICK, this.onDisableClick, this);
        }
    }
}
