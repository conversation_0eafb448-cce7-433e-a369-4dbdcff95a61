{"appid": "wx9a75f92306014b22", "compileType": "game", "libVersion": "3.8.10", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "compileHotReLoad": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "cloudfunctionRoot": "cloudfunctions/", "cloudbaseRoot": "cloudbase/"}