import { _decorator, Component, input, Input, KeyCode } from 'cc';
import { GameManager } from './GameManager';
const { ccclass, property } = _decorator;

/**
 * 背景切换器 - 用于测试背景切换功能
 * 按键1、2、3切换到对应的背景套装
 */
@ccclass('BackgroundSwitcher')
export class BackgroundSwitcher extends Component {

    start() {
        // 注册键盘事件
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    onDestroy() {
        // 取消键盘事件监听
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);

        // 取消所有调度器
        this.unscheduleAllCallbacks();

        console.log("BackgroundSwitcher: 组件已销毁，事件监听器已清理");
    }

    private onKeyDown(event: any) {
        try {
            const gameManager = GameManager.inst();
            if (!gameManager) {
                console.warn("GameManager未找到");
                return;
            }

            switch (event.keyCode) {
                case KeyCode.DIGIT_1:
                    console.log("切换到背景套装1");
                    gameManager.switchBackgroundSet(1);
                    break;
                case KeyCode.DIGIT_2:
                    console.log("切换到背景套装2");
                    gameManager.switchBackgroundSet(2);
                    break;
                case KeyCode.DIGIT_3:
                    console.log("切换到背景套装3");
                    gameManager.switchBackgroundSet(3);
                    break;
                case KeyCode.KEY_N:
                    // N键切换到下一套背景
                    console.log("切换到下一套背景");
                    const backgroundManager = gameManager['backgroundManager'];
                    if (backgroundManager) {
                        backgroundManager.switchToNextBackground();
                    }
                    break;
                case KeyCode.KEY_P:
                    // P键切换到上一套背景
                    console.log("切换到上一套背景");
                    const bgManager = gameManager['backgroundManager'];
                    if (bgManager) {
                        bgManager.switchToPreviousBackground();
                    }
                    break;
            }
        } catch (error) {
            console.error("背景切换出错:", error);
        }
    }
}
