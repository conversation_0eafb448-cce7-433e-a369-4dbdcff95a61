import { _decorator, Component, Node, Collider2D, find } from 'cc';
import { GameData, BirdType } from './GameData';
import { GameManager } from './GameManager';
const { ccclass, property } = _decorator;

@ccclass('WoodpeckerSkillManager')
export class WoodpeckerSkillManager extends Component {

    private static _instance: WoodpeckerSkillManager = null;

    // 技能冷却时间（秒）
    private readonly SKILL_COOLDOWN: number = 30;

    // 当前冷却剩余时间
    private _cooldownTimer: number = 0;

    // 技能是否可用
    private _isSkillAvailable: boolean = true;

    // 冷却UI标签的引用（现在由专门的UI组件处理）

    public static getInstance(): WoodpeckerSkillManager {
        return this._instance;
    }

    onLoad() {
        WoodpeckerSkillManager._instance = this;
    }

    onDestroy() {
        // 清理单例引用
        if (WoodpeckerSkillManager._instance === this) {
            WoodpeckerSkillManager._instance = null;
        }

        // 取消所有调度器
        this.unscheduleAllCallbacks();

        // 重置技能状态
        this._cooldownTimer = 0;
        this._isSkillAvailable = true;

        console.log("WoodpeckerSkillManager: 组件已销毁，资源已清理");
    }

    start() {
        // UI显示现在由专门的WoodpeckerCooldownLabel组件处理
        console.log("啄木鸟技能管理器已启动");

        // 检查是否有WoodpeckerCooldownLabel组件
        this.scheduleOnce(() => {
            // 从场景根节点查找所有WoodpeckerCooldownLabel组件
            const scene = this.node.scene;
            if (scene) {
                const allNodes = scene.children;
                console.log("场景中的根节点数量:", allNodes.length);

                // 递归查找所有WoodpeckerCooldownLabel组件
                const findCooldownLabels = (node: any): any[] => {
                    let components: any[] = [];
                    const comp = node.getComponent('WoodpeckerCooldownLabel');
                    if (comp) {
                        components.push(comp);
                        console.log("找到WoodpeckerCooldownLabel组件，节点名称:", node.name);
                    }

                    for (let child of node.children) {
                        components = components.concat(findCooldownLabels(child));
                    }
                    return components;
                };

                let totalComponents: any[] = [];
                for (let rootNode of allNodes) {
                    totalComponents = totalComponents.concat(findCooldownLabels(rootNode));
                }

                console.log(`总共找到 ${totalComponents.length} 个WoodpeckerCooldownLabel组件`);
            }
        }, 1);
    }

    update(deltaTime: number) {
        // 更新冷却计时器
        this.updateCooldown(deltaTime);
    }



    /**
     * 更新冷却计时器
     */
    private updateCooldown(deltaTime: number): void {
        if (!this._isSkillAvailable && this._cooldownTimer > 0) {
            // 检查游戏是否暂停，暂停时不更新冷却计时器
            const gameManager = GameManager.inst();
            if (gameManager && gameManager.isPaused()) {
                return; // 暂停时不更新倒计时
            }

            this._cooldownTimer -= deltaTime;

            // 检查冷却是否结束
            if (this._cooldownTimer <= 0) {
                this.resetSkill();
            }
        }
    }



    /**
     * 重置技能状态
     */
    private resetSkill(): void {
        this._isSkillAvailable = true;
        this._cooldownTimer = 0;

        console.log("啄木鸟技能冷却结束，技能可用");
    }

    /**
     * 检查是否是啄木鸟
     */
    private isWoodpeckerSelected(): boolean {
        return GameData.getSelectedBirdType() === BirdType.WOODPECKER;
    }

    /**
     * 尝试使用啄木技能
     * @param pipeCollider 碰撞的管道碰撞器
     * @returns 是否成功使用技能
     */
    public tryUseSkill(pipeCollider: Collider2D): boolean {
        // 检查是否是啄木鸟
        if (!this.isWoodpeckerSelected()) {
            return false;
        }

        // 检查技能是否可用
        if (!this._isSkillAvailable) {
            console.log("啄木鸟技能正在冷却中，无法使用");
            return false;
        }

        // 使用技能：销毁管道
        this.destroyPipe(pipeCollider);
        
        // 开始冷却
        this.startCooldown();
        
        return true;
    }

    /**
     * 销毁管道
     */
    private destroyPipe(pipeCollider: Collider2D): void {
        if (!pipeCollider || !pipeCollider.node) {
            console.error("无效的管道碰撞器");
            return;
        }

        // 获取管道的根节点（通常是管道预制体的实例）
        let pipeNode = pipeCollider.node;

        // 如果碰撞器在子节点上，需要找到管道的根节点
        // 管道预制体通常包含多个碰撞器（上管道、下管道、中间计分区域）
        if (pipeNode.parent && pipeNode.parent.name.includes('Pipe')) {
            pipeNode = pipeNode.parent;
        }

        console.log(`啄木鸟啄掉管道: ${pipeNode.name}`);

        // 延迟销毁，避免在物理碰撞过程中立即销毁导致错误
        this.scheduleOnce(() => {
            if (pipeNode && pipeNode.isValid) {
                pipeNode.destroy();
            }
        }, 0);
    }

    /**
     * 开始技能冷却
     */
    private startCooldown(): void {
        this._isSkillAvailable = false;
        this._cooldownTimer = this.SKILL_COOLDOWN;

        console.log(`啄木鸟技能进入冷却，冷却时间: ${this.SKILL_COOLDOWN}秒`);

        // 通知UI开始倒计时
        const gameManager = GameManager.inst();
        if (gameManager && gameManager.woodpeckerCooldownLabel) {
            gameManager.woodpeckerCooldownLabel.startCooldown(this.SKILL_COOLDOWN);
        }
    }

    /**
     * 重置技能状态（游戏重新开始时调用）
     */
    public resetSkillState(): void {
        this._isSkillAvailable = true;
        this._cooldownTimer = 0;

        console.log("啄木鸟技能状态已重置");
    }

    /**
     * 重置技能状态并同时重置UI（复活时调用）
     */
    public resetSkillStateWithUI(): void {
        this._isSkillAvailable = true;
        this._cooldownTimer = 0;

        console.log("啄木鸟技能状态已重置（包含UI）");

        // 同时重置UI倒计时
        const gameManager = GameManager.inst();
        if (gameManager && gameManager.woodpeckerCooldownLabel) {
            gameManager.woodpeckerCooldownLabel.stopCooldown();
            console.log("啄木鸟技能UI倒计时已重置");
        }
    }

    /**
     * 获取技能是否可用
     */
    public isSkillAvailable(): boolean {
        return this._isSkillAvailable && this.isWoodpeckerSelected();
    }

    /**
     * 获取剩余冷却时间
     */
    public getRemainingCooldownTime(): number {
        return this._cooldownTimer;
    }
}
