import { _decorator, Component, Node } from 'cc';
import { GameData, GameMode } from './GameData';
import { GameDifficulty } from './GameDifficulty';
import { ChallengeMode } from './ChallengeMode';
import { GameDataManager } from './Data/GameDataManager';

const { ccclass, property } = _decorator;

/**
 * 不使用云同步的游戏模式测试
 * 用于排除云数据同步对本地记录的影响
 */
@ccclass('GameModeTestWithoutCloud')
export class GameModeTestWithoutCloud extends Component {

    onLoad() {
        console.log("=== 无云同步游戏模式测试开始 ===");
        
        // 延迟执行，确保其他系统初始化完成
        this.scheduleOnce(() => {
            this.runTestWithoutCloud();
        }, 2.0);
    }

    private async runTestWithoutCloud() {
        console.log("\n🧪 开始无云同步测试");
        
        // 1. 禁用云数据同步
        await this.disableCloudSync();
        
        // 2. 清除所有记录
        GameData.clearAllGameRecords();
        console.log("✅ 已清除所有记录");
        
        // 3. 测试各个模式
        this.testModeDirectly("轻松", GameMode.NORMAL_EASY, 15);
        this.testModeDirectly("标准", GameMode.NORMAL_STANDARD, 25);
        this.testModeDirectly("困难", GameMode.NORMAL_HARD, 35);
        
        // 4. 验证结果
        this.verifyResults();
        
        // 5. 测试GameOverUI的逻辑
        this.testGameOverUILogic();
    }

    private async disableCloudSync() {
        try {
            const gameDataManager = GameDataManager.instance;
            if (gameDataManager) {
                // 禁用自动同步
                gameDataManager.setAutoSyncEnabled(false);
                console.log("✅ 已禁用云数据自动同步");
            }
        } catch (error) {
            console.log("⚠️ 禁用云同步失败，但继续测试:", error);
        }
    }

    private testModeDirectly(name: string, mode: GameMode, testScore: number) {
        console.log(`\n--- 直接测试${name}模式 (${mode}) ---`);
        
        // 直接设置游戏模式，不依赖难度设置
        GameData.setCurrentGameMode(mode);
        
        // 验证设置
        const currentMode = GameData.getCurrentGameMode();
        console.log(`设置验证: 期望=${mode}, 实际=${currentMode}`);
        
        // 模拟游戏分数
        GameData.resetScore();
        GameData.addScore(testScore);
        console.log(`设置分数: ${GameData.getScore()}`);
        
        // 直接保存分数到指定模式
        console.log(`开始保存分数到模式 ${mode}`);
        GameData.saveScore(mode);
        
        // 立即验证保存结果
        const savedBest = GameData.getBestScore(mode);
        const savedTop = GameData.getTopScores(mode);
        console.log(`保存结果: 最高分=${savedBest}, 前三分=[${savedTop.join(', ')}]`);
        
        // 检查localStorage
        const bestKey = `BestScore_Mode_${mode}`;
        const topKey = `TopScores_Mode_${mode}`;
        const bestValue = localStorage.getItem(bestKey);
        const topValue = localStorage.getItem(topKey);
        console.log(`localStorage: ${bestKey}=${bestValue}, ${topKey}=${topValue}`);
        
        // 检查是否保存到了错误的模式
        for (let checkMode = 0; checkMode <= 5; checkMode++) {
            if (checkMode !== mode) {
                const wrongBest = GameData.getBestScore(checkMode as GameMode);
                if (wrongBest > 0) {
                    console.log(`⚠️ 警告: 模式${checkMode}也有分数: ${wrongBest}`);
                }
            }
        }
    }

    private verifyResults() {
        console.log("\n📊 最终验证结果:");
        
        const expectedResults = [
            { mode: GameMode.NORMAL_EASY, name: "轻松", expected: 15 },
            { mode: GameMode.NORMAL_STANDARD, name: "标准", expected: 25 },
            { mode: GameMode.NORMAL_HARD, name: "困难", expected: 35 },
            { mode: GameMode.CHALLENGE_WIND, name: "大风吹", expected: 0 },
            { mode: GameMode.CHALLENGE_FOG, name: "大雾起", expected: 0 },
            { mode: GameMode.CHALLENGE_SNOW, name: "大雪飘", expected: 0 }
        ];
        
        let allCorrect = true;
        
        for (const { mode, name, expected } of expectedResults) {
            const actual = GameData.getBestScore(mode);
            const topScores = GameData.getTopScores(mode);
            const isCorrect = actual === expected;
            
            console.log(`${name}模式: 期望=${expected}, 实际=${actual}, 前三分=[${topScores.join(', ')}] ${isCorrect ? '✅' : '❌'}`);
            
            if (!isCorrect) {
                allCorrect = false;
            }
        }
        
        console.log(`\n🎯 无云同步测试结果: ${allCorrect ? '✅ 所有测试通过！' : '❌ 存在问题！'}`);
        
        if (allCorrect) {
            console.log("✅ 本地分数保存逻辑正常，问题可能出在云数据同步上");
        } else {
            console.log("❌ 本地分数保存逻辑有问题，需要进一步调试");
        }
    }

    private testGameOverUILogic() {
        console.log("\n--- 测试GameOverUI逻辑（模拟） ---");
        
        // 模拟用户选择轻松模式
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_EASY);
        ChallengeMode.clearMode();
        
        // 验证设置
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        console.log(`难度设置: ${difficulty}, 挑战模式: ${challengeMode}`);
        
        // 计算游戏模式
        const calculatedMode = GameData.determineGameMode(difficulty, challengeMode);
        console.log(`计算的游戏模式: ${calculatedMode} (${GameData.getGameModeName(calculatedMode)})`);
        
        // 设置当前游戏模式
        GameData.setCurrentGameMode(calculatedMode);
        
        // 验证当前模式
        const currentMode = GameData.getCurrentGameMode();
        console.log(`当前游戏模式: ${currentMode} (${GameData.getGameModeName(currentMode)})`);
        
        // 模拟分数保存
        GameData.resetScore();
        GameData.addScore(50);
        console.log(`模拟分数: ${GameData.getScore()}`);
        
        // 模拟GameOverUI.show()中的保存逻辑
        GameData.saveScore(currentMode);
        
        // 验证结果
        const easyBest = GameData.getBestScore(GameMode.NORMAL_EASY);
        const standardBest = GameData.getBestScore(GameMode.NORMAL_STANDARD);
        const hardBest = GameData.getBestScore(GameMode.NORMAL_HARD);
        
        console.log(`轻松模式最高分: ${easyBest} (期望: 50)`);
        console.log(`标准模式最高分: ${standardBest} (期望: 25)`);
        console.log(`困难模式最高分: ${hardBest} (期望: 35)`);
        
        if (easyBest === 50 && standardBest === 25 && hardBest === 35) {
            console.log("✅ GameOverUI逻辑测试通过");
        } else {
            console.log("❌ GameOverUI逻辑测试失败");
        }
    }

    /**
     * 检查localStorage的完整状态
     */
    public checkLocalStorageState() {
        console.log("\n📁 localStorage完整状态:");
        
        for (let mode = 0; mode <= 5; mode++) {
            const bestKey = `BestScore_Mode_${mode}`;
            const topKey = `TopScores_Mode_${mode}`;
            const bestValue = localStorage.getItem(bestKey);
            const topValue = localStorage.getItem(topKey);
            const modeName = GameData.getGameModeName(mode as GameMode);
            
            console.log(`${modeName} (${mode}):`);
            console.log(`  - ${bestKey}: ${bestValue}`);
            console.log(`  - ${topKey}: ${topValue}`);
        }
    }

    /**
     * 手动重新启用云同步
     */
    public async reEnableCloudSync() {
        try {
            const gameDataManager = GameDataManager.instance;
            if (gameDataManager) {
                gameDataManager.setAutoSyncEnabled(true);
                console.log("✅ 已重新启用云数据自动同步");
            }
        } catch (error) {
            console.log("⚠️ 重新启用云同步失败:", error);
        }
    }

    /**
     * 清除所有记录
     */
    public clearAllRecords() {
        GameData.clearAllGameRecords();
        console.log("✅ 已清除所有游戏记录");
    }
}
