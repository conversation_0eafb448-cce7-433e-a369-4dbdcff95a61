import { _decorator, Component } from 'cc';
import { EnergyManager } from './EnergyManager';
import { WeChatGameStorage } from './Utils/WeChatGameStorage';
const { ccclass, property } = _decorator;

/**
 * 体力系统调试助手
 * 用于在微信小游戏环境中调试体力系统问题
 */
@ccclass('EnergyDebugHelper')
export class EnergyDebugHelper extends Component {

    start() {
        // 在开发环境中暴露调试方法到全局
        if (typeof window !== 'undefined') {
            this.exposeDebugMethods();
        }
        
        // 执行初始诊断
        this.performInitialDiagnostics();
    }

    /**
     * 暴露调试方法到全局
     */
    private exposeDebugMethods() {
        (window as any).energyDebug = {
            // 检查体力系统状态
            checkEnergyStatus: () => {
                return this.checkEnergyStatus();
            },
            
            // 重置体力系统
            resetEnergySystem: () => {
                this.resetEnergySystem();
            },
            
            // 测试localStorage兼容性
            testLocalStorage: () => {
                return this.testLocalStorageCompatibility();
            },
            
            // 强制设置体力值
            setEnergy: (value: number) => {
                this.forceSetEnergy(value);
            },
            
            // 获取存储统计
            getStorageStats: () => {
                return WeChatGameStorage.getStorageStats();
            },
            
            // 清理所有体力相关存储
            clearEnergyStorage: () => {
                this.clearEnergyStorage();
            }
        };
        
        console.log("体力调试方法已暴露到 window.energyDebug");
        console.log("可用方法:");
        console.log("- window.energyDebug.checkEnergyStatus() - 检查体力系统状态");
        console.log("- window.energyDebug.resetEnergySystem() - 重置体力系统");
        console.log("- window.energyDebug.testLocalStorage() - 测试localStorage兼容性");
        console.log("- window.energyDebug.setEnergy(value) - 强制设置体力值");
        console.log("- window.energyDebug.getStorageStats() - 获取存储统计");
        console.log("- window.energyDebug.clearEnergyStorage() - 清理体力存储");
    }

    /**
     * 执行初始诊断
     */
    private performInitialDiagnostics() {
        console.log("=== 体力系统初始诊断 ===");
        
        // 检查localStorage可用性
        const storageAvailable = WeChatGameStorage.isAvailable();
        console.log(`localStorage可用性: ${storageAvailable}`);
        
        // 检查当前环境
        const isWeChatGame = typeof wx !== 'undefined';
        console.log(`微信小游戏环境: ${isWeChatGame}`);
        
        // 检查体力相关存储
        const energyValue = WeChatGameStorage.getString("PlayerEnergy", "未设置");
        const recoverTime = WeChatGameStorage.getString("EnergyRecoverTime", "未设置");
        console.log(`存储的体力值: ${energyValue}`);
        console.log(`存储的恢复时间: ${recoverTime}`);
        
        // 检查EnergyManager实例
        const energyManager = EnergyManager.getInstance();
        console.log(`EnergyManager实例: ${energyManager ? '已创建' : '未创建'}`);
        
        if (energyManager) {
            const currentEnergy = energyManager.getCurrentEnergy();
            const timeUntilRecover = energyManager.getTimeUntilNextRecover();
            console.log(`当前体力: ${currentEnergy}`);
            console.log(`距离恢复时间: ${timeUntilRecover}ms`);
            
            // 检查是否有NaN值
            if (isNaN(currentEnergy)) {
                console.error("❌ 检测到体力值为NaN！");
            }
            if (isNaN(timeUntilRecover)) {
                console.error("❌ 检测到恢复时间为NaN！");
            }
        }
        
        console.log("=== 诊断完成 ===");
    }

    /**
     * 检查体力系统状态
     */
    private checkEnergyStatus() {
        const energyManager = EnergyManager.getInstance();
        
        const status = {
            managerExists: !!energyManager,
            currentEnergy: energyManager ? energyManager.getCurrentEnergy() : "N/A",
            maxEnergy: EnergyManager.MAX_ENERGY,
            timeUntilRecover: energyManager ? energyManager.getTimeUntilNextRecover() : "N/A",
            nextRecoverTime: energyManager ? energyManager.getNextRecoverTime() : "N/A",
            storageEnergy: WeChatGameStorage.getString("PlayerEnergy", "未设置"),
            storageRecoverTime: WeChatGameStorage.getString("EnergyRecoverTime", "未设置"),
            hasNaNValues: false
        };
        
        // 检查NaN值
        if (energyManager) {
            const currentEnergy = energyManager.getCurrentEnergy();
            const timeUntilRecover = energyManager.getTimeUntilNextRecover();
            status.hasNaNValues = isNaN(currentEnergy) || isNaN(timeUntilRecover);
        }
        
        console.log("体力系统状态:", status);
        return status;
    }

    /**
     * 重置体力系统
     */
    private resetEnergySystem() {
        console.log("重置体力系统...");
        
        // 清理存储
        WeChatGameStorage.setItem("PlayerEnergy", EnergyManager.MAX_ENERGY);
        WeChatGameStorage.setItem("EnergyRecoverTime", 0);
        
        // 如果EnergyManager存在，强制重新初始化
        const energyManager = EnergyManager.getInstance();
        if (energyManager) {
            // 通过反射访问私有方法（仅用于调试）
            (energyManager as any)._currentEnergy = EnergyManager.MAX_ENERGY;
            (energyManager as any)._nextRecoverTime = 0;
            (energyManager as any)._initialized = false;
            
            // 重新初始化
            (energyManager as any).initialize();
        }
        
        console.log("体力系统已重置");
    }

    /**
     * 测试localStorage兼容性
     */
    private testLocalStorageCompatibility() {
        console.log("测试localStorage兼容性...");
        
        const testResults = {
            basicWrite: false,
            basicRead: false,
            numberWrite: false,
            numberRead: false,
            errorHandling: false
        };
        
        try {
            // 基本写入测试
            localStorage.setItem("test_basic", "test_value");
            testResults.basicWrite = true;
            
            // 基本读取测试
            const basicValue = localStorage.getItem("test_basic");
            testResults.basicRead = basicValue === "test_value";
            
            // 数字写入测试
            localStorage.setItem("test_number", "123");
            testResults.numberWrite = true;
            
            // 数字读取测试
            const numberValue = localStorage.getItem("test_number");
            const parsedNumber = parseInt(numberValue || "");
            testResults.numberRead = !isNaN(parsedNumber) && parsedNumber === 123;
            
            // 清理测试数据
            localStorage.removeItem("test_basic");
            localStorage.removeItem("test_number");
            
            testResults.errorHandling = true;
        } catch (error) {
            console.error("localStorage测试出错:", error);
        }
        
        console.log("localStorage兼容性测试结果:", testResults);
        return testResults;
    }

    /**
     * 强制设置体力值
     */
    private forceSetEnergy(value: number) {
        if (isNaN(value) || value < 0 || value > EnergyManager.MAX_ENERGY) {
            console.error(`无效的体力值: ${value}`);
            return;
        }
        
        console.log(`强制设置体力值为: ${value}`);
        
        // 更新存储
        WeChatGameStorage.setItem("PlayerEnergy", value);
        
        // 更新EnergyManager实例
        const energyManager = EnergyManager.getInstance();
        if (energyManager) {
            (energyManager as any)._currentEnergy = value;
            
            // 如果体力满了，清除恢复时间
            if (value >= EnergyManager.MAX_ENERGY) {
                (energyManager as any)._nextRecoverTime = 0;
                WeChatGameStorage.setItem("EnergyRecoverTime", 0);
            }
            
            // 通知UI更新
            (energyManager as any).notifyEnergyChange();
        }
        
        console.log("体力值设置完成");
    }

    /**
     * 清理体力相关存储
     */
    private clearEnergyStorage() {
        console.log("清理体力相关存储...");
        
        WeChatGameStorage.removeItem("PlayerEnergy");
        WeChatGameStorage.removeItem("EnergyRecoverTime");
        
        console.log("体力存储已清理");
    }
}
