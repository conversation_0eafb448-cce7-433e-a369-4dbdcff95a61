/*
 * 开放数据域入口脚本
 * 仅运行在小游戏开放数据域环境，用于：
 * 1) 代主域调用 wx.getFriendCloudStorage / wx.setUserCloudStorage
 * 2) 在 sharedCanvas 上绘制好友排行榜
 */

function getSharedCanvas() {
  try {
    if (typeof wx !== 'undefined' && wx.getSharedCanvas) return wx.getSharedCanvas();
  } catch (e) {}
  try {
    // 部分环境下开放数据域的全局 canvas 即为 sharedCanvas
    if (typeof globalThis !== 'undefined' && (globalThis).sharedCanvas) return (globalThis).sharedCanvas;
  } catch (e) {}
  // 兜底：globalThis.canvas
  return (typeof globalThis !== 'undefined' && (globalThis).canvas) ? (globalThis).canvas : null;
}

function extractBestScoreFromKV(kvValue, mode) {
  try {
    const obj = JSON.parse(kvValue || '{}');
    const arr = obj && obj[mode];
    if (Array.isArray(arr) && arr.length > 0) {
      const nums = arr.filter(n => typeof n === 'number' && !isNaN(n));
      if (nums.length > 0) return Math.max.apply(null, nums);
    }
  } catch (e) {}
  return 0;
}

// 缓存上一份好友数据，避免偶发返回空数组导致只显示“自己”
let __lastFriendsRaw = [];

// 头像资源（固定企鹅头像），用于在名次与昵称之间显示
const AVATAR_CANDIDATE_SRCS = [
  '1_penguin_home.png',
  './1_penguin_home.png',
  '/openDataContext/1_penguin_home.png',
  'openDataContext/1_penguin_home.png',
  '/1_penguin_home.png'
];
let __avatarImg = null;
let __avatarReady = false;
let __lastDrawArgs = null;
let __avatarSrcIndex = 0;
function ensureAvatarImage() {
  try {
    if (!__avatarImg && typeof wx !== 'undefined' && wx.createImage) {
      const img = wx.createImage();
      img.onload = () => {
        __avatarReady = true;
        console.log('[openData] 头像图片加载成功:', AVATAR_CANDIDATE_SRCS[__avatarSrcIndex]);
        // 首次加载完成后重绘一次，确保头像显示
        if (__lastDrawArgs) {
          try { drawFriendsRanking(__lastDrawArgs); } catch (e) {}
        }
      };
      img.onerror = (e) => {
        console.warn('[openData] 头像图片加载失败，尝试下一个路径:', AVATAR_CANDIDATE_SRCS[__avatarSrcIndex], e);
        if (__avatarSrcIndex + 1 < AVATAR_CANDIDATE_SRCS.length) {
          __avatarSrcIndex++;
          try { img.src = AVATAR_CANDIDATE_SRCS[__avatarSrcIndex]; } catch (_) {}
        }
      };
      try { img.src = AVATAR_CANDIDATE_SRCS[__avatarSrcIndex]; } catch (_) {}
      __avatarImg = img;
    }
  } catch (e) {}
  return __avatarImg;
}


function drawFriendsRanking({ mode = 0, width = 720, height = 1280, dpr = 2 }) {
  // 记录最新绘制参数，用于头像首载完成后触发重绘
  __lastDrawArgs = { mode, width, height, dpr };
  // 确保头像资源开始加载
  ensureAvatarImage();

  const cvs = getSharedCanvas();
  if (!cvs) {
    console.warn('[openData] 未获取到 sharedCanvas');
    return;
  }
  cvs.width = Math.max(1, Math.floor(width));
  cvs.height = Math.max(1, Math.floor(height));
  const ctx = cvs.getContext('2d');
  if (!ctx) return;

  // 背景（向右移2像素，填满剩余canvas宽度）
  const bgRightOffset = 2;
  ctx.fillStyle = '#101218';
  ctx.fillRect(bgRightOffset, 0, cvs.width - bgRightOffset, cvs.height);
  ctx.fillStyle = '#FFFFFF';
  ctx.textBaseline = 'top';
  // 说明：标题文字在主域已有，这里不再重复绘制

  const keyList = ['topScores'];
  const getFriends = new Promise((resolve) => {
    if (typeof wx.getFriendCloudStorage !== 'function') {
      console.warn('[openData] 当前环境不支持 getFriendCloudStorage');
      resolve([]);
      return;
    }
    wx.getFriendCloudStorage({
      keyList,
      success: (res) => resolve(res?.data || []),
      fail: (err) => { console.error('[openData] 获取好友数据失败', err); resolve([]); }
    });
  });

  const getSelf = new Promise((resolve) => {
    if (typeof wx.getUserCloudStorage !== 'function') { resolve(null); return; }
    wx.getUserCloudStorage({
      keyList,
      success: (res) => resolve(res?.KVDataList || []),
      fail: () => resolve(null)
    });
  });

  Promise.all([getFriends, getSelf]).then(([friends, selfKV]) => {
    try {
      // 如果本次获取为空，回退到上一份非空结果
      const effectiveFriends = (Array.isArray(friends) && friends.length > 0)
        ? (__lastFriendsRaw = friends)
        : (__lastFriendsRaw && __lastFriendsRaw.length > 0 ? __lastFriendsRaw : []);

      // 直接使用好友数据，不再额外添加"我"的条目
      const merged = effectiveFriends.map(f => {
        const kv = (f.KVDataList || []).find(k => k.key === 'topScores');
        const score = extractBestScoreFromKV(kv && kv.value, mode);
        return {
          id: f.openid || f.openId || '',
          nickname: f.nickname || f.nickName || '好友',
          avatarUrl: f.avatarUrl || '',
          score,
          isSelf: false,
        };
      });

      // 排序与截断
      merged.sort((a, b) => b.score - a.score);
      const list = merged.slice(0, 100);

      // 绘制榜单（排行榜条目保持原来的位置和宽度）
      const rightOffset = 0; // 不偏移
      const widthScale = 1.0; // 宽度保持原来的1倍
      const padding = 8 * dpr;
      const startY = 40 * dpr; // 顶部无标题，列表更靠上
      const lineH = 16 * dpr;
      const nameX = 35 * dpr;
      const scoreX = cvs.width * widthScale - 8 * dpr;

      ctx.font = `${6 * dpr}px sans-serif`;
      list.forEach((item, idx) => {
        const y = startY + idx * lineH;
        // 行底色（保持原来的位置和宽度）
        ctx.fillStyle = idx % 2 === 0 ? '#161a22' : '#1c2230';
        ctx.fillRect(padding / 2, y - 4 * dpr, (cvs.width - padding) * widthScale, lineH - 4 * dpr);

        // 排名（保持原来的位置）
        ctx.fillStyle = '#FFFFFF';
        ctx.fillText(String(idx + 1), padding, y);

        // 头像（在名次与昵称之间，放在20*dpr位置，大小40×40，并下移以居中）
        if (__avatarReady && __avatarImg && __avatarImg.width > 0 && __avatarImg.height > 0) {
          try {
            const avatarSize = 35; // 放大为40×40
            const avatarY = y - 3 * dpr; // 略微下移让其更接近条目垂直中心
            ctx.drawImage(__avatarImg, 20 * dpr, avatarY, avatarSize, avatarSize);
          } catch (e) {}
        }

        // 昵称（截断）
        const name = (item.nickname || '好友').slice(0, 20);
        ctx.fillText(name, nameX, y);

        // 分数（右对齐）
        const scoreText = String(item.score || 0);
        const scoreWidth = ctx.measureText(scoreText).width;
        ctx.fillText(scoreText, scoreX - scoreWidth, y);
      });

      console.log(`[openData] ✅ 排行绘制完成，条目数: ${list.length}`);
    } catch (e) {
      console.error('[openData] 绘制排行榜异常', e);
    }
  });
}

// 监听主域消息
wx.onMessage((msg) => {
  try {
    const { type } = msg || {};
    switch (type) {
      case 'SYNC_TOP_SCORES': {
        // 主域传入 { type:'SYNC_TOP_SCORES', topScores: {...} }
        const { topScores } = msg;
        if (topScores) {
          wx.setUserCloudStorage({
            KVDataList: [
              { key: 'topScores', value: JSON.stringify(topScores) },
            ],
            success: () => {
              console.log('[openData] ✅ 已将 topScores 同步到用户云存储');
            },
            fail: (err) => {
              console.error('[openData] ❌ 同步 topScores 失败', err);
            },
          });
        }
        break;
      }

      case 'GET_FRIENDS_RAW': {
        const keyList = Array.isArray(msg?.keyList) ? msg.keyList : ['topScores'];
        if (typeof wx.getFriendCloudStorage !== 'function') {
          console.warn('[openData] 当前环境不支持 getFriendCloudStorage');
          return;
        }
        console.log('[openData] 调用 getFriendCloudStorage, keyList=', keyList);
        wx.getFriendCloudStorage({
          keyList,
          success: (res) => {
            console.log(`[openData] ✅ 获取到 ${res?.data?.length || 0} 条好友数据`);
          },
          fail: (err) => {
            console.error('[openData] ❌ 获取好友数据失败', err);
          },
        });
        break;
      }

      case 'DRAW_FRIENDS_RANK': {
        const { mode = 0, width = 720, height = 1280, dpr = 2 } = msg || {};
        drawFriendsRanking({ mode, width, height, dpr });
        break;
      }

      default:
        console.log('[openData] 收到未知消息类型:', type, msg);
        break;
    }
  } catch (e) {
    console.error('[openData] 处理主域消息异常:', e);
  }
});

