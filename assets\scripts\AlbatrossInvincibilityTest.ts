import { _decorator, Component, Node, Label, Button } from 'cc';
import { AlbatrossInvincibilityManager } from './AlbatrossInvincibilityManager';
import { GameData, BirdType } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 信天翁无敌功能测试组件
 * 用于测试和调试信天翁无敌时间功能
 */
@ccclass('AlbatrossInvincibilityTest')
export class AlbatrossInvincibilityTest extends Component {
    
    @property(Button)
    testPipePassButton: Button = null; // 测试管道通过按钮
    
    @property(Button)
    resetCountButton: Button = null; // 重置计数按钮
    
    @property(Button)
    switchToAlbatrossButton: Button = null; // 切换到信天翁按钮
    
    @property(Label)
    statusLabel: Label = null; // 状态显示标签
    
    start() {
        // 绑定按钮事件
        if (this.testPipePassButton) {
            this.testPipePassButton.node.on(Button.EventType.CLICK, this.onTestPipePass, this);
        }
        
        if (this.resetCountButton) {
            this.resetCountButton.node.on(Button.EventType.CLICK, this.onResetCount, this);
        }
        
        if (this.switchToAlbatrossButton) {
            this.switchToAlbatrossButton.node.on(Button.EventType.CLICK, this.onSwitchToAlbatross, this);
        }
        
        // 初始化状态显示
        this.updateStatusDisplay();
    }
    
    update() {
        // 定期更新状态显示
        this.updateStatusDisplay();
    }
    
    /**
     * 测试管道通过事件
     */
    private onTestPipePass(): void {
        const invincibilityManager = AlbatrossInvincibilityManager.getInstance();
        if (invincibilityManager) {
            invincibilityManager.onPipePassed();
            console.log("测试：模拟管道通过事件");
        } else {
            console.error("测试错误：未找到AlbatrossInvincibilityManager实例");
        }
    }
    
    /**
     * 重置计数
     */
    private onResetCount(): void {
        const invincibilityManager = AlbatrossInvincibilityManager.getInstance();
        if (invincibilityManager) {
            invincibilityManager.resetPipeCount();
            console.log("测试：重置管道计数");
        } else {
            console.error("测试错误：未找到AlbatrossInvincibilityManager实例");
        }
    }
    
    /**
     * 切换到信天翁
     */
    private onSwitchToAlbatross(): void {
        GameData.setSelectedBirdType(BirdType.ALBATROSS);
        console.log("测试：切换到信天翁");
    }
    
    /**
     * 更新状态显示
     */
    private updateStatusDisplay(): void {
        if (!this.statusLabel) return;
        
        const selectedBirdType = GameData.getSelectedBirdType();
        const birdName = GameData.getBirdName(selectedBirdType);
        
        const invincibilityManager = AlbatrossInvincibilityManager.getInstance();
        if (invincibilityManager) {
            const pipeCount = invincibilityManager.getCurrentPipeCount();
            const isInvincible = invincibilityManager.isInvincible();
            const remainingTime = invincibilityManager.getRemainingInvincibilityTime();
            
            let statusText = `当前小鸟: ${birdName}\n`;
            statusText += `管道计数: ${pipeCount}/15\n`;
            statusText += `无敌状态: ${isInvincible ? '是' : '否'}\n`;

            if (isInvincible) {
                const countdownSeconds = Math.ceil(remainingTime);
                statusText += `剩余时间: ${remainingTime.toFixed(2)}秒\n`;
                statusText += `倒计时显示: ${countdownSeconds}`;
            }
            
            this.statusLabel.string = statusText;
        } else {
            this.statusLabel.string = `当前小鸟: ${birdName}\n无敌管理器未找到`;
        }
    }
    
    onDestroy() {
        // 清理事件监听
        if (this.testPipePassButton) {
            this.testPipePassButton.node.off(Button.EventType.CLICK, this.onTestPipePass, this);
        }
        
        if (this.resetCountButton) {
            this.resetCountButton.node.off(Button.EventType.CLICK, this.onResetCount, this);
        }
        
        if (this.switchToAlbatrossButton) {
            this.switchToAlbatrossButton.node.off(Button.EventType.CLICK, this.onSwitchToAlbatross, this);
        }
    }
}
