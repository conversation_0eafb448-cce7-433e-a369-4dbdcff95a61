import { _decorator, Component, Node, AudioClip, asset<PERSON>anager, director, Label, Sprite, Button } from 'cc';
import { AudioMgr } from '../AudioMgr';
import { GameData, GameMode } from '../GameData';
import { LeaderboardMask } from './LeaderboardMask';
import { LeaderboardUI } from './LeaderboardUI';
import { AdManager, AdType, AdCallback } from '../AdManager';
import { EnergyManager } from '../EnergyManager';
const { ccclass, property } = _decorator;

@ccclass('HomeUI')
export class HomeUI extends Component {
    @property(Node)
    mainPanel: Node = null;

    @property(Node)
    difficultyPanel: Node = null;

    @property(Node)
    challengePanel: Node = null;

    // 排行榜相关面板
    @property(Node)
    leaderboardPanel: Node = null;

    @property(Node)
    personalRecordContent: Node = null;

    @property(Node)
    friendsRankContent: Node = null;

    @property(Node)
    globalRankContent: Node = null;

    // 透明遮罩节点
    @property(Node)
    maskNode: Node = null;

    // 获取体力广告按钮
    @property(Button)
    obtainEnergyButton: Button = null;

    private leaderboardMask: LeaderboardMask = null;
    private leaderboardUI: LeaderboardUI = null;

    // 重新使用直接引用，但确保在运行时正确处理
    @property(AudioClip)
    bgAudio: AudioClip = null;

    onLoad() {
        console.log("HomeUI onLoad");

        // 先停止任何正在播放的音乐
        AudioMgr.inst.stop();

        // 检查bgAudio是否已设置
        if (this.bgAudio) {
            console.log("bgAudio已设置，准备播放主菜单BGM");
            // 确保在场景加载时立即播放主菜单BGM
            this.playHomeBGM();
        } else {
            //console.error("bgAudio未设置！请在编辑器中将home_bgm.mp3拖拽到HomeUI组件的bgAudio属性中");
            // 尝试在运行时获取bgAudio
            this.tryGetBgAudio();
            // 注意：tryGetBgAudio成功后会自动调用playHomeBGM
        }
    }

    start() {
        // 初始显示主面板，隐藏其他面板
        this.mainPanel.active = true;
        this.difficultyPanel.active = false;

        // 隐藏排行榜面板
        if (this.leaderboardPanel) {
            this.leaderboardPanel.active = false;
        }

        // 初始化透明遮罩
        this.initializeMask();

        // 初始化LeaderboardUI引用
        this.initializeLeaderboardUI();

        // 初始化获取体力广告按钮
        this.initializeObtainEnergyButton();

        // 再次检查bgAudio是否已设置
        if (!this.bgAudio) {
            console.log("start: bgAudio仍未设置，再次尝试获取");
            this.tryGetBgAudio();
        }
    }

    // 尝试在运行时获取bgAudio
    tryGetBgAudio() {
        // 这是一个备用方案，尝试通过UUID直接获取音频资源
        // 这是home_bgm.mp3的UUID，从meta文件中获取
        const HOME_BGM_UUID = "2d384416-f267-40be-9fb8-f091110d84c6";

        try {
            // 尝试通过UUID直接加载音频资源
            console.log("尝试通过UUID加载主菜单BGM:", HOME_BGM_UUID);
            assetManager.loadAny({uuid: HOME_BGM_UUID}, (err, asset) => {
                if (err) {
                    console.error("通过UUID加载主菜单BGM失败:", err);
                    return;
                }

                console.log("通过UUID加载主菜单BGM成功");
                this.bgAudio = asset as AudioClip;

                // 加载成功后立即播放
                this.playHomeBGM();
            });
        } catch (error) {
            console.error("获取bgAudio失败:", error);
        }
    }

    // 播放主菜单BGM的方法
    playHomeBGM() {
        console.log("尝试播放主菜单BGM");

        // 先停止任何正在播放的音乐
        AudioMgr.inst.stop();

        // 检查bgAudio是否已设置
        if (this.bgAudio) {
            // 延迟一帧再播放，确保音频系统已准备好
            this.scheduleOnce(() => {
                console.log("播放主菜单BGM");
                AudioMgr.inst.play(this.bgAudio, 0.1);
            }, 0);
        } else {
            console.error("无法播放主菜单BGM：bgAudio未设置");
        }
    }

    // 点击普通模式按钮
    onNormalModeBtnClick() {
        // 隐藏主面板，显示难度选择面板
        this.mainPanel.active = false;
        this.difficultyPanel.active = true;
    }

    // 点击挑战模式按钮
    onChallengeModeBtnClick() {
        // 隐藏主面板，显示挑战选择面板
        this.mainPanel.active = false;
        this.challengePanel.active = true;
    }

    // 点击金币商店按钮
    onShopButtonClick() {
        console.log("HomeUI: 进入金币商店");

        // 停止主菜单音乐
        AudioMgr.inst.stop();

        // 加载商店场景
        director.loadScene('Shop');
    }

    // 点击设置按钮
    onSettingsButtonClick() {
        console.log("HomeUI: 进入设置");

        // 停止主菜单音乐
        AudioMgr.inst.stop();

        // 加载设置场景
        director.loadScene('Settings');
    }

    // 点击获取体力广告按钮
    onObtainEnergyButtonClick() {
        console.log("HomeUI: 点击获取体力广告按钮");

        const adManager = AdManager.getInstance();
        if (!adManager) {
            console.error("HomeUI: AdManager实例不存在");
            return;
        }

        const energyManager = EnergyManager.getInstance();
        if (!energyManager) {
            console.error("HomeUI: EnergyManager实例不存在");
            return;
        }

        // 检查当前体力值
        const currentEnergy = energyManager.getCurrentEnergy();
        const maxEnergy = energyManager.getMaxEnergy();

        console.log(`HomeUI: 当前体力 ${currentEnergy}/${maxEnergy}`);

        // 如果体力已满，不需要观看广告
        if (currentEnergy >= maxEnergy) {
            console.log("HomeUI: 体力已满，无需观看广告");
            // 可以显示一个提示
            return;
        }

        // 显示广告
        const adCallback: AdCallback = {
            onSuccess: () => {
                console.log("HomeUI: 获取体力广告观看成功");
                this.handleEnergyAdReward();
            },
            onFailed: (error) => {
                console.log("HomeUI: 获取体力广告观看失败:", error);
                // 可以显示失败提示
            },
            onClosed: () => {
                console.log("HomeUI: 获取体力广告关闭");
            }
        };

        adManager.showRewardedAd(AdType.ENERGY, adCallback);
    }

    // 处理体力广告奖励
    private handleEnergyAdReward() {
        const energyManager = EnergyManager.getInstance();
        if (!energyManager) {
            console.error("HomeUI: EnergyManager实例不存在");
            return;
        }

        const currentEnergy = energyManager.getCurrentEnergy();
        const maxEnergy = energyManager.getMaxEnergy();

        let energyToAdd: number;

        if (currentEnergy > 50) {
            // 如果当前体力超过50点，补充至100点
            energyToAdd = maxEnergy - currentEnergy;
        } else {
            // 如果当前体力不超过50点，获得50点体力值
            energyToAdd = 50;
        }

        const actualAdded = energyManager.addEnergy(energyToAdd);

        console.log(`HomeUI: 体力广告奖励 - 尝试增加${energyToAdd}点，实际增加${actualAdded}点`);
        console.log(`HomeUI: 体力变化 ${currentEnergy} -> ${energyManager.getCurrentEnergy()}`);

        // 可以显示获得体力的提示UI
        // 例如：显示 "+50体力" 或 "体力已满" 的提示
    }

    // 排行榜相关方法
    // 点击排行榜按钮
    onLeaderboardBtnClick() {
        console.log("HomeUI: 打开排行榜");
        this.mainPanel.active = false;

        // 显示透明遮罩
        if (this.maskNode) {
            this.maskNode.active = true;
        }

        this.leaderboardPanel.active = true;

        // 默认显示个人记录
        this.showPersonalRecord();
        // 设置默认按钮状态
        this.updateLeaderboardButtonStates('personal');
    }

    // 点击个人记录按钮
    onPersonalRecordBtnClick() {
        console.log("HomeUI: 显示个人记录");
        this.showPersonalRecord();
        // 更新按钮状态
        this.updateLeaderboardButtonStates('personal');
    }

    // 点击好友排行榜按钮
    onFriendsRankBtnClick() {
        console.log("HomeUI: 显示好友排行榜");
        this.showFriendsRank();
        // 更新按钮状态
        this.updateLeaderboardButtonStates('friends');
    }

    // 点击全服排行榜按钮
    onGlobalRankBtnClick() {
        console.log("HomeUI: 显示全服排行榜");
        this.showGlobalRank();
        // 更新按钮状态
        this.updateLeaderboardButtonStates('global');
    }

    // 点击排行榜返回按钮
    onLeaderboardBackBtnClick() {
        console.log("HomeUI: 关闭排行榜");
        this.leaderboardPanel.active = false;

        // 隐藏透明遮罩
        if (this.maskNode) {
            this.maskNode.active = false;
        }

        this.mainPanel.active = true;
    }

    // 初始化透明遮罩
    private initializeMask() {
        if (this.maskNode) {
            // 获取遮罩组件
            this.leaderboardMask = this.maskNode.getComponent(LeaderboardMask);

            if (this.leaderboardMask) {
                console.log("HomeUI: 遮罩组件初始化成功");
            } else {
                console.warn("HomeUI: 未找到LeaderboardMask组件");
            }

            // 初始状态隐藏遮罩
            this.maskNode.active = false;
        } else {
            console.warn("HomeUI: maskNode未设置");
        }
    }

    // 初始化LeaderboardUI引用
    private initializeLeaderboardUI() {
        if (this.leaderboardPanel) {
            this.leaderboardUI = this.leaderboardPanel.getComponent(LeaderboardUI);
            if (this.leaderboardUI) {
                console.log("HomeUI: LeaderboardUI组件引用成功");
            } else {
                console.warn("HomeUI: 未找到LeaderboardUI组件");
            }
        }
    }

    // 初始化获取体力广告按钮
    private initializeObtainEnergyButton() {
        if (this.obtainEnergyButton) {
            this.obtainEnergyButton.node.on(Button.EventType.CLICK, this.onObtainEnergyButtonClick, this);
            console.log("HomeUI: 获取体力广告按钮初始化成功");
        } else {
            console.warn("HomeUI: 获取体力广告按钮未设置");
        }
    }

    // 更新排行榜按钮状态
    private updateLeaderboardButtonStates(panelType: 'personal' | 'friends' | 'global') {
        if (this.leaderboardUI) {
            // 设置LeaderboardUI的当前面板类型
            this.leaderboardUI.setCurrentPanelType(panelType);
            // 调用按钮状态更新方法
            this.leaderboardUI.forceRefreshButtonStates();
        } else {
            console.warn("HomeUI: LeaderboardUI组件未找到，无法更新按钮状态");
        }
    }

    // 显示个人记录内容
    private showPersonalRecord() {
        if (this.personalRecordContent) {
            this.personalRecordContent.active = true;
        }
        if (this.friendsRankContent) {
            this.friendsRankContent.active = false;
        }
        if (this.globalRankContent) {
            this.globalRankContent.active = false;
        }

        // 更新个人记录数据
        this.updatePersonalRecordData();
    }

    // 显示好友排行榜内容
    private showFriendsRank() {
        if (this.personalRecordContent) {
            this.personalRecordContent.active = false;
        }
        if (this.friendsRankContent) {
            this.friendsRankContent.active = true;
        }
        if (this.globalRankContent) {
            this.globalRankContent.active = false;
        }

        // 延迟初始化好友排行榜UI，避免阻塞UI切换
        this.scheduleOnce(() => {
            if (this.friendsRankContent) {
                const friendsRankUI = this.friendsRankContent.getComponent('FriendsRankUI');
                if (friendsRankUI && typeof (friendsRankUI as any).selectLevel === 'function') {
                    // 检查当前模式，避免重复初始化
                    const currentMode = (friendsRankUI as any).getCurrentGameMode();
                    if (currentMode !== GameMode.NORMAL_EASY) {
                        (friendsRankUI as any).selectLevel(GameMode.NORMAL_EASY);
                    }
                    console.log("HomeUI: 好友排行榜已初始化");
                }
            }
        }, 0);
    }

    // 显示全服排行榜内容
    private showGlobalRank() {
        if (this.personalRecordContent) {
            this.personalRecordContent.active = false;
        }
        if (this.friendsRankContent) {
            this.friendsRankContent.active = false;
        }
        if (this.globalRankContent) {
            this.globalRankContent.active = true;
        }

        // 初始化全服排行榜UI
        if (this.globalRankContent) {
            const globalRankUI = this.globalRankContent.getComponent('GlobalRankUI');
            if (globalRankUI && typeof (globalRankUI as any).selectLevel === 'function') {
                (globalRankUI as any).selectLevel(GameMode.NORMAL_EASY);
                console.log("HomeUI: 全服排行榜已初始化");
            }
        }
    }

    // 更新个人记录数据
    private updatePersonalRecordData() {
        if (!this.personalRecordContent) {
            console.error("PersonalRecordContent 未设置");
            return;
        }

        // 获取记录项容器
        const recordItemsContainer = this.personalRecordContent.getChildByName("RecordItemsContainer");
        if (!recordItemsContainer) {
            console.error("找不到 RecordItemsContainer");
            return;
        }

        // 定义关卡信息
        const levelInfo = [
            { name: "EasyRecord", mode: GameMode.NORMAL_EASY, displayName: "轻松" },
            { name: "NormalRecord", mode: GameMode.NORMAL_STANDARD, displayName: "标准" },
            { name: "HardRecord", mode: GameMode.NORMAL_HARD, displayName: "困难" },
            { name: "WindRecord", mode: GameMode.CHALLENGE_WIND, displayName: "大风吹" },
            { name: "FogRecord", mode: GameMode.CHALLENGE_FOG, displayName: "大雾起" },
            { name: "SnowRecord", mode: GameMode.CHALLENGE_SNOW, displayName: "大雪飘" }
        ];

        // 更新每个记录项
        levelInfo.forEach(level => {
            const recordNode = recordItemsContainer.getChildByName(level.name);
            if (recordNode) {
                this.updateSingleRecord(recordNode, level.mode, level.displayName);
            } else {
                console.warn(`找不到记录节点: ${level.name}`);
            }
        });
    }

    // 更新单个记录项
    private updateSingleRecord(recordNode: Node, mode: GameMode, displayName: string) {
        // 获取前三高分
        const topScores = GameData.getTopScores(mode);

        // 更新关卡名称
        const levelLabel = recordNode.getChildByName("Easy") || recordNode.getChildByName("Normal") ||
                          recordNode.getChildByName("Hard") || recordNode.getChildByName("Wind") ||
                          recordNode.getChildByName("Fog") || recordNode.getChildByName("Snow");
        if (levelLabel) {
            const labelComponent = levelLabel.getComponent(Label);
            if (labelComponent) {
                labelComponent.string = displayName;
            }
        }

        // 更新分数
        const bestScoreLabel = recordNode.getChildByName("BestScore");
        const secondScoreLabel = recordNode.getChildByName("SecondScore");
        const thirdScoreLabel = recordNode.getChildByName("ThirdScore");

        if (bestScoreLabel) {
            const labelComponent = bestScoreLabel.getComponent(Label);
            if (labelComponent) {
                // 🔧 修复：确保分数是有效数字
                const validScore = (typeof topScores[0] === 'number' && !isNaN(topScores[0])) ? topScores[0] : 0;
                labelComponent.string = validScore.toString();
            }
        }

        if (secondScoreLabel) {
            const labelComponent = secondScoreLabel.getComponent(Label);
            if (labelComponent) {
                // 🔧 修复：确保分数是有效数字
                const validScore = (typeof topScores[1] === 'number' && !isNaN(topScores[1])) ? topScores[1] : 0;
                labelComponent.string = validScore.toString();
            }
        }

        if (thirdScoreLabel) {
            const labelComponent = thirdScoreLabel.getComponent(Label);
            if (labelComponent) {
                // 🔧 修复：确保分数是有效数字
                const validScore = (typeof topScores[2] === 'number' && !isNaN(topScores[2])) ? topScores[2] : 0;
                labelComponent.string = validScore.toString();
            }
        }

        console.log(`更新${displayName}记录: [${topScores.join(', ')}]`);
    }

    onDestroy() {
        // 取消所有调度器
        this.unscheduleAllCallbacks();

        // 移除获取体力广告按钮事件监听
        if (this.obtainEnergyButton && this.obtainEnergyButton.node && this.obtainEnergyButton.node.isValid) {
            this.obtainEnergyButton.node.off(Button.EventType.CLICK, this.onObtainEnergyButtonClick, this);
        }

        // 清理组件引用
        this.leaderboardMask = null;
        this.leaderboardUI = null;

        // 清理音频资源引用
        this.bgAudio = null;

        console.log("HomeUI: 组件已销毁，资源已清理");
    }

    // 我们不需要在onDestroy中停止音乐
    // 因为当从游戏场景返回主菜单时，这会导致主菜单的BGM无法播放
    // 音乐的停止应该在切换到其他场景前进行，而不是在当前场景销毁时
}
