import { _decorator, Component, Node, Button } from 'cc';
import { BirdType, GameData } from './GameData';
const { ccclass, property } = _decorator;

@ccclass('BirdSwitcher')
export class BirdSwitcher extends Component {
    // 所有小鸟节点数组
    @property([Node])
    birdNodes: Node[] = [];

    // 所有背景节点数组
    @property([Node])
    bgNodes: Node[] = [];

    // 左右切换按钮
    @property(Node)
    leftButton: Node = null;

    @property(Node)
    rightButton: Node = null;

    // 解锁提示节点
    @property(Node)
    unlockTipNode: Node = null;

    // 当前显示的小鸟索引
    private currentBirdIndex: number = 0;

    // 已解锁的小鸟列表
    private unlockedBirds: BirdType[] = [];

    start() {
        // 获取已解锁的小鸟列表
        this.unlockedBirds = GameData.getPurchasedBirds();
        console.log("已解锁的小鸟：", this.unlockedBirds);

        // 从GameData中获取上次选择的小鸟类型
        const savedBirdType = GameData.getSelectedBirdType();

        // 检查保存的小鸟是否已解锁，如果未解锁则使用第一个已解锁的小鸟（按枚举顺序）
        let initialBirdType = savedBirdType;
        if (this.unlockedBirds.indexOf(savedBirdType) === -1) {
            // 使用排序后的第一个小鸟
            const sortedUnlockedBirds = this.getSortedUnlockedBirds();
            initialBirdType = sortedUnlockedBirds[0] || BirdType.NORMAL;
            console.log(`保存的小鸟 ${savedBirdType} 未解锁，使用 ${initialBirdType}`);
        }

        // 将小鸟类型作为当前索引
        this.currentBirdIndex = initialBirdType;
        console.log("从存储中加载小鸟类型：", initialBirdType);

        // 初始化：显示保存的小鸟和对应的背景，隐藏其他小鸟和背景
        this.showBirdAndBgAtIndex(this.currentBirdIndex);

        // 检查按钮是否存在并有效
        if (this.leftButton && this.leftButton.isValid) {
            // 为左按钮添加点击事件
            const leftBtnComp = this.leftButton.getComponent(Button);
            if (leftBtnComp) {
                leftBtnComp.clickEvents = [];
                // 添加按钮点击事件监听
                this.leftButton.on(Button.EventType.CLICK, this.onLeftButtonClick, this);
            }
        }

        if (this.rightButton && this.rightButton.isValid) {
            // 为右按钮添加点击事件
            const rightBtnComp = this.rightButton.getComponent(Button);
            if (rightBtnComp) {
                rightBtnComp.clickEvents = [];
                // 添加按钮点击事件监听
                this.rightButton.on(Button.EventType.CLICK, this.onRightButtonClick, this);
            }
        }

        console.log("BirdSwitcher初始化完成，当前显示小鸟索引：", this.currentBirdIndex);
    }

    onDestroy() {
        // 移除事件监听，防止内存泄漏
        // 添加空检查，避免在场景切换时出现null引用错误
        if (this.leftButton && this.leftButton.isValid) {
            this.leftButton.off(Button.EventType.CLICK, this.onLeftButtonClick, this);
        }

        if (this.rightButton && this.rightButton.isValid) {
            this.rightButton.off(Button.EventType.CLICK, this.onRightButtonClick, this);
        }
    }

    // 显示指定索引的小鸟和背景，隐藏其他小鸟和背景
    showBirdAndBgAtIndex(index: number) {
        if (this.birdNodes.length === 0) {
            console.error("没有配置小鸟节点！");
            return;
        }

        // 确保索引在有效范围内
        index = this.normalizeIndex(index);
        this.currentBirdIndex = index;

        // 激活当前索引的小鸟，禁用其他小鸟
        for (let i = 0; i < this.birdNodes.length; i++) {
            this.birdNodes[i].active = (i === index);
        }

        // 激活当前索引的背景，禁用其他背景
        if (this.bgNodes && this.bgNodes.length > 0) {
            for (let i = 0; i < this.bgNodes.length; i++) {
                if (this.bgNodes[i]) {
                    this.bgNodes[i].active = (i === index);
                }
            }
        } else {
            console.warn("未配置背景节点，无法切换背景");
        }

        // 保存当前选择的小鸟类型到GameData
        // 将当前索引转换为BirdType枚举
        const selectedBirdType = index as BirdType;
        GameData.setSelectedBirdType(selectedBirdType);
        console.log("切换到小鸟和背景：", index, "，保存小鸟类型：", selectedBirdType);
    }

    // 处理索引循环（当索引超出范围时循环到另一端）
    normalizeIndex(index: number): number {
        const length = this.birdNodes.length;
        // 如果索引小于0，则循环到最后一个
        if (index < 0) {
            return length - 1;
        }
        // 如果索引超出范围，则循环到第一个
        if (index >= length) {
            return 0;
        }
        return index;
    }

    // 左按钮点击事件处理
    onLeftButtonClick() {
        console.log("点击左按钮");
        this.switchToPreviousBird();
    }

    // 右按钮点击事件处理
    onRightButtonClick() {
        console.log("点击右按钮");
        this.switchToNextBird();
    }

    // 切换到上一个已解锁的小鸟
    private switchToPreviousBird() {
        if (this.unlockedBirds.length <= 1) {
            this.showUnlockTip();
            return;
        }

        // 按照BirdType枚举顺序排序已解锁的小鸟
        const sortedUnlockedBirds = this.getSortedUnlockedBirds();
        const currentIndex = sortedUnlockedBirds.indexOf(this.currentBirdIndex as BirdType);
        const previousIndex = currentIndex > 0 ? currentIndex - 1 : sortedUnlockedBirds.length - 1;
        const previousBird = sortedUnlockedBirds[previousIndex];

        this.showBirdAndBgAtIndex(previousBird);
    }

    // 切换到下一个已解锁的小鸟
    private switchToNextBird() {
        if (this.unlockedBirds.length <= 1) {
            this.showUnlockTip();
            return;
        }

        // 按照BirdType枚举顺序排序已解锁的小鸟
        const sortedUnlockedBirds = this.getSortedUnlockedBirds();
        const currentIndex = sortedUnlockedBirds.indexOf(this.currentBirdIndex as BirdType);
        const nextIndex = currentIndex < sortedUnlockedBirds.length - 1 ? currentIndex + 1 : 0;
        const nextBird = sortedUnlockedBirds[nextIndex];

        this.showBirdAndBgAtIndex(nextBird);
    }

    // 获取按BirdType枚举顺序排序的已解锁小鸟列表
    private getSortedUnlockedBirds(): BirdType[] {
        // 按照BirdType枚举值排序
        const sorted = this.unlockedBirds.slice().sort((a, b) => a - b);
        console.log("原始已解锁小鸟：", this.unlockedBirds);
        console.log("排序后的小鸟：", sorted);
        return sorted;
    }

    // 显示解锁提示
    private showUnlockTip() {
        if (this.unlockTipNode) {
            this.unlockTipNode.active = true;
            // 2秒后隐藏提示
            this.scheduleOnce(() => {
                if (this.unlockTipNode) {
                    this.unlockTipNode.active = false;
                }
            }, 2.0);
        }
        console.log("请解锁更多角色！");
    }
}
