import { _decorator, Component, Node } from 'cc';
import { GameData, GameMode } from './GameData';
import { GameDifficulty } from './GameDifficulty';
import { ChallengeMode } from './ChallengeMode';

const { ccclass, property } = _decorator;

/**
 * 快速游戏模式测试
 * 用于快速验证分数记录问题
 */
@ccclass('QuickGameModeTest')
export class QuickGameModeTest extends Component {

    onLoad() {
        console.log("=== 快速游戏模式测试开始 ===");
        
        // 延迟执行，确保其他系统初始化完成
        this.scheduleOnce(() => {
            this.runQuickTest();
        }, 1.0);
    }

    private runQuickTest() {
        console.log("\n🧪 开始快速测试分数记录问题");
        
        // 清除所有记录
        GameData.clearAllGameRecords();
        console.log("✅ 已清除所有记录");
        
        // 测试轻松模式
        this.testMode("轻松", GameDifficulty.DIFFICULTY_EASY, 0, GameMode.NORMAL_EASY, 10);
        
        // 测试标准模式
        this.testMode("标准", GameDifficulty.DIFFICULTY_NORMAL, 0, GameMode.NORMAL_STANDARD, 20);
        
        // 测试困难模式
        this.testMode("困难", GameDifficulty.DIFFICULTY_HARD, 0, GameMode.NORMAL_HARD, 30);
        
        // 验证结果
        this.verifyResults();
    }

    private testMode(name: string, difficulty: number, challengeMode: number, expectedMode: GameMode, testScore: number) {
        console.log(`\n--- 测试${name}模式 ---`);
        
        // 设置难度和挑战模式
        GameDifficulty.setDifficulty(difficulty);
        ChallengeMode.clearMode();
        
        // 验证设置
        const actualDifficulty = GameDifficulty.getDifficulty();
        const actualChallengeMode = ChallengeMode.getMode();
        console.log(`设置验证: 难度=${actualDifficulty}(期望${difficulty}), 挑战=${actualChallengeMode}(期望${challengeMode})`);
        
        // 计算游戏模式
        const calculatedMode = GameData.determineGameMode(actualDifficulty, actualChallengeMode);
        console.log(`计算模式: ${calculatedMode}(${GameData.getGameModeName(calculatedMode)}), 期望: ${expectedMode}`);
        
        // 设置当前游戏模式
        GameData.setCurrentGameMode(calculatedMode);
        
        // 验证当前模式
        const currentMode = GameData.getCurrentGameMode();
        console.log(`当前模式: ${currentMode}(${GameData.getGameModeName(currentMode)})`);
        
        // 模拟游戏分数
        GameData.resetScore();
        GameData.addScore(testScore);
        console.log(`设置分数: ${GameData.getScore()}`);
        
        // 保存分数
        console.log(`开始保存分数到模式 ${calculatedMode}`);
        GameData.saveScore(calculatedMode);
        
        // 立即验证保存结果
        const savedBest = GameData.getBestScore(calculatedMode);
        const savedTop = GameData.getTopScores(calculatedMode);
        console.log(`保存结果: 最高分=${savedBest}, 前三分=[${savedTop.join(', ')}]`);
        
        // 检查是否保存到了错误的模式
        for (let mode = 0; mode <= 5; mode++) {
            if (mode !== calculatedMode) {
                const wrongBest = GameData.getBestScore(mode as GameMode);
                const wrongTop = GameData.getTopScores(mode as GameMode);
                if (wrongBest > 0 || wrongTop.some(s => s > 0)) {
                    console.log(`⚠️ 警告: 模式${mode}(${GameData.getGameModeName(mode as GameMode)})也有分数: 最高分=${wrongBest}, 前三分=[${wrongTop.join(', ')}]`);
                }
            }
        }
    }

    private verifyResults() {
        console.log("\n📊 最终验证结果:");
        
        const expectedResults = [
            { mode: GameMode.NORMAL_EASY, name: "轻松", expected: 10 },
            { mode: GameMode.NORMAL_STANDARD, name: "标准", expected: 20 },
            { mode: GameMode.NORMAL_HARD, name: "困难", expected: 30 },
            { mode: GameMode.CHALLENGE_WIND, name: "大风吹", expected: 0 },
            { mode: GameMode.CHALLENGE_FOG, name: "大雾起", expected: 0 },
            { mode: GameMode.CHALLENGE_SNOW, name: "大雪飘", expected: 0 }
        ];
        
        let allCorrect = true;
        
        for (const { mode, name, expected } of expectedResults) {
            const actual = GameData.getBestScore(mode);
            const topScores = GameData.getTopScores(mode);
            const isCorrect = actual === expected;
            
            console.log(`${name}模式: 期望=${expected}, 实际=${actual}, 前三分=[${topScores.join(', ')}] ${isCorrect ? '✅' : '❌'}`);
            
            if (!isCorrect) {
                allCorrect = false;
            }
        }
        
        console.log(`\n🎯 测试结果: ${allCorrect ? '✅ 所有测试通过！' : '❌ 存在问题！'}`);
        
        if (!allCorrect) {
            console.log("\n🔍 问题分析:");
            console.log("如果轻松和困难模式的分数都记录到了标准模式，说明游戏模式设置有问题");
            console.log("请检查 GameDifficulty.getDifficulty() 和 ChallengeMode.getMode() 的返回值");
            
            // 输出localStorage的实际内容
            console.log("\n📁 localStorage实际内容:");
            for (let mode = 0; mode <= 5; mode++) {
                const bestKey = `BestScore_Mode_${mode}`;
                const topKey = `TopScores_Mode_${mode}`;
                const bestValue = localStorage.getItem(bestKey);
                const topValue = localStorage.getItem(topKey);
                console.log(`模式${mode}: BestScore=${bestValue}, TopScores=${topValue}`);
            }
        }
    }

    /**
     * 手动触发测试（可以在控制台调用）
     */
    public manualTest() {
        this.runQuickTest();
    }

    /**
     * 检查当前设置状态
     */
    public checkCurrentSettings() {
        console.log("\n🔍 当前设置状态:");
        console.log(`GameDifficulty.getDifficulty(): ${GameDifficulty.getDifficulty()}`);
        console.log(`ChallengeMode.getMode(): ${ChallengeMode.getMode()}`);
        console.log(`GameData.getCurrentGameMode(): ${GameData.getCurrentGameMode()}`);
        
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        const calculatedMode = GameData.determineGameMode(difficulty, challengeMode);
        console.log(`根据当前设置计算的模式: ${calculatedMode}(${GameData.getGameModeName(calculatedMode)})`);
    }

    /**
     * 清除所有记录
     */
    public clearAllRecords() {
        GameData.clearAllGameRecords();
        console.log("✅ 已清除所有游戏记录");
    }
}
