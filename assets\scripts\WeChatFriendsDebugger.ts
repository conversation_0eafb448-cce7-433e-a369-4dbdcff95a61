import { _decorator, Component, Node, Label, Button } from 'cc';
import { GameData, GameMode } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 微信好友数据调试工具
 * 用于测试和调试微信好友数据获取流程
 */
@ccclass('WeChatFriendsDebugger')
export class WeChatFriendsDebugger extends Component {
    @property(Label)
    statusLabel: Label = null;

    @property(Button)
    uploadDataButton: Button = null;

    @property(Button)
    getFriendsButton: Button = null;

    @property(Button)
    checkAuthButton: Button = null;

    @property(Button)
    testAllButton: Button = null;

    onLoad() {
        // 绑定按钮事件
        if (this.uploadDataButton) {
            this.uploadDataButton.node.on('click', this.uploadPlayerData, this);
        }
        if (this.getFriendsButton) {
            this.getFriendsButton.node.on('click', this.getFriendsData, this);
        }
        if (this.checkAuthButton) {
            this.checkAuthButton.node.on('click', this.checkAuthorization, this);
        }
        if (this.testAllButton) {
            this.testAllButton.node.on('click', this.runFullTest, this);
        }

        this.updateStatus("微信好友数据调试工具已加载");
    }

    /**
     * 更新状态显示
     */
    private updateStatus(message: string): void {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log("WeChatFriendsDebugger:", message);
    }

    /**
     * 上传当前玩家数据到微信云存储
     */
    private async uploadPlayerData(): Promise<void> {
        this.updateStatus("正在上传玩家数据...");

        try {
            if (typeof wx === 'undefined') {
                this.updateStatus("❌ 非微信环境");
                return;
            }

            if (!(wx as any).setUserCloudStorage) {
                this.updateStatus("❌ setUserCloudStorage API不可用");
                return;
            }

            // 获取当前玩家的分数数据
            const topScores = {
                [GameMode.NORMAL_EASY]: GameData.getTopScores(GameMode.NORMAL_EASY),
                [GameMode.NORMAL_STANDARD]: GameData.getTopScores(GameMode.NORMAL_STANDARD),
                [GameMode.NORMAL_HARD]: GameData.getTopScores(GameMode.NORMAL_HARD),
                [GameMode.CHALLENGE_WIND]: GameData.getTopScores(GameMode.CHALLENGE_WIND),
                [GameMode.CHALLENGE_FOG]: GameData.getTopScores(GameMode.CHALLENGE_FOG),
                [GameMode.CHALLENGE_SNOW]: GameData.getTopScores(GameMode.CHALLENGE_SNOW)
            };

            console.log("准备上传的数据:", topScores);

            // 计算最高分
            const maxScores = Object.keys(topScores).map(mode => {
                const scores = topScores[mode];
                const maxScore = Array.isArray(scores) && scores.length > 0 ? Math.max(...scores) : 0;
                return `关卡${mode}:${maxScore}`;
            });

            this.updateStatus(`准备上传数据:\n${maxScores.join('\n')}`);

            // 上传数据
            (wx as any).setUserCloudStorage({
                KVDataList: [
                    {
                        key: 'topScores',
                        value: JSON.stringify(topScores)
                    }
                ],
                success: () => {
                    this.updateStatus("✅ 数据上传成功!\n好友现在应该能看到你的数据了");
                },
                fail: (error: any) => {
                    this.updateStatus(`❌ 数据上传失败:\n${error.errMsg || JSON.stringify(error)}`);
                }
            });

        } catch (error) {
            this.updateStatus(`❌ 上传数据异常: ${error.message}`);
        }
    }

    /**
     * 获取好友数据
     */
    private async getFriendsData(): Promise<void> {
        this.updateStatus("正在获取好友数据...");

        try {
            if (typeof wx === 'undefined') {
                this.updateStatus("❌ 非微信环境");
                return;
            }

            if (!(wx as any).getFriendCloudStorage) {
                this.updateStatus("❌ getFriendCloudStorage API不可用");
                return;
            }

            (wx as any).getFriendCloudStorage({
                keyList: ['topScores'],
                success: (res: any) => {
                    console.log("获取到的好友数据:", res.data);
                    
                    let status = `✅ 获取好友数据成功!\n`;
                    status += `好友数量: ${res.data.length}\n\n`;
                    
                    if (res.data.length === 0) {
                        status += "❌ 没有好友数据\n";
                        status += "可能原因:\n";
                        status += "1. 好友没有玩过游戏\n";
                        status += "2. 好友没有上传数据\n";
                        status += "3. 没有好友信息授权\n";
                        status += "4. 在开发环境中";
                    } else {
                        status += "好友列表:\n";
                        res.data.forEach((friend: any, index: number) => {
                            status += `${index + 1}. ${friend.nickname}\n`;
                            
                            // 解析好友分数
                            try {
                                const topScoresData = friend.KVDataList.find((kv: any) => kv.key === 'topScores');
                                if (topScoresData && topScoresData.value) {
                                    const scores = JSON.parse(topScoresData.value);
                                    const maxScore = Math.max(...Object.values(scores).flat());
                                    status += `   最高分: ${maxScore}\n`;
                                }
                            } catch (e) {
                                status += `   数据解析失败\n`;
                            }
                        });
                    }
                    
                    this.updateStatus(status);
                },
                fail: (error: any) => {
                    this.updateStatus(`❌ 获取好友数据失败:\n${error.errMsg || JSON.stringify(error)}`);
                }
            });

        } catch (error) {
            this.updateStatus(`❌ 获取好友数据异常: ${error.message}`);
        }
    }

    /**
     * 检查授权状态
     */
    private async checkAuthorization(): Promise<void> {
        this.updateStatus("正在检查授权状态...");

        try {
            if (typeof wx === 'undefined') {
                this.updateStatus("❌ 非微信环境");
                return;
            }

            (wx as any).getSetting({
                success: (res: any) => {
                    console.log("授权设置:", res.authSetting);
                    
                    const friendAuth = res.authSetting['scope.WxFriendInteraction'];
                    const werunAuth = res.authSetting['scope.werun'];
                    
                    let status = "授权状态检查:\n";
                    status += `好友信息授权: ${friendAuth === true ? '✅已授权' : friendAuth === false ? '❌已拒绝' : '⚪未设置'}\n`;
                    status += `微信运动授权: ${werunAuth === true ? '✅已授权' : werunAuth === false ? '❌已拒绝' : '⚪未设置'}\n\n`;
                    
                    if (friendAuth !== true) {
                        status += "💡 建议:\n";
                        status += "需要好友信息授权才能获取好友数据\n";
                        status += "请在游戏中授权或在设置中手动开启";
                    } else {
                        status += "✅ 授权状态正常，可以获取好友数据";
                    }
                    
                    this.updateStatus(status);
                },
                fail: (error: any) => {
                    this.updateStatus(`❌ 检查授权失败: ${error.errMsg}`);
                }
            });

        } catch (error) {
            this.updateStatus(`❌ 检查授权异常: ${error.message}`);
        }
    }

    /**
     * 运行完整测试流程
     */
    private async runFullTest(): Promise<void> {
        this.updateStatus("开始完整测试流程...");

        // 1. 检查授权
        await this.checkAuthorization();
        await this.delay(2000);

        // 2. 上传数据
        await this.uploadPlayerData();
        await this.delay(2000);

        // 3. 获取好友数据
        await this.getFriendsData();
    }

    /**
     * 延迟函数
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 手动请求好友授权
     */
    public async requestFriendAuth(): Promise<void> {
        this.updateStatus("正在请求好友信息授权...");

        try {
            if (typeof wx === 'undefined') {
                this.updateStatus("❌ 非微信环境");
                return;
            }

            (wx as any).authorize({
                scope: 'scope.WxFriendInteraction',
                success: () => {
                    this.updateStatus("✅ 好友信息授权成功");
                },
                fail: (error: any) => {
                    this.updateStatus(`❌ 好友信息授权失败: ${error.errMsg}`);
                }
            });

        } catch (error) {
            this.updateStatus(`❌ 请求授权异常: ${error.message}`);
        }
    }
}
