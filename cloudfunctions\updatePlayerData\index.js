// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 使用当前云环境
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { playerData } = event
  
  try {
    // 更新玩家数据
    const result = await db.collection('players')
      .where({
        openid: wxContext.OPENID
      })
      .update({
        data: {
          ...playerData,
          updateTime: new Date()
        }
      })
    
    if (result.stats.updated > 0) {
      return {
        success: true,
        message: '玩家数据更新成功'
      }
    } else {
      return {
        success: false,
        message: '未找到要更新的玩家数据'
      }
    }
  } catch (error) {
    console.error('更新玩家数据失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
