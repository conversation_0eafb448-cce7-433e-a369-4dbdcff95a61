import { _decorator, Component, Node, Button, Label } from 'cc';
import { WeChatLoginManager } from '../WeChatLoginManager';
const { ccclass, property } = _decorator;

/**
 * 授权面板
 * 用于引导用户进行微信授权
 */
@ccclass('AuthorizationPanel')
export class AuthorizationPanel extends Component {

    @property(Button)
    authorizeButton: Button = null;

    @property(Label)
    tipLabel: Label = null;

    @property(Node)
    loadingNode: Node = null;

    private static _instance: AuthorizationPanel = null;

    public static get instance(): AuthorizationPanel {
        return this._instance;
    }

    onLoad() {
        AuthorizationPanel._instance = this;
        this.node.active = false; // 默认隐藏
    }

    start() {
        if (this.authorizeButton) {
            this.authorizeButton.node.on(Button.EventType.CLICK, this.onAuthorizeClick, this);
        }
    }

    /**
     * 显示授权面板
     */
    public show(message: string = '为了提供更好的游戏体验，需要获取您的微信授权'): void {
        this.node.active = true;
        
        if (this.tipLabel) {
            this.tipLabel.string = message;
        }
        
        if (this.loadingNode) {
            this.loadingNode.active = false;
        }
        
        if (this.authorizeButton) {
            this.authorizeButton.interactable = true;
        }
        
        console.log('AuthorizationPanel: 显示授权面板');
    }

    /**
     * 隐藏授权面板
     */
    public hide(): void {
        this.node.active = false;
        console.log('AuthorizationPanel: 隐藏授权面板');
    }

    /**
     * 显示加载状态
     */
    private showLoading(): void {
        if (this.loadingNode) {
            this.loadingNode.active = true;
        }
        
        if (this.authorizeButton) {
            this.authorizeButton.interactable = false;
        }
        
        if (this.tipLabel) {
            this.tipLabel.string = '正在获取授权...';
        }
    }

    /**
     * 授权按钮点击事件
     */
    private async onAuthorizeClick(): Promise<void> {
        console.log('AuthorizationPanel: 用户点击授权按钮');
        
        this.showLoading();
        
        try {
            const loginManager = WeChatLoginManager.instance;
            const success = await loginManager.authorizeLogin();
            
            if (success) {
                console.log('AuthorizationPanel: 授权成功');
                this.hide();
                
                // 通知游戏继续初始化
                this.notifyAuthorizationComplete(true);
            } else {
                console.log('AuthorizationPanel: 授权失败，显示重试选项');
                this.showRetryOption();
            }
        } catch (error) {
            console.error('AuthorizationPanel: 授权过程出错', error);
            this.showRetryOption();
        }
    }

    /**
     * 显示重试选项
     */
    private showRetryOption(): void {
        if (this.loadingNode) {
            this.loadingNode.active = false;
        }
        
        if (this.authorizeButton) {
            this.authorizeButton.interactable = true;
        }
        
        if (this.tipLabel) {
            this.tipLabel.string = '授权失败，请重试或在开发环境中继续';
        }
    }

    /**
     * 通知授权完成
     */
    private notifyAuthorizationComplete(success: boolean): void {
        // 可以通过事件系统通知其他组件
        console.log(`AuthorizationPanel: 授权完成，结果: ${success}`);
        
        // 这里可以添加事件分发逻辑
        // 或者直接调用游戏初始化的下一步
    }

    onDestroy() {
        if (AuthorizationPanel._instance === this) {
            AuthorizationPanel._instance = null;
        }
    }
}
