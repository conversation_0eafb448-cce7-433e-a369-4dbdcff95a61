import { _decorator, Component, Node, Label, Sprite, Button, ScrollView, Prefab, instantiate, SpriteFrame, Color } from 'cc';
import { GameData, GameMode } from '../GameData';
import { FriendsRankUI } from './FriendsRankUI';
import { GlobalRankUI } from './GlobalRankUI';
const { ccclass, property } = _decorator;

@ccclass('LeaderboardUI')
export class LeaderboardUI extends Component {
    @property(Node)
    personalRecordContent: Node = null;

    @property(Node)
    friendsRankContent: Node = null;

    @property(Node)
    globalRankContent: Node = null;

    // 个人记录相关节点
    @property(Node)
    recordItemsContainer: Node = null;

    // 奖牌容器
    @property(Node)
    medalsContainer: Node = null;

    // 排行榜切换按钮
    @property(Button)
    personalRecordBtn: Button = null;

    @property(Button)
    friendsRankBtn: Button = null;

    @property(Button)
    globalRankBtn: Button = null;

    // 好友排行榜UI组件
    private friendsRankUI: FriendsRankUI = null;

    // 全服排行榜UI组件
    private globalRankUI: GlobalRankUI = null;

    // 当前选中的面板类型
    private currentPanelType: 'personal' | 'friends' | 'global' = 'personal';

    // 按钮颜色配置
    private readonly SELECTED_COLOR = new Color(255, 225, 0, 255);       // 选中状态：橙金色
    private readonly NORMAL_COLOR = new Color(255, 255, 255, 255);     // 普通状态：白色（保持原色）

    onLoad() {
        console.log("LeaderboardUI onLoad");

        // 获取好友排行榜UI组件
        if (this.friendsRankContent) {
            this.friendsRankUI = this.friendsRankContent.getComponent(FriendsRankUI);
            if (!this.friendsRankUI) {
                console.warn("LeaderboardUI: 未找到FriendsRankUI组件");
            }
        }

        // 获取全服排行榜UI组件
        if (this.globalRankContent) {
            this.globalRankUI = this.globalRankContent.getComponent(GlobalRankUI);
            if (!this.globalRankUI) {
                console.warn("LeaderboardUI: 未找到GlobalRankUI组件");
            }
        }

        // 检查按钮配置
        this.checkButtonConfiguration();
    }

    start() {
        // 默认显示个人记录
        this.showPersonalRecord();
    }

    /**
     * 检查按钮配置是否正确
     */
    private checkButtonConfiguration(): void {
        console.log("LeaderboardUI: 检查按钮配置");

        const buttonConfigs = [
            { btn: this.personalRecordBtn, name: '个人记录按钮' },
            { btn: this.friendsRankBtn, name: '好友排行榜按钮' },
            { btn: this.globalRankBtn, name: '全服排行榜按钮' }
        ];

        buttonConfigs.forEach(({ btn, name }) => {
            if (!btn) {
                console.error(`LeaderboardUI: ${name}未配置，请在编辑器中拖拽按钮节点到对应属性`);
                return;
            }

            const sprite = btn.getComponent(Sprite);
            if (!sprite) {
                console.error(`LeaderboardUI: ${name}缺少Sprite组件，请添加Sprite组件`);
                return;
            }

            console.log(`LeaderboardUI: ${name}配置正确`);
        });
    }

    // 显示个人记录
    showPersonalRecord() {
        this.currentPanelType = 'personal';
        this.setContentVisibility(true, false, false);
        this.updateButtonStates();
        this.updatePersonalRecordData();
    }

    // 显示好友排行榜
    showFriendsRank() {
        this.currentPanelType = 'friends';
        this.setContentVisibility(false, true, false);
        this.updateButtonStates();

        // 初始化好友排行榜UI
        if (this.friendsRankUI) {
            // 默认显示轻松关卡的排行榜
            this.friendsRankUI.selectLevel(GameMode.NORMAL_EASY);
            console.log("LeaderboardUI: 好友排行榜已初始化");
        } else {
            console.error("LeaderboardUI: FriendsRankUI组件未找到");
        }
    }

    // 显示全服排行榜
    showGlobalRank() {
        this.currentPanelType = 'global';
        this.setContentVisibility(false, false, true);
        this.updateButtonStates();

        // 初始化全服排行榜UI
        if (this.globalRankUI) {
            // 默认显示轻松关卡的排行榜
            this.globalRankUI.selectLevel(GameMode.NORMAL_EASY);
            console.log("LeaderboardUI: 全服排行榜已初始化");
        } else {
            console.error("LeaderboardUI: GlobalRankUI组件未找到");
        }
    }

    // 设置内容区域的可见性
    private setContentVisibility(personal: boolean, friends: boolean, global: boolean) {
        if (this.personalRecordContent) {
            this.personalRecordContent.active = personal;
        }
        if (this.friendsRankContent) {
            this.friendsRankContent.active = friends;
        }
        if (this.globalRankContent) {
            this.globalRankContent.active = global;
        }
    }

    // 更新个人记录数据
    private updatePersonalRecordData() {
        if (!this.personalRecordContent) {
            console.error("PersonalRecordContent 未设置");
            return;
        }

        // 获取记录项容器
        const recordItemsContainer = this.personalRecordContent.getChildByName("RecordItemsContainer");
        if (!recordItemsContainer) {
            console.error("找不到 RecordItemsContainer");
            return;
        }

        // 定义关卡信息
        const levelInfo = [
            { name: "EasyRecord", mode: GameMode.NORMAL_EASY, displayName: "轻松" },
            { name: "NormalRecord", mode: GameMode.NORMAL_STANDARD, displayName: "标准" },
            { name: "HardRecord", mode: GameMode.NORMAL_HARD, displayName: "困难" },
            { name: "WindRecord", mode: GameMode.CHALLENGE_WIND, displayName: "大风吹" },
            { name: "FogRecord", mode: GameMode.CHALLENGE_FOG, displayName: "大雾起" },
            { name: "SnowRecord", mode: GameMode.CHALLENGE_SNOW, displayName: "大雪飘" }
        ];

        // 更新每个记录项
        levelInfo.forEach(level => {
            const recordNode = recordItemsContainer.getChildByName(level.name);
            if (recordNode) {
                this.updateSingleRecord(recordNode, level.mode, level.displayName);
            } else {
                console.warn(`找不到记录节点: ${level.name}`);
            }
        });

        // 更新奖牌显示
        this.updateMedalsDisplay();
    }

    // 更新单个记录项
    private updateSingleRecord(recordNode: Node, mode: GameMode, displayName: string) {
        // 获取前三高分
        const topScores = GameData.getTopScores(mode);
        
        // 更新关卡名称 - 查找所有可能的关卡名称节点
        const possibleNames = ["Easy", "Normal", "Hard", "Wind", "Fog", "Snow"];
        let levelLabel: Node = null;
        
        for (const name of possibleNames) {
            levelLabel = recordNode.getChildByName(name);
            if (levelLabel) break;
        }
        
        if (levelLabel) {
            const labelComponent = levelLabel.getComponent(Label);
            if (labelComponent) {
                labelComponent.string = displayName;
            }
        }

        // 更新分数
        this.updateScoreLabel(recordNode, "BestScore", topScores[0]);
        this.updateScoreLabel(recordNode, "SecondScore", topScores[1]);
        this.updateScoreLabel(recordNode, "ThirdScore", topScores[2]);

        console.log(`更新${displayName}记录: [${topScores.join(', ')}]`);
    }

    // 更新分数标签
    private updateScoreLabel(parentNode: Node, labelName: string, score: number) {
        const scoreLabel = parentNode.getChildByName(labelName);
        if (scoreLabel) {
            const labelComponent = scoreLabel.getComponent(Label);
            if (labelComponent) {
                // 🔧 修复：确保score是有效数字
                const validScore = (typeof score === 'number' && !isNaN(score)) ? score : 0;
                labelComponent.string = validScore.toString();
            }
        }
    }

    // 更新奖牌显示
    private updateMedalsDisplay() {
        if (!this.medalsContainer) {
            console.warn("MedalsContainer 未设置，跳过奖牌更新");
            return;
        }

        // 获取所有模式的最高分
        const allModes = [
            GameMode.NORMAL_EASY,
            GameMode.NORMAL_STANDARD,
            GameMode.NORMAL_HARD,
            GameMode.CHALLENGE_WIND,
            GameMode.CHALLENGE_FOG,
            GameMode.CHALLENGE_SNOW
        ];

        // 收集所有最高分
        const allBestScores = allModes.map(mode => GameData.getBestScore(mode));

        // 排序获取前三名
        const sortedScores = [...allBestScores].sort((a, b) => b - a);

        // 修复：金银铜牌应该始终显示，不管分数是否为0
        // 只有当所有分数都为0时，才可能隐藏奖牌，但按需求应该始终显示
        this.updateMedalSprite("gold", true);    // 金牌始终显示
        this.updateMedalSprite("silver", true);  // 银牌始终显示
        this.updateMedalSprite("bronze", true);  // 铜牌始终显示

        console.log(`奖牌显示更新: 金银铜牌均已显示, 当前最高三分: [${sortedScores.slice(0, 3).join(', ')}]`);
    }

    // 更新奖牌精灵
    private updateMedalSprite(medalName: string, isVisible: boolean) {
        const medalSprite = this.medalsContainer.getChildByName(medalName);
        if (medalSprite) {
            medalSprite.active = isVisible;
        }
    }

    /**
     * 更新按钮状态（颜色指示）
     */
    private updateButtonStates(): void {
        console.log(`LeaderboardUI: 开始更新按钮状态，当前选中: ${this.currentPanelType}`);

        const buttons = [
            { btn: this.personalRecordBtn, type: 'personal' as const, name: '个人记录' },
            { btn: this.friendsRankBtn, type: 'friends' as const, name: '好友排行榜' },
            { btn: this.globalRankBtn, type: 'global' as const, name: '全服排行榜' }
        ];

        buttons.forEach(({ btn, type, name }) => {
            if (!btn) {
                console.warn(`LeaderboardUI: ${name}按钮引用为空，请在编辑器中配置`);
                return;
            }

            const sprite = btn.getComponent(Sprite);
            if (!sprite) {
                console.warn(`LeaderboardUI: ${name}按钮没有Sprite组件，无法改变颜色`);
                return;
            }

            const isSelected = type === this.currentPanelType;
            const targetColor = isSelected ? this.SELECTED_COLOR : this.NORMAL_COLOR;
            sprite.color = targetColor;

            console.log(`LeaderboardUI: ${name}按钮 - 选中状态: ${isSelected}, 背景颜色: ${targetColor.toString()}`);
        });

        console.log(`LeaderboardUI: 按钮状态更新完成`);
    }

    // 按钮事件处理方法
    onPersonalRecordBtnClick() {
        console.log("✅ LeaderboardUI: 个人记录按钮被点击");
        this.showPersonalRecord();
    }

    onFriendsRankBtnClick() {
        console.log("✅ LeaderboardUI: 好友排行榜按钮被点击");
        this.showFriendsRank();
    }

    onGlobalRankBtnClick() {
        console.log("✅ LeaderboardUI: 全服排行榜按钮被点击");
        this.showGlobalRank();
    }

    /**
     * 强制刷新按钮状态（用于调试）
     */
    public forceRefreshButtonStates(): void {
        console.log("LeaderboardUI: 强制刷新按钮状态");
        this.updateButtonStates();
    }

    /**
     * 获取当前面板类型（用于调试）
     */
    public getCurrentPanelType(): string {
        return this.currentPanelType;
    }

    /**
     * 设置当前面板类型（供HomeUI调用）
     */
    public setCurrentPanelType(panelType: 'personal' | 'friends' | 'global'): void {
        this.currentPanelType = panelType;
        console.log(`LeaderboardUI: 面板类型已设置为 ${panelType}`);
    }

    /**
     * 强制设置按钮为纯色背景（用于测试）
     */
    public setButtonsPureColor(): void {
        console.log("LeaderboardUI: 设置按钮为纯色背景");

        const buttons = [
            { btn: this.personalRecordBtn, name: '个人记录' },
            { btn: this.friendsRankBtn, name: '好友排行榜' },
            { btn: this.globalRankBtn, name: '全服排行榜' }
        ];

        buttons.forEach(({ btn, name }) => {
            if (btn) {
                const sprite = btn.getComponent(Sprite);
                if (sprite) {
                    // 清除精灵图片，使用纯色
                    sprite.spriteFrame = null;
                    sprite.color = new Color(200, 200, 200, 255); // 设置为浅灰色
                    console.log(`${name}按钮已设置为纯色背景`);
                }
            }
        });

        // 重新更新按钮状态
        this.updateButtonStates();
    }
}
