import { _decorator } from 'cc';
import { GameMode, GameData } from '../GameData';
import { CloudDatabaseManager } from './CloudDatabaseManager';
const { ccclass } = _decorator;

/**
 * 微信好友信息接口
 */
export interface WeChatFriend {
    id: string;           // 好友ID
    nickname: string;     // 微信昵称
    avatarUrl: string;    // 微信头像URL
    scores: {             // 各关卡分数
        [GameMode.NORMAL_EASY]: number;
        [GameMode.NORMAL_STANDARD]: number;
        [GameMode.NORMAL_HARD]: number;
        [GameMode.CHALLENGE_WIND]: number;
        [GameMode.CHALLENGE_FOG]: number;
        [GameMode.CHALLENGE_SNOW]: number;
    };
}

/**
 * 排行榜条目接口
 */
export interface RankItem {
    rank: number;         // 排名
    friend: WeChatFriend; // 好友信息
    score: number;        // 该关卡分数
}

/**
 * 微信好友数据管理器
 * 模拟微信云开发API，提供本地测试数据
 */
@ccclass('WeChatFriendsData')
export class WeChatFriendsData {
    private static _instance: WeChatFriendsData = null;
    private _friendsData: WeChatFriend[] = [];
    private _globalPlayersData: WeChatFriend[] = []; // 全服玩家数据（包含好友和非好友）

    // 全服排行榜缓存（按关卡分类）
    private _globalLeaderboardCache: {[key: number]: RankItem[]} = {};
    private _globalCacheTimestamp: number = 0;

    // 好友排行榜缓存（按关卡分类）
    private _friendsLeaderboardCache: {[key: number]: RankItem[]} = {};
    private _friendsCacheTimestamp: number = 0;

    private readonly CACHE_DURATION = 5 * 60 * 1000; // 缓存5分钟

    public static get instance(): WeChatFriendsData {
        if (!this._instance) {
            this._instance = new WeChatFriendsData();
            // 异步初始化数据
            this._instance.initializeData();
        }
        return this._instance;
    }

    /**
     * 异步初始化所有数据
     */
    private async initializeData(): Promise<void> {
        try {
            // 🔧 修复：先上传当前玩家数据到微信云存储，确保好友能看到
            console.log("WeChatFriendsData: 开始上传当前玩家数据到微信云存储");
            this.syncScoresToOpenDataContext();

            await this.initializeMockData();
            await this.initializeGlobalPlayersData();
            console.log("WeChatFriendsData: 所有数据初始化完成");
        } catch (error) {
            console.error("WeChatFriendsData: 数据初始化失败", error);
        }
    }

    /**
     * 初始化好友数据（获取真实微信好友数据）
     */
    private async initializeMockData(): Promise<void> {
        console.log("WeChatFriendsData: 开始初始化微信好友数据");

        this._friendsData = [];

        try {
            // 获取真实的微信好友数据
            const friendsData = await this.getRealWeChatFriends();

            // 添加自己的数据
            const selfFriend: WeChatFriend = {
                id: "self",
                nickname: "我",
                avatarUrl: "1_penguin_home",
                scores: {
                    [GameMode.NORMAL_EASY]: this.getPlayerScore(GameMode.NORMAL_EASY),
                    [GameMode.NORMAL_STANDARD]: this.getPlayerScore(GameMode.NORMAL_STANDARD),
                    [GameMode.NORMAL_HARD]: this.getPlayerScore(GameMode.NORMAL_HARD),
                    [GameMode.CHALLENGE_WIND]: this.getPlayerScore(GameMode.CHALLENGE_WIND),
                    [GameMode.CHALLENGE_FOG]: this.getPlayerScore(GameMode.CHALLENGE_FOG),
                    [GameMode.CHALLENGE_SNOW]: this.getPlayerScore(GameMode.CHALLENGE_SNOW)
                }
            };
            this._friendsData.push(selfFriend);

            // 添加微信好友数据
            this._friendsData.push(...friendsData);

            console.log(`WeChatFriendsData: 好友数据初始化完成，共${this._friendsData.length}条数据（包含${friendsData.length}个微信好友）`);
        } catch (error) {
            console.error("WeChatFriendsData: 好友数据初始化失败", error);
        }
    }

    /**
     * 获取真实的微信好友游戏数据
     */
    private async getRealWeChatFriends(): Promise<WeChatFriend[]> {
        return new Promise((resolve) => {
            // 🔧 增强：详细的环境检查
            console.log('WeChatFriendsData: 🔍 环境检查开始');
            console.log('WeChatFriendsData: wx对象类型:', typeof wx);
            console.log('WeChatFriendsData: getFriendCloudStorage可用性:', typeof wx?.getFriendCloudStorage);
            console.log('WeChatFriendsData: setUserCloudStorage可用性:', typeof wx?.setUserCloudStorage);

            // 检查是否在微信小游戏环境中
            if (typeof wx === 'undefined') {
                console.log("WeChatFriendsData: ❌ wx对象未定义，非微信环境");
                resolve([]);
                return;
            }

            if (!wx.getFriendCloudStorage) {
                console.log("WeChatFriendsData: ❌ getFriendCloudStorage API不可用");
                resolve([]);
                return;
            }

            // 🔧 新增：检查好友信息授权状态
            this.checkFriendAuthorizationStatus().then((hasAuth) => {
                if (!hasAuth) {
                    console.log("WeChatFriendsData: ⚠️ 没有好友信息授权，无法获取好友数据");
                    resolve([]);
                    return;
                }

                console.log("WeChatFriendsData: ✅ 微信环境检查通过，开始获取好友游戏数据");

                // 🔧 增强：添加详细的调试信息
                console.log("WeChatFriendsData: 🚀 调用 wx.getFriendCloudStorage");
                console.log("WeChatFriendsData: 请求的keyList:", ['topScores']);

                wx.getFriendCloudStorage({
                    keyList: ['topScores'], // 获取好友的最高分数据
                    success: (res) => {
                        console.log(`WeChatFriendsData: ✅ 成功获取${res.data.length}个好友的游戏数据`);
                        console.log('WeChatFriendsData: 🔍 好友数据详情:', res.data);

                        // 如果没有好友数据，记录详细信息
                        if (res.data.length === 0) {
                            console.log("WeChatFriendsData: ⚠️ 没有获取到好友数据，可能的原因：");
                            console.log("WeChatFriendsData: 1. 好友没有玩过这个游戏");
                            console.log("WeChatFriendsData: 2. 好友没有上传过游戏数据");
                            console.log("WeChatFriendsData: 3. 没有获得好友信息授权 (scope.WxFriendInteraction)");
                            console.log("WeChatFriendsData: 4. 在开发环境中，好友数据可能不可用");
                        }

                        const friends: WeChatFriend[] = res.data.map((friendData) => {
                            // 解析好友的分数数据
                            let scores = {
                                [GameMode.NORMAL_EASY]: 0,
                                [GameMode.NORMAL_STANDARD]: 0,
                                [GameMode.NORMAL_HARD]: 0,
                                [GameMode.CHALLENGE_WIND]: 0,
                                [GameMode.CHALLENGE_FOG]: 0,
                                [GameMode.CHALLENGE_SNOW]: 0
                            };

                            try {
                                const topScoresData = friendData.KVDataList.find(kv => kv.key === 'topScores');
                                if (topScoresData && topScoresData.value) {
                                    const parsedScores = JSON.parse(topScoresData.value);
                                    // 计算每个关卡的最高分
                                    Object.keys(scores).forEach(mode => {
                                        if (parsedScores[mode] && Array.isArray(parsedScores[mode])) {
                                            scores[mode] = Math.max(...parsedScores[mode]);
                                        }
                                    });
                                }
                            } catch (error) {
                                console.warn(`WeChatFriendsData: 解析好友${friendData.nickname}的分数数据失败`, error);
                            }

                            return {
                                id: friendData.openid,
                                nickname: friendData.nickname,
                                avatarUrl: friendData.avatarUrl,
                                scores: scores
                            };
                        });

                        resolve(friends);
                    },
                    fail: (error) => {
                        console.error("WeChatFriendsData: ❌ 获取微信好友数据失败", error);
                        console.error("WeChatFriendsData: 🔍 错误详情:", JSON.stringify(error));
                        console.log("WeChatFriendsData: 💡 可能的解决方案：");
                        console.log("WeChatFriendsData: 1. 检查是否已获得好友信息授权 (scope.WxFriendInteraction)");
                        console.log("WeChatFriendsData: 2. 在真机上测试，而不是开发者工具");
                        console.log("WeChatFriendsData: 3. 确保有好友也在玩这个游戏");
                        console.log("WeChatFriendsData: 4. 尝试重新授权好友信息权限");
                        resolve([]);
                    }
                });
            });
        });
    }

    /**
     * 🔧 新增：检查好友信息授权状态
     */
    private async checkFriendAuthorizationStatus(): Promise<boolean> {
        // 小游戏好友数据（getFriendCloudStorage）不依赖用户授权开关；
        // 其可用性由“开放数据域环境”和后台能力、测试环境决定。
        // 这里返回 true，并仅作环境日志输出，避免误将权限作为前置条件而直接返回空数据。
        return new Promise((resolve) => {
            try {
                const hasWx = typeof wx !== 'undefined';
                const apiAvailable = hasWx && !!(wx as any).getFriendCloudStorage;
                console.log('WeChatFriendsData: 好友数据环境可用性检查 => wx存在:', hasWx, ' getFriendCloudStorage可用:', apiAvailable);
                resolve(true);
            } catch (e) {
                console.warn('WeChatFriendsData: 权限/环境检测遇到异常，继续尝试获取好友数据', e);
                resolve(true);
            }
        });
    }



    /**
     * 初始化全服玩家数据（预加载默认关卡数据）
     */
    private async initializeGlobalPlayersData(): Promise<void> {
        console.log("WeChatFriendsData: 开始初始化全服玩家数据");

        // 全服数据将在获取排行榜时动态加载，这里只做基础初始化
        this._globalPlayersData = [];

        try {
            // 初始化云数据库管理器
            const cloudDB = CloudDatabaseManager.instance;
            const initialized = await cloudDB.initialize();

            if (!initialized) {
                console.error("WeChatFriendsData: 云数据库初始化失败");
                return;
            }

            console.log("WeChatFriendsData: 云数据库初始化成功，全服数据将通过云函数动态获取");

            // 自动检查数据库状态
            await this.checkDatabaseStatus(cloudDB);

            // 注释掉自动创建缓存记录的逻辑（6条记录已手动创建）
            // console.log("WeChatFriendsData: 检查并创建排行榜缓存记录");
            // await this.ensureLeaderboardCacheExists();

            // 预加载所有关卡的排行榜数据
            console.log("WeChatFriendsData: 开始预加载所有关卡排行榜数据");
            await this.refreshAllGlobalLeaderboards();

            // 预加载好友排行榜数据
            console.log("WeChatFriendsData: 开始预加载所有关卡好友排行榜数据");
            this.refreshAllFriendsLeaderboards();

        } catch (error) {
            console.error("WeChatFriendsData: 云数据库初始化失败", error);
        }
    }

    /**
     * 检查数据库状态（调试用）
     */
    private async checkDatabaseStatus(cloudDB: CloudDatabaseManager): Promise<void> {
        console.log("=== 🔍 数据库状态检查 ===");

        try {
            // 1. 检查总数据量（分批查询以获取准确数量）
            console.log("📊 正在检查数据库总数据量...");
            let totalCount = 0;
            let batchSize = 20;
            let skip = 0;
            let hasMore = true;

            while (hasMore) {
                const batch = await cloudDB.getPlayersData(batchSize, skip);
                if (batch.length > 0) {
                    totalCount += batch.length;
                    console.log(`📦 第${Math.floor(skip/batchSize) + 1}批: ${batch.length}条数据`);

                    if (batch.length < batchSize) {
                        hasMore = false; // 最后一批
                    } else {
                        skip += batchSize;
                    }
                } else {
                    hasMore = false;
                }
            }

            console.log(`📈 数据库总计: ${totalCount}条玩家数据`);

            // 2. 检查前5名玩家的详细信息
            console.log("🏆 前5名玩家详细信息:");
            const top5 = await cloudDB.getPlayersData(5, 0);
            top5.forEach((player, index) => {
                console.log(`${index + 1}. ${player.nickname} - 金币:${player.coins} - ID:${player._id}`);
                console.log(`   分数: 简单:${Math.max(...(player.topScores[GameMode.NORMAL_EASY] || [0]))}, 标准:${Math.max(...(player.topScores[GameMode.NORMAL_STANDARD] || [0]))}, 困难:${Math.max(...(player.topScores[GameMode.NORMAL_HARD] || [0]))}`);
            });

            // 3. 测试云函数排行榜
            console.log("☁️ 测试云函数排行榜...");
            const leaderboard = await cloudDB.getGlobalLeaderboard(GameMode.NORMAL_STANDARD, 5);
            console.log(`☁️ 云函数返回: ${leaderboard.length}条排行榜数据`);
            if (leaderboard.length > 0) {
                console.log("🥇 云函数排行榜前3名:");
                leaderboard.slice(0, 3).forEach((player, index) => {
                    console.log(`${index + 1}. ${player.nickname} - 分数:${player.maxScore || 'N/A'}`);
                });
            }

        } catch (error) {
            console.error("❌ 数据库状态检查失败:", error);
        }

        console.log("=== 🔍 数据库状态检查完成 ===");
    }

    /**
     * 确保排行榜缓存记录存在，如果不存在则创建初始记录
     * 注意：6条缓存记录已手动创建，此方法暂时不使用，保留以备不时之需
     */
    private async ensureLeaderboardCacheExists(): Promise<void> {
        try {
            const cloudDB = CloudDatabaseManager.instance;

            // 开发模式下跳过
            const wxContext = await cloudDB.getWxContext();
            if (wxContext && wxContext.openid && wxContext.openid.startsWith('dev_openid_')) {
                console.log('WeChatFriendsData: 开发模式，跳过缓存记录检查');
                return;
            }

            console.log("WeChatFriendsData: 检查排行榜缓存记录是否存在");

            // 检查现有缓存记录
            const existingCache = await cloudDB.getLeaderboardCacheUpdateTimes();
            const allModes = [0, 1, 2, 3, 4, 5];
            const missingModes = allModes.filter(mode => !existingCache[mode]);

            if (missingModes.length === 0) {
                console.log("WeChatFriendsData: 所有关卡的缓存记录都已存在");
                return;
            }

            console.log(`WeChatFriendsData: 发现${missingModes.length}个关卡缺少缓存记录:`, missingModes);

            // 手动触发云函数创建缓存记录
            if (typeof wx !== 'undefined' && wx.cloud && wx.cloud.callFunction) {
                console.log("WeChatFriendsData: 调用云函数创建初始缓存记录");

                const result = await wx.cloud.callFunction({
                    name: 'updateLeaderboardCache',
                    data: {}
                });

                if (result.result && result.result.success) {
                    console.log(`WeChatFriendsData: 缓存记录创建成功 - ${result.result.message}`);
                    console.log(`WeChatFriendsData: 总玩家数: ${result.result.totalPlayers}`);
                } else {
                    console.error('WeChatFriendsData: 创建缓存记录失败:', result.result?.error);
                }
            } else {
                console.warn('WeChatFriendsData: 微信云函数不可用，无法创建缓存记录');
            }

        } catch (error) {
            console.error('WeChatFriendsData: 检查缓存记录时出错:', error);
        }
    }





    /**
     * 获取玩家在指定模式下的最高分
     */
    private getPlayerScore(mode: GameMode): number {
        return GameData.getBestScore(mode);
    }

    /**
     * 获取指定关卡的好友排行榜（按分数排序，带缓存）
     * @param mode 游戏模式
     * @param maxCount 最大返回数量，默认100
     * @returns 排行榜数据
     */
    public getFriendsRanking(mode: GameMode, maxCount: number = 100): RankItem[] {
        console.log(`WeChatFriendsData: 获取关卡 ${mode} 的好友排行榜，共${this._friendsData.length}个好友`);

        // 检查缓存是否有效
        const now = Date.now();
        if (this._friendsCacheTimestamp > 0 && (now - this._friendsCacheTimestamp) < this.CACHE_DURATION) {
            if (this._friendsLeaderboardCache[mode]) {
                console.log(`WeChatFriendsData: 使用好友排行榜缓存数据，关卡${mode}，共${this._friendsLeaderboardCache[mode].length}条`);
                return this._friendsLeaderboardCache[mode].slice(0, maxCount);
            }
        }

        // 缓存无效或不存在，重新计算所有关卡的好友排行榜
        if (this._friendsCacheTimestamp === 0 || (now - this._friendsCacheTimestamp) >= this.CACHE_DURATION) {
            console.log(`WeChatFriendsData: 好友排行榜缓存过期，重新计算所有关卡数据`);
            this.refreshAllFriendsLeaderboards();
        }

        // 返回指定关卡的数据
        if (this._friendsLeaderboardCache[mode]) {
            return this._friendsLeaderboardCache[mode].slice(0, maxCount);
        } else {
            console.warn(`WeChatFriendsData: 关卡${mode}没有好友排行榜数据`);
            return [];
        }
    }

    /**
     * 刷新所有关卡的好友排行榜缓存
     */
    private refreshAllFriendsLeaderboards(): void {
        console.log(`WeChatFriendsData: 开始刷新所有关卡的好友排行榜缓存`);

        // 清空旧缓存
        this._friendsLeaderboardCache = {};

        // 为每个关卡计算排行榜
        const allModes = [
            GameMode.NORMAL_EASY,
            GameMode.NORMAL_STANDARD,
            GameMode.NORMAL_HARD,
            GameMode.CHALLENGE_WIND,
            GameMode.CHALLENGE_FOG,
            GameMode.CHALLENGE_SNOW
        ];

        allModes.forEach(mode => {
            // 根据指定关卡分数排序
            const sortedFriends = [...this._friendsData]
                .filter(friend => {
                    const score = friend.scores[mode];
                    return typeof score === 'number' && score >= 0; // 确保分数是有效数字
                })
                .sort((a, b) => {
                    // 按分数降序排列，分数相同时按昵称排序
                    const scoreDiff = b.scores[mode] - a.scores[mode];
                    if (scoreDiff !== 0) return scoreDiff;
                    return a.nickname.localeCompare(b.nickname);
                });

            // 生成排行榜数据
            const ranking: RankItem[] = sortedFriends.map((friend, index) => ({
                rank: index + 1,
                friend: friend,
                score: friend.scores[mode]
            }));

            this._friendsLeaderboardCache[mode] = ranking;
            console.log(`WeChatFriendsData: 缓存关卡${mode}好友排行榜，共${ranking.length}条数据`);
        });

        // 更新缓存时间戳
        this._friendsCacheTimestamp = Date.now();

        const totalCached = Object.values(this._friendsLeaderboardCache).reduce((sum, data) => sum + data.length, 0);
        console.log(`WeChatFriendsData: 所有关卡好友排行榜缓存刷新完成，总计${totalCached}条数据`);
    }

    /**
     * 获取指定关卡的全服排行榜（通过云函数按分数排序，带缓存）
     * @param mode 游戏模式
     * @param maxCount 最大返回数量，默认100
     * @returns 排行榜数据
     */
    public async getGlobalRanking(mode: GameMode, maxCount: number = 100): Promise<RankItem[]> {
        console.log(`WeChatFriendsData: 获取关卡 ${mode} 的全服排行榜，前${maxCount}名`);

        // 检查缓存是否有效
        const now = Date.now();
        if (this._globalCacheTimestamp > 0 && (now - this._globalCacheTimestamp) < this.CACHE_DURATION) {
            if (this._globalLeaderboardCache[mode]) {
                console.log(`WeChatFriendsData: 使用全服排行榜缓存数据，关卡${mode}，共${this._globalLeaderboardCache[mode].length}条`);
                return this._globalLeaderboardCache[mode].slice(0, maxCount);
            }
        }

        // 缓存无效或不存在，需要重新获取所有关卡数据
        if (this._globalCacheTimestamp === 0 || (now - this._globalCacheTimestamp) >= this.CACHE_DURATION) {
            console.log(`WeChatFriendsData: 全服排行榜缓存过期，重新获取所有关卡排行榜数据`);
            await this.refreshAllGlobalLeaderboards();
        }

        // 返回指定关卡的数据
        if (this._globalLeaderboardCache[mode]) {
            return this._globalLeaderboardCache[mode].slice(0, maxCount);
        } else {
            console.warn(`WeChatFriendsData: 关卡${mode}没有排行榜数据`);
            return [];
        }
    }

    /**
     * 刷新所有关卡的全服排行榜缓存（从数据库缓存集合读取）
     */
    private async refreshAllGlobalLeaderboards(): Promise<void> {
        try {
            console.log(`WeChatFriendsData: 开始从缓存集合刷新所有关卡的全服排行榜`);

            // 初始化云数据库管理器
            const cloudDB = CloudDatabaseManager.instance;
            const initialized = await cloudDB.initialize();

            if (!initialized) {
                console.error("WeChatFriendsData: 云数据库未初始化，无法刷新缓存");
                return;
            }

            // 从缓存集合获取所有关卡的排行榜数据（本地API，不调用云函数）
            const allModesData = await cloudDB.getAllGlobalLeaderboardsFromCache();

            // 清空旧缓存
            this._globalLeaderboardCache = {};

            // 处理每个关卡的数据
            Object.keys(allModesData).forEach(modeStr => {
                const mode = parseInt(modeStr) as GameMode;
                const cloudPlayers = allModesData[mode];

                if (cloudPlayers && cloudPlayers.length > 0) {
                    // 转换为排行榜格式
                    const ranking: RankItem[] = cloudPlayers.map((player, index) => ({
                        rank: player.rank || (index + 1),
                        friend: {
                            id: player._id || `cache_${mode}_${index}`,
                            nickname: player.nickname,
                            avatarUrl: player.avatarUrl,
                            scores: {
                                // 注意：缓存数据中没有完整的topScores，这里用maxScore填充当前关卡
                                [GameMode.NORMAL_EASY]: mode === GameMode.NORMAL_EASY ? player.maxScore : 0,
                                [GameMode.NORMAL_STANDARD]: mode === GameMode.NORMAL_STANDARD ? player.maxScore : 0,
                                [GameMode.NORMAL_HARD]: mode === GameMode.NORMAL_HARD ? player.maxScore : 0,
                                [GameMode.CHALLENGE_WIND]: mode === GameMode.CHALLENGE_WIND ? player.maxScore : 0,
                                [GameMode.CHALLENGE_FOG]: mode === GameMode.CHALLENGE_FOG ? player.maxScore : 0,
                                [GameMode.CHALLENGE_SNOW]: mode === GameMode.CHALLENGE_SNOW ? player.maxScore : 0
                            }
                        },
                        score: player.maxScore || 0
                    }));

                    this._globalLeaderboardCache[mode] = ranking;
                    console.log(`WeChatFriendsData: 从缓存集合获取关卡${mode}排行榜数据，共${ranking.length}条`);
                }
            });

            // 更新缓存时间戳
            this._globalCacheTimestamp = Date.now();

            const totalCached = Object.values(this._globalLeaderboardCache).reduce((sum, data) => sum + data.length, 0);
            console.log(`WeChatFriendsData: 所有关卡全服排行榜缓存刷新完成，总计${totalCached}条数据`);

        } catch (error) {
            console.error(`WeChatFriendsData: 刷新全服排行榜缓存失败`, error);
        }
    }

    /**
     * 手动清除缓存（当玩家分数更新时调用）
     */
    public clearAllLeaderboardCache(): void {
        console.log("WeChatFriendsData: 清除所有排行榜缓存");
        // 清除全服排行榜缓存
        this._globalLeaderboardCache = {};
        this._globalCacheTimestamp = 0;
        // 清除好友排行榜缓存
        this._friendsLeaderboardCache = {};
        this._friendsCacheTimestamp = 0;
    }

    /**
     * 获取所有好友数据
     */
    public getAllFriends(): WeChatFriend[] {
        return [...this._friendsData];
    }

    /**
     * 获取所有全服玩家数据
     */
    public getAllGlobalPlayers(): WeChatFriend[] {
        return [...this._globalPlayersData];
    }

    /**
     * 根据ID获取好友信息
     */
    public getFriendById(id: string): WeChatFriend | null {
        return this._friendsData.find(friend => friend.id === id) || null;
    }

    /**
     * 更新玩家分数（当玩家创造新纪录时调用）
     */
    public updatePlayerScore(mode: GameMode, newScore: number): void {
        let updated = false;

        // 更新好友数据中的玩家分数
        const friendPlayerData = this._friendsData.find(friend => friend.id === "self");
        if (friendPlayerData && newScore > friendPlayerData.scores[mode]) {
            const oldScore = friendPlayerData.scores[mode];
            friendPlayerData.scores[mode] = newScore;
            console.log(`WeChatFriendsData: 更新好友排行榜中玩家分数 - 模式: ${mode}, 旧分数: ${oldScore}, 新分数: ${newScore}`);
            updated = true;
        }

        // 更新全服数据中的玩家分数
        const globalPlayerData = this._globalPlayersData.find(player => player.id === "self");
        if (globalPlayerData && newScore > globalPlayerData.scores[mode]) {
            const oldScore = globalPlayerData.scores[mode];
            globalPlayerData.scores[mode] = newScore;
            console.log(`WeChatFriendsData: 更新全服排行榜中玩家分数 - 模式: ${mode}, 旧分数: ${oldScore}, 新分数: ${newScore}`);
            updated = true;
        }

        if (updated) {
            console.log(`WeChatFriendsData: 排行榜数据更新完成 - 模式: ${mode}, 新分数: ${newScore}`);
            // 同步分数到微信云存储，让好友能看到
            this.syncScoresToWeChatCloudStorage();
            // 清除所有排行榜缓存，因为玩家分数可能影响排名
            this.clearAllLeaderboardCache();
            // 触发排行榜UI刷新事件（如果需要的话）
            this.notifyLeaderboardUpdate(mode, newScore);
        } else {
            console.log(`WeChatFriendsData: 分数未更新 - 模式: ${mode}, 分数: ${newScore} (可能不是新纪录)`);
        }
    }

    /**
     * 同步玩家分数到微信云存储
     */
    public syncScoresToWeChatCloudStorage(): void {
        // 检查是否在微信小游戏环境中
        if (typeof wx === 'undefined' || !(wx as any).setUserCloudStorage) {
            console.log("WeChatFriendsData: 非微信环境或API不可用，跳过云存储同步");
            return;
        }

        try {
            // 获取所有关卡的最高分数据
            const topScores = {
                [GameMode.NORMAL_EASY]: GameData.getTopScores(GameMode.NORMAL_EASY),
                [GameMode.NORMAL_STANDARD]: GameData.getTopScores(GameMode.NORMAL_STANDARD),
                [GameMode.NORMAL_HARD]: GameData.getTopScores(GameMode.NORMAL_HARD),
                [GameMode.CHALLENGE_WIND]: GameData.getTopScores(GameMode.CHALLENGE_WIND),
                [GameMode.CHALLENGE_FOG]: GameData.getTopScores(GameMode.CHALLENGE_FOG),
                [GameMode.CHALLENGE_SNOW]: GameData.getTopScores(GameMode.CHALLENGE_SNOW)
            };

            // 🔧 增强：详细的调试信息
            console.log("WeChatFriendsData: 🚀 准备上传玩家数据到微信云存储");
            console.log("WeChatFriendsData: 📊 当前玩家分数数据:", topScores);

            // 计算最高分用于调试
            const maxScores = Object.keys(topScores).map(mode => {
                const scores = topScores[mode];
                const maxScore = Array.isArray(scores) && scores.length > 0 ? Math.max(...scores) : 0;
                return `关卡${mode}:${maxScore}`;
            });
            console.log("WeChatFriendsData: 📈 各关卡最高分:", maxScores.join(', '));

            // 设置用户云存储数据
            (wx as any).setUserCloudStorage({
                KVDataList: [
                    {
                        key: 'topScores',
                        value: JSON.stringify(topScores)
                    }
                ],
                success: () => {
                    console.log("WeChatFriendsData: ✅ 成功同步分数到微信云存储");
                    console.log("WeChatFriendsData: 💡 现在好友应该能看到你的游戏数据了");
                },
                fail: (error) => {
                    console.error("WeChatFriendsData: ❌ 同步分数到微信云存储失败", error);
                    console.error("WeChatFriendsData: 🔍 错误详情:", JSON.stringify(error));
                    console.log("WeChatFriendsData: 💡 可能的原因：");
                    console.log("WeChatFriendsData: 1. 网络连接问题");
                    console.log("WeChatFriendsData: 2. 微信云存储服务异常");
                    console.log("WeChatFriendsData: 3. 数据格式问题");
                }
            });
        } catch (error) {
            console.error("WeChatFriendsData: 同步云存储数据异常", error);
        }
    }

    /**
     * 通知排行榜更新（可以用于实时刷新UI）
     */
    private notifyLeaderboardUpdate(mode: GameMode, newScore: number): void {
        // 这里可以添加事件通知机制，让排行榜UI实时更新
        // 目前只是记录日志，实际项目中可以使用事件系统
        console.log(`WeChatFriendsData: 排行榜更新通知 - 模式: ${mode}, 新分数: ${newScore}`);
    }

    /**
     * 通过开放数据域同步玩家分数（主域->开放数据域）
     */
    private syncScoresToOpenDataContext(): void {
        // 主域向开放数据域发送消息，由开放数据域调用 setUserCloudStorage
        try {
            if (typeof wx === 'undefined' || !(wx as any).getOpenDataContext) {
                console.log('WeChatFriendsData: 非微信环境或不支持开放数据域，回退到主域直接上传');
                this.syncScoresToWeChatCloudStorage();
                return;
            }

            const topScores = {
                [GameMode.NORMAL_EASY]: GameData.getTopScores(GameMode.NORMAL_EASY),
                [GameMode.NORMAL_STANDARD]: GameData.getTopScores(GameMode.NORMAL_STANDARD),
                [GameMode.NORMAL_HARD]: GameData.getTopScores(GameMode.NORMAL_HARD),
                [GameMode.CHALLENGE_WIND]: GameData.getTopScores(GameMode.CHALLENGE_WIND),
                [GameMode.CHALLENGE_FOG]: GameData.getTopScores(GameMode.CHALLENGE_FOG),
                [GameMode.CHALLENGE_SNOW]: GameData.getTopScores(GameMode.CHALLENGE_SNOW),
            } as any;

            const ctx = (wx as any).getOpenDataContext();
            if (!ctx) {
                console.warn('WeChatFriendsData: 未获取到开放数据域上下文，回退到主域直接上传');
                this.syncScoresToWeChatCloudStorage();
                return;
            }

            ctx.postMessage({ type: 'SYNC_TOP_SCORES', topScores });
            console.log('WeChatFriendsData: 📤 已向开放数据域发送分数同步消息');
        } catch (e) {
            console.error('WeChatFriendsData: 向开放数据域发送消息失败，回退到主域直接上传', e);
            this.syncScoresToWeChatCloudStorage();
        }
    }

    /**
     * 强制刷新玩家分数（从GameData读取最新分数）
     */
    public refreshPlayerScores(): void {
        console.log("WeChatFriendsData: 强制刷新玩家分数");

        // 更新好友数据中的玩家分数
        const friendPlayerData = this._friendsData.find(friend => friend.id === "self");
        if (friendPlayerData) {
            friendPlayerData.scores = {
                [GameMode.NORMAL_EASY]: this.getPlayerScore(GameMode.NORMAL_EASY),
                [GameMode.NORMAL_STANDARD]: this.getPlayerScore(GameMode.NORMAL_STANDARD),
                [GameMode.NORMAL_HARD]: this.getPlayerScore(GameMode.NORMAL_HARD),
                [GameMode.CHALLENGE_WIND]: this.getPlayerScore(GameMode.CHALLENGE_WIND),
                [GameMode.CHALLENGE_FOG]: this.getPlayerScore(GameMode.CHALLENGE_FOG),
                [GameMode.CHALLENGE_SNOW]: this.getPlayerScore(GameMode.CHALLENGE_SNOW)
            };
            console.log("WeChatFriendsData: 已更新好友排行榜中的玩家分数", friendPlayerData.scores);
        }

        // 更新全服数据中的玩家分数
        const globalPlayerData = this._globalPlayersData.find(player => player.id === "self");
        if (globalPlayerData) {
            globalPlayerData.scores = {
                [GameMode.NORMAL_EASY]: this.getPlayerScore(GameMode.NORMAL_EASY),
                [GameMode.NORMAL_STANDARD]: this.getPlayerScore(GameMode.NORMAL_STANDARD),
                [GameMode.NORMAL_HARD]: this.getPlayerScore(GameMode.NORMAL_HARD),
                [GameMode.CHALLENGE_WIND]: this.getPlayerScore(GameMode.CHALLENGE_WIND),
                [GameMode.CHALLENGE_FOG]: this.getPlayerScore(GameMode.CHALLENGE_FOG),
                [GameMode.CHALLENGE_SNOW]: this.getPlayerScore(GameMode.CHALLENGE_SNOW)
            };
            console.log("WeChatFriendsData: 已更新全服排行榜中的玩家分数", globalPlayerData.scores);
        }

        console.log("WeChatFriendsData: 玩家分数刷新完成");
    }

    /**
     * 刷新好友数据（真正从微信开放数据域重新获取最新数据）
     */
    public async refreshFriendsData(): Promise<void> {
        console.log("WeChatFriendsData: 🔄 开始刷新好友数据（重新获取）");

        try {
            // 重新获取真实的微信好友数据
            const freshFriendsData = await this.getRealWeChatFriends();

            // 保留自己的数据，更新好友数据
            const selfFriend = this._friendsData.find(friend => friend.id === "self");
            this._friendsData = [];

            // 重新添加自己的数据（保持最新的本地分数）
            if (selfFriend) {
                // 更新自己的分数为最新的本地分数
                selfFriend.scores = {
                    [GameMode.NORMAL_EASY]: this.getPlayerScore(GameMode.NORMAL_EASY),
                    [GameMode.NORMAL_STANDARD]: this.getPlayerScore(GameMode.NORMAL_STANDARD),
                    [GameMode.NORMAL_HARD]: this.getPlayerScore(GameMode.NORMAL_HARD),
                    [GameMode.CHALLENGE_WIND]: this.getPlayerScore(GameMode.CHALLENGE_WIND),
                    [GameMode.CHALLENGE_FOG]: this.getPlayerScore(GameMode.CHALLENGE_FOG),
                    [GameMode.CHALLENGE_SNOW]: this.getPlayerScore(GameMode.CHALLENGE_SNOW)
                };
                this._friendsData.push(selfFriend);
            }

            // 添加刷新后的好友数据
            this._friendsData.push(...freshFriendsData);

            // 清除排行榜缓存，强制重新计算
            this.clearAllLeaderboardCache();

            console.log(`WeChatFriendsData: ✅ 好友数据刷新完成，共${this._friendsData.length}条数据（包含${freshFriendsData.length}个微信好友）`);

        } catch (error) {
            console.error("WeChatFriendsData: ❌ 刷新好友数据失败", error);
            // 如果刷新失败，回退到模拟更新
            this.randomUpdateFriendsScores();
        }
    }

    /**
     * 模拟刷新好友数据（仅用于开发测试）
     */
    public mockRefreshFriendsData(): Promise<void> {
        return new Promise((resolve) => {
            console.log("WeChatFriendsData: 🎭 模拟刷新好友数据...");

            // 模拟网络延迟
            setTimeout(() => {
                // 随机更新一些好友的分数
                this.randomUpdateFriendsScores();
                console.log("WeChatFriendsData: 模拟好友数据刷新完成");
                resolve();
            }, 500);
        });
    }

    /**
     * 随机更新好友分数（模拟其他玩家游戏产生新纪录）
     */
    private randomUpdateFriendsScores(): void {
        const updateCount = Math.floor(Math.random() * 5) + 1; // 随机更新1-5个好友

        for (let i = 0; i < updateCount; i++) {
            const randomFriendIndex = Math.floor(Math.random() * (this._friendsData.length - 1)) + 1; // 排除自己
            const randomMode = Math.floor(Math.random() * 6) as GameMode;
            const friend = this._friendsData[randomFriendIndex];

            // 有30%概率提升分数
            if (Math.random() < 0.3) {
                const currentScore = friend.scores[randomMode];
                const improvement = Math.floor(Math.random() * 20) + 1;
                friend.scores[randomMode] = currentScore + improvement;
            }
        }

        // 清除缓存，确保下次获取排行榜时会重新计算
        this.clearAllLeaderboardCache();
    }
}
