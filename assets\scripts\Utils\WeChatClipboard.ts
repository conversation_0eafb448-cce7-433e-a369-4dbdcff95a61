import { _decorator } from 'cc';

// 微信小游戏API类型声明
declare global {
    const wx: {
        setClipboardData: (options: {
            data: string;
            success?: () => void;
            fail?: (err: any) => void;
        }) => void;
        showToast: (options: {
            title: string;
            icon?: 'success' | 'loading' | 'none';
            duration?: number;
            success?: () => void;
            fail?: (err: any) => void;
        }) => void;
        showModal: (options: {
            title?: string;
            content: string;
            showCancel?: boolean;
            success?: (res: { confirm: boolean; cancel: boolean }) => void;
            fail?: (err: any) => void;
        }) => void;
    } | undefined;
}

/**
 * 微信小游戏剪贴板工具类
 * 专门处理微信小游戏环境中的复制功能
 */
export class WeChatClipboard {
    
    /**
     * 复制文本到剪贴板
     * @param text 要复制的文本
     * @param onSuccess 成功回调
     * @param onFail 失败回调
     */
    public static copyText(
        text: string,
        onSuccess?: () => void,
        onFail?: (error: string) => void
    ): void {
        console.log(`WeChatClipboard: 尝试复制文本: ${text}`);

        // 在微信小游戏环境中，优先且主要依赖微信API
        if (typeof wx !== 'undefined') {
            console.log("WeChatClipboard: 检测到微信环境，使用微信API");
            this.tryWeChatAPI(text, onSuccess, onFail);
            return;
        }

        // 非微信环境，尝试浏览器API（主要用于开发调试）
        console.log("WeChatClipboard: 非微信环境，尝试浏览器API");

        // 方法1: 现代浏览器API
        if (this.tryModernAPI(text, onSuccess, onFail)) {
            return;
        }

        // 方法2: 传统方法
        if (this.tryLegacyAPI(text, onSuccess, onFail)) {
            return;
        }

        // 所有方法都失败，显示手动复制提示
        console.log("WeChatClipboard: 所有浏览器API都不可用");
        this.showManualCopyTip(text);
        if (onFail) {
            onFail("所有复制方法都不可用");
        }
    }
    
    /**
     * 尝试使用微信小游戏API
     */
    private static tryWeChatAPI(
        text: string,
        onSuccess?: () => void,
        onFail?: (error: string) => void
    ): boolean {
        if (typeof wx === 'undefined' || !wx.setClipboardData) {
            console.log("WeChatClipboard: 微信API不可用");
            return false;
        }

        console.log("WeChatClipboard: 使用微信API");
        try {
            wx.setClipboardData({
                data: text,
                success: () => {
                    console.log("WeChatClipboard: 微信API复制成功");
                    this.showSuccessToast();
                    if (onSuccess) onSuccess();
                },
                fail: (err: any) => {
                    console.log("WeChatClipboard: 微信API复制失败，详细错误:", err);
                    console.log("错误类型:", typeof err);
                    console.log("错误内容:", JSON.stringify(err));

                    // 分析失败原因
                    const errorMsg = err?.errMsg || err?.error || '未知错误';
                    const errno = err?.errno;
                    console.log("WeChatClipboard: 错误信息:", errorMsg);
                    console.log("WeChatClipboard: 错误码:", errno);

                    // 根据错误类型提供不同的处理
                    if (errorMsg.includes('privacy usage') || errno === 1026) {
                        console.log("WeChatClipboard: 隐私权限问题，需要在小程序后台配置");
                        this.showPrivacyErrorModal(text);
                    } else if (errorMsg.includes('user deny') || errorMsg.includes('cancel')) {
                        console.log("WeChatClipboard: 用户拒绝了复制权限");
                        this.showUserDenyModal(text);
                    } else {
                        console.log("WeChatClipboard: 其他微信API错误");
                        this.showCopyFailedModal(text);
                    }

                    if (onFail) onFail(`微信复制功能不可用: ${errorMsg}`);
                }
            });
            return true;
        } catch (error) {
            console.log("WeChatClipboard: 微信API调用异常:", error);
            return false;
        }
    }
    
    /**
     * 尝试使用现代浏览器API
     */
    private static tryModernAPI(
        text: string,
        onSuccess?: () => void,
        onFail?: (error: string) => void
    ): boolean {
        if (!navigator.clipboard || !window.isSecureContext) {
            console.log("WeChatClipboard: 现代API不可用");
            return false;
        }

        console.log("WeChatClipboard: 使用现代API");
        navigator.clipboard.writeText(text).then(() => {
            console.log("WeChatClipboard: 现代API复制成功");
            this.showSuccessToast();
            if (onSuccess) onSuccess();
        }).catch((err) => {
            console.log("WeChatClipboard: 现代API复制失败:", err);
            // 不调用onFail，让调用者决定下一步
        });

        return true;
    }
    
    /**
     * 尝试使用传统方法
     */
    private static tryLegacyAPI(
        text: string, 
        onSuccess?: () => void, 
        onFail?: (error: string) => void
    ): boolean {
        if (typeof document === 'undefined' || typeof document.execCommand !== 'function') {
            console.log("WeChatClipboard: 传统API不可用");
            return false;
        }
        
        console.log("WeChatClipboard: 使用传统API");
        try {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            textArea.style.opacity = "0";
            
            document.body.appendChild(textArea);
            textArea.focus();
            
            // 安全地选择文本
            if (typeof textArea.select === 'function') {
                textArea.select();
            } else if (typeof textArea.setSelectionRange === 'function') {
                textArea.setSelectionRange(0, textArea.value.length);
            }
            
            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);
            
            if (successful) {
                console.log("WeChatClipboard: 传统API复制成功");
                this.showSuccessToast();
                if (onSuccess) onSuccess();
                return true;
            } else {
                console.log("WeChatClipboard: 传统API复制失败");
                return false;
            }
        } catch (error) {
            console.log("WeChatClipboard: 传统API异常:", error);
            return false;
        }
    }

    /**
     * 尝试使用备用方法（input元素）
     */
    private static tryAlternativeAPI(
        text: string,
        onSuccess?: () => void,
        onFail?: (error: string) => void
    ): boolean {
        if (typeof document === 'undefined') {
            console.log("WeChatClipboard: 备用API不可用（无document）");
            return false;
        }

        console.log("WeChatClipboard: 使用备用API（input元素）");
        try {
            const input = document.createElement("input");
            input.value = text;
            input.style.position = "fixed";
            input.style.left = "-999999px";
            input.style.top = "-999999px";
            input.style.opacity = "0";
            input.style.zIndex = "-1";

            document.body.appendChild(input);

            // 聚焦并选择
            input.focus();
            input.select();

            // 尝试使用现代API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    console.log("WeChatClipboard: 备用API（现代）复制成功");
                    document.body.removeChild(input);
                    this.showSuccessToast();
                    if (onSuccess) onSuccess();
                }).catch(() => {
                    // 现代API失败，尝试execCommand
                    this.tryExecCommandOnInput(input, text, onSuccess, onFail);
                });
            } else {
                // 直接使用execCommand
                this.tryExecCommandOnInput(input, text, onSuccess, onFail);
            }

            return true;
        } catch (error) {
            console.log("WeChatClipboard: 备用API异常:", error);
            return false;
        }
    }

    /**
     * 在input元素上尝试execCommand
     */
    private static tryExecCommandOnInput(
        input: HTMLInputElement,
        text: string,
        onSuccess?: () => void,
        onFail?: (error: string) => void
    ): void {
        try {
            if (typeof document.execCommand === 'function') {
                const successful = document.execCommand('copy');
                document.body.removeChild(input);

                if (successful) {
                    console.log("WeChatClipboard: 备用API（execCommand）复制成功");
                    this.showSuccessToast();
                    if (onSuccess) onSuccess();
                } else {
                    console.log("WeChatClipboard: 备用API（execCommand）复制失败");
                }
            } else {
                console.log("WeChatClipboard: execCommand不可用");
                document.body.removeChild(input);
            }
        } catch (error) {
            console.log("WeChatClipboard: execCommand异常:", error);
            if (input.parentNode) {
                document.body.removeChild(input);
            }
        }
    }

    /**
     * 显示成功提示
     */
    private static showSuccessToast(): void {
        if (typeof wx !== 'undefined' && wx.showToast) {
            try {
                wx.showToast({
                    title: '复制成功',
                    icon: 'success',
                    duration: 2000
                });
                return;
            } catch (error) {
                console.log("showToast失败:", error);
            }
        }
        
        console.log("✅ 复制成功！");
    }
    
    /**
     * 显示隐私权限错误的模态框
     */
    private static showPrivacyErrorModal(text: string): void {
        if (typeof wx !== 'undefined' && wx.showModal) {
            try {
                wx.showModal({
                    title: '复制功能暂不可用',
                    content: `复制功能需要隐私授权，请手动复制邀请码：${text}`,
                    showCancel: false,
                    success: (res) => {
                        console.log("用户确认了隐私权限提示");
                    }
                });
                return;
            } catch (error) {
                console.log("showModal失败:", error);
            }
        }

        this.showManualCopyToast(text);
    }

    /**
     * 显示用户拒绝权限的模态框
     */
    private static showUserDenyModal(text: string): void {
        if (typeof wx !== 'undefined' && wx.showModal) {
            try {
                wx.showModal({
                    title: '复制被取消',
                    content: `请手动复制邀请码：${text}`,
                    showCancel: false,
                    success: (res) => {
                        console.log("用户确认了权限拒绝提示");
                    }
                });
                return;
            } catch (error) {
                console.log("showModal失败:", error);
            }
        }

        this.showManualCopyToast(text);
    }

    /**
     * 显示复制失败的模态框
     */
    private static showCopyFailedModal(text: string): void {
        if (typeof wx !== 'undefined' && wx.showModal) {
            try {
                wx.showModal({
                    title: '复制失败',
                    content: `请手动复制邀请码：${text}`,
                    showCancel: false,
                    success: (res) => {
                        console.log("用户确认了手动复制提示");
                    }
                });
                return;
            } catch (error) {
                console.log("showModal失败:", error);
            }
        }

        // fallback到toast
        this.showManualCopyToast(text);
    }
    
    /**
     * 显示手动复制的Toast提示
     */
    private static showManualCopyToast(text: string): void {
        if (typeof wx !== 'undefined' && wx.showToast) {
            try {
                wx.showToast({
                    title: `请手动复制：${text}`,
                    icon: 'none',
                    duration: 4000
                });
                return;
            } catch (error) {
                console.log("showToast失败:", error);
            }
        }
        
        this.showManualCopyTip(text);
    }
    
    /**
     * 显示手动复制提示（控制台）
     */
    private static showManualCopyTip(text: string): void {
        console.log("=".repeat(60));
        console.log(`📋 复制功能不可用，请手动复制邀请码：${text}`);
        console.log("=".repeat(60));
    }
    
    /**
     * 检查复制功能可用性
     */
    public static checkAvailability(): {
        environment: 'wechat' | 'browser' | 'unknown';
        wechat: boolean;
        modern: boolean;
        legacy: boolean;
        anyAvailable: boolean;
        recommendation: string;
    } {
        const wechat = typeof wx !== 'undefined' && typeof wx.setClipboardData === 'function';
        const modern = !!(navigator.clipboard && window.isSecureContext);
        const legacy = typeof document !== 'undefined' && typeof document.execCommand === 'function';

        let environment: 'wechat' | 'browser' | 'unknown' = 'unknown';
        let recommendation = '';

        if (typeof wx !== 'undefined') {
            environment = 'wechat';
            if (wechat) {
                recommendation = '微信环境，建议使用微信API';
            } else {
                recommendation = '微信环境但API不可用，只能手动复制';
            }
        } else if (typeof navigator !== 'undefined' && typeof document !== 'undefined') {
            environment = 'browser';
            if (modern) {
                recommendation = '浏览器环境，建议使用现代API';
            } else if (legacy) {
                recommendation = '浏览器环境，建议使用传统API';
            } else {
                recommendation = '浏览器环境但复制API不可用';
            }
        }

        return {
            environment,
            wechat,
            modern,
            legacy,
            anyAvailable: wechat || modern || legacy,
            recommendation
        };
    }

    /**
     * 打印环境诊断信息
     */
    public static diagnoseEnvironment(): void {
        const availability = this.checkAvailability();

        console.log("=== 剪贴板环境诊断 ===");
        console.log(`环境类型: ${availability.environment}`);
        console.log(`微信API: ${availability.wechat ? '✅ 可用' : '❌ 不可用'}`);
        console.log(`现代API: ${availability.modern ? '✅ 可用' : '❌ 不可用'}`);
        console.log(`传统API: ${availability.legacy ? '✅ 可用' : '❌ 不可用'}`);
        console.log(`任何可用: ${availability.anyAvailable ? '✅ 是' : '❌ 否'}`);
        console.log(`建议: ${availability.recommendation}`);

        if (typeof navigator !== 'undefined') {
            console.log(`用户代理: ${navigator.userAgent}`);
        }

        if (typeof window !== 'undefined') {
            console.log(`安全上下文: ${window.isSecureContext ? '✅ 是' : '❌ 否'}`);
            console.log(`协议: ${window.location?.protocol || '未知'}`);
        }

        console.log("=== 诊断完成 ===");
    }
}
