import { _decorator, Component, Node, director } from 'cc';
import { AdIdleTipManager } from './AdIdleTipManager';
const { ccclass, property } = _decorator;

// 广告类型枚举
export enum AdType {
    ENERGY = 'energy',      // 获取体力广告
    COIN_CARD = 'coin_card', // 获取金币卡广告
    REVIVE = 'revive'       // 复活广告
}

// 广告回调接口
export interface AdCallback {
    onSuccess?: () => void;  // 广告观看成功回调
    onFailed?: (error?: any) => void;   // 广告观看失败回调
    onClosed?: () => void;   // 广告关闭回调
}

/**
 * 广告管理器
 * 负责管理激励广告的显示、回调处理等
 * 支持微信小游戏广告API，同时提供测试模式
 */
@ccclass('AdManager')
export class AdManager extends Component {

    // 单例实例
    private static _instance: AdManager = null;

    // 是否为测试模式（未接入广告API时使用）
    private _isTestMode: boolean = false;

    // 微信广告单元ID配置
    private readonly AD_UNIT_IDS = {
        [AdType.ENERGY]: 'adunit-energy-id',      // 获取体力广告单元ID
        [AdType.COIN_CARD]: 'adunit-coincard-id', // 获取金币卡广告单元ID
        [AdType.REVIVE]: 'adunit-revive-id'       // 复活广告单元ID
    };

    // 广告实例缓存
    private _adInstances: Map<AdType, any> = new Map();

    onLoad() {
        // 单例模式
        if (AdManager._instance) {
            this.node.destroy();
            return;
        }

        AdManager._instance = this;

        // 检测广告API是否可用
        this.checkAdAPIAvailability();

        console.log(`AdManager 初始化完成，当前模式: ${this._isTestMode ? '测试模式' : '广告模式'}`);
    }

    onDestroy() {
        // 清理单例引用
        if (AdManager._instance === this) {
            AdManager._instance = null;
        }

        // 清理广告实例
        this._adInstances.clear();

        console.log("AdManager: 组件已销毁，资源已清理");
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): AdManager | null {
        if (!AdManager._instance) {
            // 如果实例不存在，尝试创建一个
            AdManager.createInstance();
        }
        return AdManager._instance;
    }

    /**
     * 创建AdManager实例
     */
    private static createInstance(): void {
        // 在当前场景中查找是否已有AdManager节点
        const scene = director.getScene();
        if (scene) {
            const existingAdManager = scene.getComponentInChildren(AdManager);
            if (existingAdManager) {
                AdManager._instance = existingAdManager;
                console.log("AdManager: 找到现有实例");
                return;
            }
        }

        // 如果没有找到，创建一个新的AdManager节点
        const adManagerNode = new Node('AdManager');
        adManagerNode.addComponent(AdManager);

        // 将节点添加到当前场景
        if (scene) {
            scene.addChild(adManagerNode);
            console.log("AdManager: 创建新实例并添加到场景");
        } else {
            console.error("AdManager: 无法获取当前场景，无法创建实例");
        }
    }

    /**
     * 检测广告API是否可用
     */
    private checkAdAPIAvailability(): void {
        // 检查是否在微信小游戏环境中
        if (typeof wx !== 'undefined' && (wx as any).createRewardedVideoAd) {
            console.log("AdManager: 检测到微信小游戏广告API");

            // 进一步检查是否在开发环境中，或者广告位ID是否为测试ID
            const isDevEnvironment = this.isInDevelopmentEnvironment();
            const hasValidAdUnitIds = this.hasValidAdUnitIds();

            if (isDevEnvironment || !hasValidAdUnitIds) {
                console.log("AdManager: 检测到开发环境或广告位ID未配置，启用测试模式");
                this._isTestMode = true;
            } else {
                console.log("AdManager: 生产环境且广告位ID已配置，启用广告模式");
                this._isTestMode = false;
                this.initializeWeChatAds();
            }
        } else {
            console.log("AdManager: 未检测到广告API，启用测试模式");
            this._isTestMode = true;
        }
    }

    /**
     * 检查是否在开发环境中
     */
    private isInDevelopmentEnvironment(): boolean {
        // 检查微信开发者工具环境
        if (typeof wx !== 'undefined') {
            const systemInfo = (wx as any).getSystemInfoSync();
            if (systemInfo && systemInfo.platform === 'devtools') {
                console.log("AdManager: 检测到微信开发者工具环境");
                return true;
            }
        }

        // 检查是否在浏览器环境中
        if (typeof window !== 'undefined' && window.location) {
            const hostname = window.location.hostname;
            if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('dev')) {
                console.log("AdManager: 检测到浏览器开发环境");
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否有有效的广告位ID配置
     */
    private hasValidAdUnitIds(): boolean {
        const adUnitIds = Object.values(this.AD_UNIT_IDS);
        const validIds = adUnitIds.filter(id =>
            id &&
            id !== 'adunit-energy-id' &&
            id !== 'adunit-coincard-id' &&
            id !== 'adunit-revive-id' &&
            !id.includes('test') &&
            !id.includes('demo')
        );

        console.log(`AdManager: 有效广告位ID数量: ${validIds.length}/${adUnitIds.length}`);
        return validIds.length > 0;
    }

    /**
     * 初始化微信小游戏广告
     */
    private initializeWeChatAds(): void {
        // 为每种广告类型创建广告实例
        Object.values(AdType).forEach(adType => {
            try {
                const adUnitId = this.AD_UNIT_IDS[adType];
                if (adUnitId && typeof wx !== 'undefined') {
                    const adInstance = (wx as any).createRewardedVideoAd({
                        adUnitId: adUnitId
                    });

                    // 添加广告实例的错误监听，用于检测广告位配置问题
                    adInstance.onError((err: any) => {
                        console.warn(`AdManager: ${adType}广告实例错误:`, err);
                        if (err && err.errMsg) {
                            if (err.errMsg.includes('adunit') ||
                                err.errMsg.includes('流量主') ||
                                err.errMsg.includes('no advertisement data available')) {
                                console.log(`AdManager: ${adType}广告位可能未配置或流量主未开通，将在显示时切换到测试模式`);
                            }
                        }
                    });

                    this._adInstances.set(adType, adInstance);
                    console.log(`AdManager: 创建${adType}广告实例成功，广告位ID: ${adUnitId}`);
                }
            } catch (error) {
                console.error(`AdManager: 创建${adType}广告实例失败:`, error);
                // 创建失败时，该广告类型将在显示时自动使用测试模式
            }
        });
    }

    /**
     * 显示激励广告
     * @param adType 广告类型
     * @param callback 广告回调
     */
    public showRewardedAd(adType: AdType, callback?: AdCallback): void {
        console.log(`AdManager: 请求显示${adType}广告`);

        if (this._isTestMode) {
            // 测试模式：显示广告空闲提示，不给奖励
            this.showAdIdleTip(adType, callback);
            return;
        }

        // 检查广告实例是否存在
        const adInstance = this._adInstances.get(adType);
        if (!adInstance) {
            console.warn(`AdManager: ${adType}广告实例不存在，切换到测试模式`);
            this.showAdIdleTip(adType, callback);
            return;
        }

        // 微信小游戏广告模式
        this.showWeChatAd(adType, callback);
    }

    /**
     * 显示微信小游戏广告
     */
    private showWeChatAd(adType: AdType, callback?: AdCallback): void {
        const adInstance = this._adInstances.get(adType);

        if (!adInstance) {
            console.error(`AdManager: 未找到${adType}广告实例`);
            if (callback?.onFailed) {
                callback.onFailed('广告实例不存在');
            }
            return;
        }

        // 设置广告回调
        const onCloseHandler = (res: any) => {
            if (res && res.isEnded) {
                // 用户观看完整广告
                console.log(`AdManager: ${adType}广告观看完成`);
                if (callback?.onSuccess) {
                    callback.onSuccess();
                }
            } else {
                // 用户中途关闭广告
                console.log(`AdManager: ${adType}广告被中途关闭`);
                if (callback?.onFailed) {
                    callback.onFailed('广告未观看完整');
                }
            }

            if (callback?.onClosed) {
                callback.onClosed();
            }

            // 移除事件监听
            adInstance.offClose(onCloseHandler);
            adInstance.offError(onErrorHandler);
        };

        const onErrorHandler = (err: any) => {
            console.error(`AdManager: ${adType}广告播放错误:`, err);

            // 检查是否是广告数据未加载的错误
            if (err && err.errMsg && err.errMsg.includes('no advertisement data available')) {
                console.log(`AdManager: ${adType}广告数据未加载，显示广告空闲提示`);
                this.showAdIdleTip(adType, callback);
            } else {
                if (callback?.onFailed) {
                    callback.onFailed(err);
                }
            }

            // 移除事件监听
            adInstance.offClose(onCloseHandler);
            adInstance.offError(onErrorHandler);
        };

        // 绑定事件监听
        adInstance.onClose(onCloseHandler);
        adInstance.onError(onErrorHandler);

        // 先尝试加载广告，然后显示
        console.log(`AdManager: 开始加载${adType}广告数据`);
        adInstance.load().then(() => {
            console.log(`AdManager: ${adType}广告数据加载成功，开始显示`);
            // 广告加载成功，显示广告
            return adInstance.show();
        }).catch((loadErr: any) => {
            console.warn(`AdManager: ${adType}广告加载失败:`, loadErr);

            // 检查是否是流量主未开通或广告位配置问题
            if (loadErr && loadErr.errMsg) {
                if (loadErr.errMsg.includes('no advertisement data available') ||
                    loadErr.errMsg.includes('adunit') ||
                    loadErr.errMsg.includes('流量主')) {
                    console.log(`AdManager: 检测到广告位未配置或流量主未开通，显示广告空闲提示`);
                    this.showAdIdleTip(adType, callback);
                    return;
                }
            }

            // 广告加载失败，尝试直接显示（兼容某些情况）
            console.log(`AdManager: 广告加载失败，尝试直接显示${adType}广告`);
            return adInstance.show();
        }).catch((showErr: any) => {
            console.error(`AdManager: 显示${adType}广告失败:`, showErr);

            // 移除事件监听
            adInstance.offClose(onCloseHandler);
            adInstance.offError(onErrorHandler);

            // 检查是否是广告数据未加载的错误
            if (showErr && showErr.errMsg && showErr.errMsg.includes('no advertisement data available')) {
                console.log(`AdManager: ${adType}广告数据未加载，显示广告空闲提示`);
                this.showAdIdleTip(adType, callback);
            } else {
                if (callback?.onFailed) {
                    callback.onFailed(showErr);
                }
            }
        });
    }

    /**
     * 显示广告空闲提示（测试模式）
     */
    private showAdIdleTip(adType: AdType, callback?: AdCallback): void {
        console.log(`AdManager: 测试模式 - 显示${adType}广告空闲提示`);

        // 显示广告空闲提示
        AdIdleTipManager.showTip();

        // 调用失败回调，因为没有观看广告
        if (callback?.onFailed) {
            callback.onFailed('广告位空闲');
        }
        if (callback?.onClosed) {
            callback.onClosed();
        }
    }

    /**
     * 检查是否为测试模式
     */
    public isTestMode(): boolean {
        return this._isTestMode;
    }

    /**
     * 手动设置测试模式（用于调试）
     */
    public setTestMode(testMode: boolean): void {
        this._isTestMode = testMode;
        console.log(`AdManager: 手动设置测试模式为 ${testMode}`);

        if (testMode) {
            console.log("AdManager: 已启用测试模式，所有广告请求将显示广告空闲提示");
        } else {
            console.log("AdManager: 已禁用测试模式，将尝试显示真实广告");
        }
    }

    /**
     * 强制启用测试模式（用于开发环境）
     */
    public forceTestMode(): void {
        this._isTestMode = true;
        console.log("AdManager: 强制启用测试模式");
    }

    /**
     * 预加载广告（可选，提升用户体验）
     */
    public preloadAd(adType: AdType): void {
        if (this._isTestMode) {
            console.log(`AdManager: 测试模式 - 跳过${adType}广告预加载`);
            return;
        }

        const adInstance = this._adInstances.get(adType);
        if (adInstance && adInstance.load) {
            adInstance.load().then(() => {
                console.log(`AdManager: ${adType}广告预加载成功`);
            }).catch((err: any) => {
                console.warn(`AdManager: ${adType}广告预加载失败:`, err);
            });
        }
    }

    /**
     * 预加载所有广告
     */
    public preloadAllAds(): void {
        Object.values(AdType).forEach(adType => {
            this.preloadAd(adType);
        });
    }
}