import { _decorator, Component, Node, Label } from 'cc';
import { GlobalRankUI } from './UI/GlobalRankUI';
import { CustomScrollBar } from './UI/CustomScrollBar';
import { GameMode } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 全服排行榜滚动条测试脚本
 */
@ccclass('GlobalRankScrollBarTest')
export class GlobalRankScrollBarTest extends Component {

    @property(GlobalRankUI)
    globalRankUI: GlobalRankUI = null;

    @property(Label)
    debugLabel: Label = null;

    start() {
        this.testScrollBarSetup();
    }

    /**
     * 测试滚动条设置
     */
    private testScrollBarSetup(): void {
        console.log("=== 全服排行榜滚动条测试开始 ===");

        if (!this.globalRankUI) {
            console.error("GlobalRankScrollBarTest: GlobalRankUI未设置");
            return;
        }

        // 检查GlobalRankUI的组件配置
        console.log("检查GlobalRankUI组件配置:");
        console.log(`- globalRankScrollView: ${this.globalRankUI.globalRankScrollView ? '已设置' : '未设置'}`);
        console.log(`- customScrollBar: ${this.globalRankUI.customScrollBar ? '已设置' : '未设置'}`);

        if (this.globalRankUI.customScrollBar) {
            const customScrollBar = this.globalRankUI.customScrollBar;
            console.log("检查CustomScrollBar组件配置:");
            console.log(`- targetScrollView: ${customScrollBar.targetScrollView ? '已设置' : '未设置'}`);
            console.log(`- scrollBarTrack: ${customScrollBar.scrollBarTrack ? '已设置' : '未设置'}`);
            console.log(`- scrollBarHandle: ${customScrollBar.scrollBarHandle ? '已设置' : '未设置'}`);

            // 检查targetScrollView是否指向正确的ScrollView
            if (customScrollBar.targetScrollView && this.globalRankUI.globalRankScrollView) {
                const isCorrectTarget = customScrollBar.targetScrollView === this.globalRankUI.globalRankScrollView;
                console.log(`- targetScrollView指向正确: ${isCorrectTarget ? '是' : '否'}`);
                
                if (!isCorrectTarget) {
                    console.warn("CustomScrollBar的targetScrollView没有指向GlobalRankScrollView！");
                }
            }
        }

        // 延迟测试滚动功能
        this.scheduleOnce(() => {
            this.testScrollFunctionality();
        }, 1.0);

        console.log("=== 全服排行榜滚动条测试结束 ===");
    }

    /**
     * 测试滚动功能
     */
    private testScrollFunctionality(): void {
        console.log("=== 测试滚动功能 ===");

        if (!this.globalRankUI || !this.globalRankUI.globalRankScrollView) {
            console.error("GlobalRankScrollBarTest: 无法进行滚动测试");
            return;
        }

        const scrollView = this.globalRankUI.globalRankScrollView;

        // 测试滚动到不同位置
        console.log("测试滚动到顶部...");
        scrollView.scrollToTop(0.3);

        this.scheduleOnce(() => {
            console.log("测试滚动到中间...");
            scrollView.scrollToPercentVertical(0.5, 0.3);
        }, 1.0);

        this.scheduleOnce(() => {
            console.log("测试滚动到底部...");
            scrollView.scrollToBottom(0.3);
        }, 2.0);

        this.scheduleOnce(() => {
            console.log("测试滚动回到顶部...");
            scrollView.scrollToTop(0.3);
        }, 3.0);

        // 监听滚动事件
        scrollView.node.on('scrolling', this.onScrolling, this);
    }

    /**
     * 滚动事件处理
     */
    private onScrolling(): void {
        if (!this.globalRankUI || !this.globalRankUI.globalRankScrollView) {
            return;
        }

        const scrollView = this.globalRankUI.globalRankScrollView;
        const scrollOffset = scrollView.getScrollOffset();
        const maxScrollOffset = scrollView.getMaxScrollOffset();

        let progress = 0;
        if (maxScrollOffset.y > 0) {
            progress = scrollOffset.y / maxScrollOffset.y;
        }

        const progressPercent = (progress * 100).toFixed(1);
        
        if (this.debugLabel) {
            this.debugLabel.string = `滚动进度: ${progressPercent}%\n偏移: ${scrollOffset.y.toFixed(1)}\n最大偏移: ${maxScrollOffset.y.toFixed(1)}`;
        }

        console.log(`GlobalRankScrollBarTest: 滚动进度=${progressPercent}%, 偏移=${scrollOffset.y.toFixed(1)}, 最大偏移=${maxScrollOffset.y.toFixed(1)}`);
    }

    /**
     * 测试按钮：强制更新滚动条
     */
    public testUpdateScrollBar(): void {
        if (this.globalRankUI && this.globalRankUI.customScrollBar) {
            console.log("手动更新滚动条...");
            this.globalRankUI.customScrollBar.updateHandleSize();
            this.globalRankUI.customScrollBar.updateScrollBar();
        }
    }

    /**
     * 测试按钮：切换到轻松关卡
     */
    public testSwitchToEasy(): void {
        if (this.globalRankUI) {
            console.log("切换到轻松关卡...");
            this.globalRankUI.selectLevel(GameMode.NORMAL_EASY);
        }
    }

    /**
     * 测试按钮：切换到困难关卡
     */
    public testSwitchToHard(): void {
        if (this.globalRankUI) {
            console.log("切换到困难关卡...");
            this.globalRankUI.selectLevel(GameMode.NORMAL_HARD);
        }
    }

    onDestroy() {
        // 清理事件监听
        if (this.globalRankUI && this.globalRankUI.globalRankScrollView) {
            this.globalRankUI.globalRankScrollView.node.off('scrolling', this.onScrolling, this);
        }
    }
}
