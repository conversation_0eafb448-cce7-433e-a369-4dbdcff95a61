import { _decorator, Component, Node } from 'cc';
import { CloudDatabaseManager } from './Data/CloudDatabaseManager';
import { WeChatLoginManager } from './WeChatLoginManager';

const { ccclass, property } = _decorator;

/**
 * 用户识别机制测试
 * 验证设备ID和openid的用户识别逻辑
 */
@ccclass('UserIdentificationTest')
export class UserIdentificationTest extends Component {

    onLoad() {
        console.log("=== 用户识别机制测试开始 ===");
        
        // 延迟执行，确保系统初始化完成
        this.scheduleOnce(() => {
            this.runIdentificationTest();
        }, 3.0);
    }

    private async runIdentificationTest() {
        console.log("\n🧪 开始用户识别机制测试");
        
        // 1. 检查设备ID生成
        await this.testDeviceIdGeneration();
        
        // 2. 检查openid获取
        await this.testOpenIdRetrieval();
        
        // 3. 检查用户数据查询逻辑
        await this.testUserDataQuery();
        
        // 4. 分析现有数据库记录
        await this.analyzeExistingRecords();
        
        // 5. 测试用户识别逻辑
        await this.testUserIdentificationLogic();
    }

    private async testDeviceIdGeneration() {
        console.log("\n--- 测试设备ID生成 ---");
        
        // 获取当前设备ID
        const deviceId1 = localStorage.getItem('device_unique_id');
        console.log(`当前设备ID: ${deviceId1}`);
        
        // 清除设备ID，测试重新生成
        localStorage.removeItem('device_unique_id');
        
        // 触发重新生成（通过调用CloudDatabaseManager的方法）
        const cloudDB = CloudDatabaseManager.instance;
        const userData = await cloudDB.getCurrentUserData();
        
        const deviceId2 = localStorage.getItem('device_unique_id');
        console.log(`重新生成的设备ID: ${deviceId2}`);
        
        if (deviceId2 && deviceId2.startsWith('device_')) {
            console.log("✅ 设备ID生成正常");
        } else {
            console.log("❌ 设备ID生成异常");
        }
        
        // 恢复原设备ID（如果有）
        if (deviceId1) {
            localStorage.setItem('device_unique_id', deviceId1);
            console.log(`已恢复原设备ID: ${deviceId1}`);
        }
    }

    private async testOpenIdRetrieval() {
        console.log("\n--- 测试OpenID获取 ---");
        
        try {
            const cloudDB = CloudDatabaseManager.instance;
            const wxContext = await cloudDB.getWxContext();
            
            if (wxContext && wxContext.openid) {
                console.log(`获取到OpenID: ${wxContext.openid}`);
                
                // 检查是否是固定的测试openid
                if (wxContext.openid === "ozC2t7TTuemXQaaYFCQoKcYcVbUQ") {
                    console.log("⚠️ 这是固定的测试openid，所有用户共享");
                } else {
                    console.log("✅ 获取到唯一openid");
                }
            } else {
                console.log("❌ 无法获取openid");
            }
            
            // 检查WeChatLoginManager中的openid
            const loginManager = WeChatLoginManager.instance;
            const loginOpenid = loginManager.getOpenid();
            console.log(`WeChatLoginManager中的openid: ${loginOpenid}`);
            
        } catch (error) {
            console.error("获取openid失败:", error);
        }
    }

    private async testUserDataQuery() {
        console.log("\n--- 测试用户数据查询逻辑 ---");
        
        try {
            const cloudDB = CloudDatabaseManager.instance;
            
            // 测试当前用户数据查询
            const currentUser = await cloudDB.getCurrentUserData();
            if (currentUser) {
                console.log("当前用户数据:", {
                    _id: currentUser._id,
                    _openid: currentUser._openid,
                    deviceId: currentUser.deviceId,
                    nickname: currentUser.nickname,
                    inviteCode: currentUser.inviteCode
                });
                
                if (currentUser.deviceId) {
                    console.log("✅ 用户记录包含设备ID");
                } else {
                    console.log("⚠️ 用户记录缺少设备ID");
                }
            } else {
                console.log("❌ 未找到当前用户数据");
            }
            
        } catch (error) {
            console.error("查询用户数据失败:", error);
        }
    }

    private async analyzeExistingRecords() {
        console.log("\n--- 分析现有数据库记录 ---");
        
        try {
            const cloudDB = CloudDatabaseManager.instance;
            
            // 获取所有玩家数据
            const allPlayers = await cloudDB.getPlayersData(20); // 获取前20条记录
            
            if (allPlayers && allPlayers.length > 0) {
                console.log(`找到 ${allPlayers.length} 条玩家记录`);
                
                // 分析openid分布
                const openidStats = {};
                const deviceIdStats = {};
                
                allPlayers.forEach((player, index) => {
                    const openid = player._openid || 'undefined';
                    const deviceId = player.deviceId || 'undefined';
                    
                    openidStats[openid] = (openidStats[openid] || 0) + 1;
                    deviceIdStats[deviceId] = (deviceIdStats[deviceId] || 0) + 1;
                    
                    console.log(`记录${index + 1}: ID=${player._id}, openid=${openid}, deviceId=${deviceId}, 昵称=${player.nickname}, 邀请码=${player.inviteCode}`);
                });
                
                console.log("\nOpenID统计:", openidStats);
                console.log("设备ID统计:", deviceIdStats);
                
                // 分析问题
                const uniqueOpenids = Object.keys(openidStats);
                const uniqueDeviceIds = Object.keys(deviceIdStats).filter(id => id !== 'undefined');
                
                if (uniqueOpenids.length === 1) {
                    console.log("⚠️ 所有记录使用相同openid，这是问题根源！");
                } else {
                    console.log(`✅ 发现 ${uniqueOpenids.length} 个不同的openid`);
                }
                
                if (uniqueDeviceIds.length > 0) {
                    console.log(`✅ 发现 ${uniqueDeviceIds.length} 个设备ID，可以用于区分用户`);
                } else {
                    console.log("⚠️ 没有设备ID，需要为现有记录添加设备ID");
                }
                
            } else {
                console.log("没有找到玩家记录");
            }
            
        } catch (error) {
            console.error("分析现有记录失败:", error);
        }
    }

    private async testUserIdentificationLogic() {
        console.log("\n--- 测试用户识别逻辑 ---");
        
        const currentDeviceId = localStorage.getItem('device_unique_id');
        console.log(`当前设备ID: ${currentDeviceId}`);
        
        try {
            const cloudDB = CloudDatabaseManager.instance;
            
            // 模拟不同的设备ID，测试识别逻辑
            const testDeviceIds = [
                currentDeviceId,
                'device_test_123',
                'device_test_456'
            ];
            
            for (const testId of testDeviceIds) {
                console.log(`\n测试设备ID: ${testId}`);
                
                // 临时设置设备ID
                localStorage.setItem('device_unique_id', testId);
                
                // 查询用户数据
                const userData = await cloudDB.getCurrentUserData();
                if (userData) {
                    console.log(`找到用户: ${userData.nickname} (${userData.inviteCode})`);
                } else {
                    console.log("未找到匹配的用户");
                }
            }
            
            // 恢复原设备ID
            if (currentDeviceId) {
                localStorage.setItem('device_unique_id', currentDeviceId);
            }
            
        } catch (error) {
            console.error("测试用户识别逻辑失败:", error);
        }
    }

    /**
     * 手动清除设备ID，强制重新生成
     */
    public clearDeviceId() {
        localStorage.removeItem('device_unique_id');
        console.log("✅ 已清除设备ID，下次查询时会重新生成");
    }

    /**
     * 显示当前用户识别信息
     */
    public async showCurrentUserInfo() {
        console.log("\n📋 当前用户识别信息:");
        
        const deviceId = localStorage.getItem('device_unique_id');
        const loginManager = WeChatLoginManager.instance;
        const openid = loginManager.getOpenid();
        const userInfo = loginManager.getUserInfo();
        
        console.log(`设备ID: ${deviceId}`);
        console.log(`OpenID: ${openid}`);
        console.log(`用户信息:`, userInfo);
        
        // 查询云端数据
        const cloudDB = CloudDatabaseManager.instance;
        const cloudUserData = await cloudDB.getCurrentUserData();
        if (cloudUserData) {
            console.log(`云端数据: 昵称=${cloudUserData.nickname}, 邀请码=${cloudUserData.inviteCode}, 记录ID=${cloudUserData._id}`);
        } else {
            console.log("云端数据: 未找到");
        }
    }

    /**
     * 手动测试
     */
    public manualTest() {
        this.runIdentificationTest();
    }
}
