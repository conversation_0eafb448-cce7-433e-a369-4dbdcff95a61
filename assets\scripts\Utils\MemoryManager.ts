import { _decorator, Component, director, Node } from 'cc';
import { AudioMgr } from '../AudioMgr';
import { EnergyManager } from '../EnergyManager';
import { ItemManager } from '../ItemManager';
import { BackgroundManager } from '../BackgroundManager';
import { WoodpeckerSkillManager } from '../WoodpeckerSkillManager';
import { AlbatrossInvincibilityManager } from '../AlbatrossInvincibilityManager';

const { ccclass, property } = _decorator;

/**
 * 内存管理器 - 用于监控和清理内存泄漏
 */
@ccclass('MemoryManager')
export class MemoryManager extends Component {
    
    private static _instance: MemoryManager = null;
    
    // 内存监控相关
    private _memoryCheckInterval: number = 5.0; // 每5秒检查一次内存
    private _lastMemoryUsage: number = 0;
    private _memoryWarningThreshold: number = 100 * 1024 * 1024; // 100MB警告阈值
    
    // 组件引用计数
    private _componentReferences: Map<string, number> = new Map();
    
    public static getInstance(): MemoryManager {
        return this._instance;
    }
    
    onLoad() {
        MemoryManager._instance = this;
        console.log("MemoryManager: 内存管理器已初始化");
        
        // 设置为持久节点
        if (this.node.parent === director.getScene()) {
            director.addPersistRootNode(this.node);
            console.log("MemoryManager: 已设置为持久节点");
        }
        
        // 开始内存监控
        this.startMemoryMonitoring();
        
        // 监听场景切换事件
        director.on('before-scene-launch', this.onBeforeSceneChange, this);
        director.on('after-scene-launch', this.onAfterSceneChange, this);
    }
    
    onDestroy() {
        // 移除事件监听
        director.off('before-scene-launch', this.onBeforeSceneChange, this);
        director.off('after-scene-launch', this.onAfterSceneChange, this);
        
        // 停止内存监控
        this.stopMemoryMonitoring();
        
        // 清理单例引用
        if (MemoryManager._instance === this) {
            MemoryManager._instance = null;
        }
        
        console.log("MemoryManager: 内存管理器已销毁");
    }
    
    /**
     * 开始内存监控
     */
    private startMemoryMonitoring(): void {
        this.schedule(this.checkMemoryUsage, this._memoryCheckInterval);
        console.log("MemoryManager: 内存监控已启动");
    }
    
    /**
     * 停止内存监控
     */
    private stopMemoryMonitoring(): void {
        this.unschedule(this.checkMemoryUsage);
        console.log("MemoryManager: 内存监控已停止");
    }
    
    /**
     * 检查内存使用情况
     */
    private checkMemoryUsage(): void {
        // 在浏览器环境中检查内存使用情况
        if (typeof window !== 'undefined' && (window as any).performance && (window as any).performance.memory) {
            const memory = (window as any).performance.memory;
            const currentUsage = memory.usedJSHeapSize;
            const totalHeap = memory.totalJSHeapSize;
            const heapLimit = memory.jsHeapSizeLimit;
            
            // 计算内存使用率
            const usagePercent = (currentUsage / heapLimit * 100).toFixed(2);
            
            // 如果内存使用超过阈值，发出警告
            if (currentUsage > this._memoryWarningThreshold) {
                console.warn(`MemoryManager: 内存使用警告! 当前使用: ${(currentUsage / 1024 / 1024).toFixed(2)}MB (${usagePercent}%)`);
                this.performMemoryCleanup();
            }
            
            // 记录内存变化
            if (this._lastMemoryUsage > 0) {
                const memoryDiff = currentUsage - this._lastMemoryUsage;
                if (Math.abs(memoryDiff) > 10 * 1024 * 1024) { // 变化超过10MB时记录
                    console.log(`MemoryManager: 内存变化: ${(memoryDiff / 1024 / 1024).toFixed(2)}MB, 当前: ${(currentUsage / 1024 / 1024).toFixed(2)}MB`);
                }
            }
            
            this._lastMemoryUsage = currentUsage;
        }
    }
    
    /**
     * 执行内存清理
     */
    public performMemoryCleanup(): void {
        console.log("MemoryManager: 开始执行内存清理...");
        
        // 清理无效的节点引用
        this.cleanupInvalidNodes();
        
        // 清理单例管理器中的无效引用
        this.cleanupSingletonManagers();
        
        // 强制垃圾回收（如果支持）
        this.forceGarbageCollection();
        
        console.log("MemoryManager: 内存清理完成");
    }
    
    /**
     * 清理无效的节点引用
     */
    private cleanupInvalidNodes(): void {
        // 遍历场景中的所有节点，清理无效引用
        const scene = director.getScene();
        if (scene) {
            this.cleanupNodeRecursively(scene);
        }
    }
    
    /**
     * 递归清理节点
     */
    private cleanupNodeRecursively(node: Node): void {
        if (!node || !node.isValid) {
            return;
        }
        
        // 检查节点上的组件
        const components = node.getComponents(Component);
        components.forEach(comp => {
            if (!comp.isValid) {
                console.warn(`MemoryManager: 发现无效组件: ${comp.constructor.name}`);
            }
        });
        
        // 递归处理子节点
        node.children.forEach(child => {
            this.cleanupNodeRecursively(child);
        });
    }
    
    /**
     * 清理单例管理器中的无效引用
     */
    private cleanupSingletonManagers(): void {
        // 检查各个单例管理器的状态
        const managers = [
            { name: 'EnergyManager', instance: EnergyManager.getInstance() },
            { name: 'ItemManager', instance: ItemManager.getInstance() },
            { name: 'BackgroundManager', instance: BackgroundManager.getInstance() },
            { name: 'WoodpeckerSkillManager', instance: WoodpeckerSkillManager.getInstance() },
            { name: 'AlbatrossInvincibilityManager', instance: AlbatrossInvincibilityManager.getInstance() }
        ];
        
        managers.forEach(manager => {
            if (manager.instance && !manager.instance.isValid) {
                console.warn(`MemoryManager: 发现无效的单例管理器: ${manager.name}`);
            }
        });
    }
    
    /**
     * 强制垃圾回收
     */
    private forceGarbageCollection(): void {
        // 在支持的环境中强制垃圾回收
        if (typeof window !== 'undefined' && (window as any).gc) {
            (window as any).gc();
            console.log("MemoryManager: 已执行强制垃圾回收");
        }
    }
    
    /**
     * 场景切换前的清理
     */
    private onBeforeSceneChange(): void {
        console.log("MemoryManager: 场景切换前清理...");
        
        // 执行内存清理
        this.performMemoryCleanup();
        
        // 清理音频资源（如果需要）
        // 注意：不要销毁AudioMgr，因为它是持久的
    }
    
    /**
     * 场景切换后的初始化
     */
    private onAfterSceneChange(): void {
        console.log("MemoryManager: 场景切换后初始化...");
        
        // 重置内存监控
        this._lastMemoryUsage = 0;
        
        // 延迟执行一次内存检查
        this.scheduleOnce(() => {
            this.checkMemoryUsage();
        }, 1.0);
    }
    
    /**
     * 注册组件引用
     */
    public registerComponent(componentName: string): void {
        const count = this._componentReferences.get(componentName) || 0;
        this._componentReferences.set(componentName, count + 1);
    }
    
    /**
     * 注销组件引用
     */
    public unregisterComponent(componentName: string): void {
        const count = this._componentReferences.get(componentName) || 0;
        if (count > 0) {
            this._componentReferences.set(componentName, count - 1);
        }
    }
    
    /**
     * 获取内存使用报告
     */
    public getMemoryReport(): string {
        let report = "=== 内存使用报告 ===\n";
        
        // 组件引用计数
        report += "组件引用计数:\n";
        this._componentReferences.forEach((count, name) => {
            report += `  ${name}: ${count}\n`;
        });
        
        // 浏览器内存信息
        if (typeof window !== 'undefined' && (window as any).performance && (window as any).performance.memory) {
            const memory = (window as any).performance.memory;
            report += `\n浏览器内存信息:\n`;
            report += `  已使用: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB\n`;
            report += `  总堆大小: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB\n`;
            report += `  堆限制: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB\n`;
        }
        
        report += "==================";
        return report;
    }
    
    /**
     * 打印内存报告
     */
    public printMemoryReport(): void {
        console.log(this.getMemoryReport());
    }
}
