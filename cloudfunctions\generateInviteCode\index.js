// 云函数：生成唯一邀请码
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 生成唯一邀请码云函数
 * 确保生成的邀请码在数据库中是唯一的
 */
exports.main = async (event, context) => {
  console.log('生成唯一邀请码请求')
  
  try {
    let inviteCode = '';
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 10; // 最大尝试次数
    
    while (!isUnique && attempts < maxAttempts) {
      attempts++;
      
      // 生成邀请码
      inviteCode = generateRandomCode();
      console.log(`第${attempts}次尝试生成邀请码: ${inviteCode}`);
      
      // 检查是否已存在
      const existingResult = await db.collection('players')
        .where({
          inviteCode: inviteCode
        })
        .count();
      
      if (existingResult.errMsg === 'collection.count:ok') {
        if (existingResult.total === 0) {
          isUnique = true;
          console.log(`邀请码 ${inviteCode} 是唯一的`);
        } else {
          console.log(`邀请码 ${inviteCode} 已存在，重新生成`);
        }
      } else {
        console.error('检查邀请码唯一性失败:', existingResult);
        // 如果查询失败，继续尝试生成新的
      }
    }
    
    if (!isUnique) {
      console.error(`经过${maxAttempts}次尝试仍无法生成唯一邀请码`);
      return {
        success: false,
        error: '生成唯一邀请码失败，请稍后重试',
        inviteCode: null
      };
    }
    
    console.log(`成功生成唯一邀请码: ${inviteCode}`);
    
    return {
      success: true,
      inviteCode: inviteCode,
      attempts: attempts
    };
    
  } catch (error) {
    console.error('生成邀请码时出错:', error);
    return {
      success: false,
      error: error.message || '生成邀请码失败',
      inviteCode: null
    };
  }
}

/**
 * 生成随机邀请码
 * 格式：6位大小写字母和数字混合
 */
function generateRandomCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}
