// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 使用当前云环境
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    // 获取玩家数据
    const result = await db.collection('players')
      .where({
        openid: wxContext.OPENID
      })
      .get()
    
    if (result.data.length > 0) {
      return {
        success: true,
        data: result.data[0]
      }
    } else {
      return {
        success: false,
        message: '未找到玩家数据'
      }
    }
  } catch (error) {
    console.error('获取玩家数据失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
