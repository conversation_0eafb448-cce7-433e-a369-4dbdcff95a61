import { _decorator, Component, Node } from 'cc';
import { WeChatFriendsData } from './Data/WeChatFriendsData';
import { GameMode } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 全服排行榜测试脚本
 */
@ccclass('GlobalRankTest')
export class GlobalRankTest extends Component {

    start() {
        this.testGlobalRankData();
    }

    /**
     * 测试全服排行榜数据
     */
    private testGlobalRankData(): void {
        console.log("=== 全服排行榜数据测试开始 ===");

        // 测试好友排行榜数据
        const friendsRanking = WeChatFriendsData.instance.getFriendsRanking(GameMode.NORMAL_EASY, 10);
        console.log("好友排行榜前10名:");
        friendsRanking.forEach((item, index) => {
            console.log(`${index + 1}. ${item.friend.nickname} - ${item.score}分 (ID: ${item.friend.id})`);
        });

        console.log("\n=== 分隔线 ===\n");

        // 测试全服排行榜数据
        const globalRanking = WeChatFriendsData.instance.getGlobalRanking(GameMode.NORMAL_EASY, 10);
        console.log("全服排行榜前10名:");
        globalRanking.forEach((item, index) => {
            console.log(`${index + 1}. ${item.friend.nickname} - ${item.score}分 (ID: ${item.friend.id})`);
        });

        // 检查"亲一下"是否在全服排行榜中
        const qinyixiaInGlobal = globalRanking.find(item => item.friend.nickname === "亲一下");
        const qinyixiaInFriends = friendsRanking.find(item => item.friend.nickname === "亲一下");

        console.log("\n=== 测试结果 ===");
        console.log(`"亲一下"在全服排行榜中: ${qinyixiaInGlobal ? '是' : '否'}`);
        console.log(`"亲一下"在好友排行榜中: ${qinyixiaInFriends ? '是' : '否'}`);

        if (qinyixiaInGlobal) {
            console.log(`"亲一下"在全服排行榜中的排名: ${qinyixiaInGlobal.rank}, 分数: ${qinyixiaInGlobal.score}`);
        }

        // 统计数据
        const allFriends = WeChatFriendsData.instance.getAllFriends();
        const allGlobalPlayers = WeChatFriendsData.instance.getAllGlobalPlayers();
        
        console.log(`\n=== 数据统计 ===`);
        console.log(`好友总数: ${allFriends.length}`);
        console.log(`全服玩家总数: ${allGlobalPlayers.length}`);

        console.log("=== 全服排行榜数据测试结束 ===");
    }
}
