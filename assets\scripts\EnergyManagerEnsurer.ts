import { _decorator, Component, Node, instantiate, Prefab } from 'cc';
import { EnergyManager } from './EnergyManager';
const { ccclass, property } = _decorator;

/**
 * 确保EnergyManager在游戏场景中存在
 * 如果不存在，则创建一个新的实例
 */
@ccclass('EnergyManagerEnsurer')
export class EnergyManagerEnsurer extends Component {

    start() {
        this.ensureEnergyManager();
    }

    /**
     * 确保EnergyManager实例存在
     */
    private ensureEnergyManager() {
        console.log("EnergyManagerEnsurer: 检查EnergyManager实例");
        
        // 检查是否已有EnergyManager实例
        let energyManager = EnergyManager.getInstance();
        
        if (energyManager) {
            console.log("✅ EnergyManager实例已存在");
            return;
        }

        console.log("⚠️ EnergyManager实例不存在，尝试创建");

        // 检查场景中是否有EnergyManager节点
        const existingNode = this.node.scene.getChildByName("EnergyManager");
        if (existingNode) {
            energyManager = existingNode.getComponent(EnergyManager);
            if (energyManager) {
                console.log("✅ 从场景中找到EnergyManager组件");
                return;
            }
        }

        // 创建新的EnergyManager节点
        this.createEnergyManager();
    }

    /**
     * 创建新的EnergyManager节点
     */
    private createEnergyManager() {
        console.log("🔧 创建新的EnergyManager节点");
        
        try {
            // 创建新节点
            const energyManagerNode = new Node("EnergyManager");
            
            // 添加EnergyManager组件
            const energyManager = energyManagerNode.addComponent(EnergyManager);
            
            // 将节点添加到场景根节点
            this.node.scene.addChild(energyManagerNode);
            
            console.log("✅ EnergyManager节点创建成功");
            
            // 验证实例是否正确设置
            const instance = EnergyManager.getInstance();
            if (instance) {
                console.log("✅ EnergyManager单例实例设置成功");
                console.log(`当前体力: ${instance.getCurrentEnergy()}/${instance.getMaxEnergy()}`);
            } else {
                console.error("❌ EnergyManager单例实例设置失败");
            }
            
        } catch (error) {
            console.error("❌ 创建EnergyManager节点失败:", error);
        }
    }

    /**
     * 静态方法：手动确保EnergyManager存在（可从控制台调用）
     */
    public static ensureEnergyManagerExists() {
        console.log("=== 手动确保EnergyManager存在 ===");
        
        let energyManager = EnergyManager.getInstance();
        
        if (energyManager) {
            console.log("✅ EnergyManager实例已存在");
            console.log(`当前体力: ${energyManager.getCurrentEnergy()}/${energyManager.getMaxEnergy()}`);
            return;
        }

        console.log("⚠️ EnergyManager实例不存在，尝试创建");

        // 获取当前场景
        const scene = cc.director.getScene();
        if (!scene) {
            console.error("❌ 无法获取当前场景");
            return;
        }

        // 创建新节点
        const energyManagerNode = new Node("EnergyManager");
        
        // 添加EnergyManager组件
        const newEnergyManager = energyManagerNode.addComponent(EnergyManager);
        
        // 将节点添加到场景根节点
        scene.addChild(energyManagerNode);
        
        console.log("✅ EnergyManager节点创建成功");
        
        // 验证实例
        const instance = EnergyManager.getInstance();
        if (instance) {
            console.log("✅ EnergyManager单例实例设置成功");
            console.log(`当前体力: ${instance.getCurrentEnergy()}/${instance.getMaxEnergy()}`);
        } else {
            console.error("❌ EnergyManager单例实例设置失败");
        }
    }
}
