import { _decorator, Component, Node } from 'cc';
import { ItemManager, ItemType } from './ItemManager';
import { GameData } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 幸运骰子功能测试类
 * 用于测试幸运骰子的各种功能
 */
@ccclass('LuckyDiceTest')
export class LuckyDiceTest extends Component {

    start() {
        console.log("=== 幸运骰子功能测试开始 ===");
        this.runAllTests();
    }

    private runAllTests() {
        this.testLuckyDiceMultiplierGeneration();
        this.testLuckyDicePurchase();
        this.testLuckyDiceActivation();
        this.testCombinedMultipliers();
        this.testPermanentItem();
    }

    /**
     * 测试幸运骰子倍率生成
     */
    private testLuckyDiceMultiplierGeneration() {
        console.log("\n--- 测试1: 幸运骰子倍率生成测试 ---");
        
        const results = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0};
        const testCount = 10000;
        
        for (let i = 0; i < testCount; i++) {
            const multiplier = ItemManager.generateLuckyDiceMultiplier();
            results[multiplier]++;
        }
        
        console.log("倍率分布统计（测试次数：" + testCount + "）：");
        for (let i = 1; i <= 6; i++) {
            const percentage = (results[i] / testCount * 100).toFixed(2);
            console.log(`${i}倍: ${results[i]}次 (${percentage}%)`);
        }
        
        // 验证概率是否大致正确（允许一定误差）
        const expectedPercentages = [69, 16, 8, 4, 2, 1];
        let allWithinRange = true;
        
        for (let i = 1; i <= 6; i++) {
            const actualPercentage = results[i] / testCount * 100;
            const expectedPercentage = expectedPercentages[i - 1];
            const tolerance = expectedPercentage * 0.2; // 20%误差容忍度
            
            if (Math.abs(actualPercentage - expectedPercentage) > tolerance) {
                allWithinRange = false;
                console.error(`${i}倍概率偏差过大: 期望${expectedPercentage}%, 实际${actualPercentage.toFixed(2)}%`);
            }
        }
        
        if (allWithinRange) {
            console.log("✅ 幸运骰子倍率生成测试通过");
        } else {
            console.error("❌ 幸运骰子倍率生成测试失败");
        }
    }

    /**
     * 测试幸运骰子购买
     */
    private testLuckyDicePurchase() {
        console.log("\n--- 测试2: 幸运骰子购买测试 ---");
        
        // 清除之前的数据
        ItemManager.setItemCount(ItemType.LUCKY_DICE, 0);
        localStorage.setItem("TotalCoins", "3000");
        
        const initialCoins = GameData.getTotalCoins();
        const price = ItemManager.getItemPrice(ItemType.LUCKY_DICE);
        
        console.log(`初始金币: ${initialCoins}, 幸运骰子价格: ${price}`);
        
        // 第一次购买应该成功
        const firstPurchase = ItemManager.purchaseItem(ItemType.LUCKY_DICE);
        const coinsAfterFirst = GameData.getTotalCoins();
        const countAfterFirst = ItemManager.getItemCount(ItemType.LUCKY_DICE);
        
        console.log(`第一次购买结果: ${firstPurchase}, 剩余金币: ${coinsAfterFirst}, 道具数量: ${countAfterFirst}`);
        
        // 第二次购买应该失败（永久道具）
        const secondPurchase = ItemManager.purchaseItem(ItemType.LUCKY_DICE);
        const coinsAfterSecond = GameData.getTotalCoins();
        const countAfterSecond = ItemManager.getItemCount(ItemType.LUCKY_DICE);
        
        console.log(`第二次购买结果: ${secondPurchase}, 剩余金币: ${coinsAfterSecond}, 道具数量: ${countAfterSecond}`);
        
        if (firstPurchase && !secondPurchase && countAfterFirst === 1 && countAfterSecond === 1 && 
            coinsAfterFirst === initialCoins - price && coinsAfterSecond === coinsAfterFirst) {
            console.log("✅ 幸运骰子购买测试通过");
        } else {
            console.error("❌ 幸运骰子购买测试失败");
        }
    }

    /**
     * 测试幸运骰子激活
     */
    private testLuckyDiceActivation() {
        console.log("\n--- 测试3: 幸运骰子激活测试 ---");
        
        // 确保有幸运骰子
        ItemManager.setItemCount(ItemType.LUCKY_DICE, 1);
        
        // 初始状态应该是未激活
        const initialActive = ItemManager.isItemActive(ItemType.LUCKY_DICE);
        console.log(`初始激活状态: ${initialActive}`);
        
        // 激活幸运骰子
        ItemManager.activateItemEffect(ItemType.LUCKY_DICE);
        const activeAfterActivation = ItemManager.isItemActive(ItemType.LUCKY_DICE);
        const luckyDiceActive = ItemManager.isLuckyDiceActive();
        console.log(`激活后状态: ${activeAfterActivation}, 内存状态: ${luckyDiceActive}`);
        
        // 取消激活
        ItemManager.deactivateItemEffect(ItemType.LUCKY_DICE);
        const activeAfterDeactivation = ItemManager.isItemActive(ItemType.LUCKY_DICE);
        const luckyDiceActiveAfterDeactivation = ItemManager.isLuckyDiceActive();
        console.log(`取消激活后状态: ${activeAfterDeactivation}, 内存状态: ${luckyDiceActiveAfterDeactivation}`);
        
        if (!initialActive && activeAfterActivation && luckyDiceActive && 
            !activeAfterDeactivation && !luckyDiceActiveAfterDeactivation) {
            console.log("✅ 幸运骰子激活测试通过");
        } else {
            console.error("❌ 幸运骰子激活测试失败");
        }
    }

    /**
     * 测试组合倍率
     */
    private testCombinedMultipliers() {
        console.log("\n--- 测试4: 组合倍率测试 ---");
        
        // 重置状态
        GameData.resetSessionCoins();
        GameData.clearLuckyDiceMultiplier();
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        ItemManager.deactivateItemEffect(ItemType.LUCKY_DICE);
        
        // 设置道具数量
        ItemManager.setItemCount(ItemType.DOUBLE_COIN, 1);
        ItemManager.setItemCount(ItemType.LUCKY_DICE, 1);
        
        // 激活双倍金币卡
        ItemManager.activateItemEffect(ItemType.DOUBLE_COIN);
        
        // 激活幸运骰子并设置倍率
        ItemManager.activateItemEffect(ItemType.LUCKY_DICE);
        GameData.setLuckyDiceMultiplier(3); // 设置3倍
        
        // 获取倍率信息
        const multiplierInfo = GameData.getMultiplierInfo();
        console.log(`倍率信息: 双倍金币=${multiplierInfo.doubleCoin}, 幸运骰子=${multiplierInfo.luckyDice}, 总倍率=${multiplierInfo.total}`);
        
        // 模拟收集金币
        GameData.addCoin(5);
        const sessionCoins = GameData.getSessionCoins();
        const finalCoins = GameData.getFinalSessionCoins();
        
        console.log(`原始金币: ${sessionCoins}, 最终金币: ${finalCoins}`);
        
        if (multiplierInfo.doubleCoin === 2 && multiplierInfo.luckyDice === 3 && 
            multiplierInfo.total === 6 && sessionCoins === 5 && finalCoins === 30) {
            console.log("✅ 组合倍率测试通过");
        } else {
            console.error("❌ 组合倍率测试失败");
        }
    }

    /**
     * 测试永久道具特性
     */
    private testPermanentItem() {
        console.log("\n--- 测试5: 永久道具特性测试 ---");
        
        // 设置幸运骰子数量为1并激活
        ItemManager.setItemCount(ItemType.LUCKY_DICE, 1);
        ItemManager.activateItemEffect(ItemType.LUCKY_DICE);
        
        const countBefore = ItemManager.getItemCount(ItemType.LUCKY_DICE);
        const activeBefore = ItemManager.isItemActive(ItemType.LUCKY_DICE);
        
        console.log(`消耗前: 数量=${countBefore}, 激活=${activeBefore}`);
        
        // 调用消耗道具方法
        ItemManager.consumeActiveItems();
        
        const countAfter = ItemManager.getItemCount(ItemType.LUCKY_DICE);
        const activeAfter = ItemManager.isItemActive(ItemType.LUCKY_DICE);
        
        console.log(`消耗后: 数量=${countAfter}, 激活=${activeAfter}`);
        
        if (countBefore === 1 && countAfter === 1 && activeBefore && activeAfter) {
            console.log("✅ 永久道具特性测试通过");
        } else {
            console.error("❌ 永久道具特性测试失败");
        }
        
        console.log("=== 幸运骰子功能测试结束 ===");
    }
}
