// 云函数：使用邀请码
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 使用邀请码云函数
 * 验证邀请码并给邀请者发放奖励
 */
exports.main = async (event, context) => {
  const { inviteCode } = event
  const wxContext = cloud.getWXContext()
  const currentOpenId = wxContext.OPENID
  
  console.log(`使用邀请码请求 - 邀请码: ${inviteCode}, 用户: ${currentOpenId}`)
  
  try {
    // 验证参数
    if (!inviteCode) {
      return {
        success: false,
        message: '邀请码不能为空'
      }
    }

    // 查找拥有该邀请码的玩家
    const inviterResult = await db.collection('players')
      .where({
        inviteCode: inviteCode
      })
      .get()

    if (inviterResult.data.length === 0) {
      return {
        success: false,
        message: '邀请码不存在'
      }
    }

    const inviter = inviterResult.data[0]
    
    // 检查是否是自己的邀请码
    if (inviter._openid === currentOpenId) {
      return {
        success: false,
        message: '不能使用自己的邀请码'
      }
    }

    // 检查当前用户是否已经使用过邀请码
    const currentUserResult = await db.collection('players')
      .where({
        _openid: currentOpenId
      })
      .get()

    if (currentUserResult.data.length > 0) {
      const currentUser = currentUserResult.data[0]
      if (currentUser.hasUsedInviteCode) {
        return {
          success: false,
          message: '您已经使用过邀请码了'
        }
      }
    }

    // 给邀请者增加金币奖励
    const inviteReward = 2000
    await db.collection('players')
      .doc(inviter._id)
      .update({
        data: {
          coins: _.inc(inviteReward)
        }
      })

    // 标记当前用户已使用邀请码
    if (currentUserResult.data.length > 0) {
      await db.collection('players')
        .doc(currentUserResult.data[0]._id)
        .update({
          data: {
            hasUsedInviteCode: true,
            usedInviteCode: inviteCode
          }
        })
    }

    console.log(`邀请码使用成功 - 邀请者: ${inviter.nickname} 获得 ${inviteReward} 金币`)

    return {
      success: true,
      message: `邀请码使用成功！邀请者 ${inviter.nickname} 获得了 ${inviteReward} 金币奖励`,
      inviterNickname: inviter.nickname,
      reward: inviteReward
    }

  } catch (error) {
    console.error('使用邀请码时出错:', error)
    return {
      success: false,
      message: '服务器错误，请稍后重试'
    }
  }
}
