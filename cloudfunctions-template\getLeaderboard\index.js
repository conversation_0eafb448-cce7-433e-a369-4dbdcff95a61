// 云函数：获取排行榜数据
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 云函数入口函数
 * 获取指定关卡的排行榜数据
 */
exports.main = async (event, context) => {
  const { gameMode, limit = 50, offset = 0 } = event
  
  console.log('获取排行榜请求:', { gameMode, limit, offset })
  
  try {
    // 验证参数
    if (!gameMode) {
      return {
        success: false,
        error: '缺少gameMode参数',
        data: null
      }
    }
    
    // 构建聚合查询，在云端进行排序
    const result = await db.collection('players')
      .aggregate()
      .addFields({
        // 计算指定关卡的最高分
        maxScore: {
          $max: `$topScores.${gameMode}`
        }
      })
      .match({
        // 过滤掉没有该关卡分数的玩家
        maxScore: _.gt(0)
      })
      .sort({
        // 按最高分降序排列
        maxScore: -1
      })
      .skip(offset)
      .limit(Math.min(limit, 100)) // 限制最大返回数量
      .project({
        // 只返回需要的字段，减少数据传输
        _id: 1,
        nickname: 1,
        avatarUrl: 1,
        coins: 1,
        maxScore: 1,
        [`topScores.${gameMode}`]: 1
      })
      .end()
    
    if (result.errMsg === 'collection.aggregate:ok') {
      // 添加排名信息
      const leaderboardData = result.list.map((player, index) => ({
        ...player,
        rank: offset + index + 1
      }))
      
      console.log(`成功获取排行榜数据，关卡${gameMode}，共${leaderboardData.length}条`)
      
      return {
        success: true,
        error: null,
        data: {
          gameMode,
          players: leaderboardData,
          total: leaderboardData.length,
          hasMore: leaderboardData.length === limit
        }
      }
    } else {
      return {
        success: false,
        error: '查询排行榜失败',
        data: null
      }
    }
    
  } catch (error) {
    console.error('获取排行榜时出错:', error)
    
    return {
      success: false,
      error: error.message || '服务器内部错误',
      data: null
    }
  }
}
