import { CloudDatabaseManager } from './Data/CloudDatabaseManager';
import { GameData, GameMode } from './GameData';
import { InviteCodeManager } from './InviteCodeManager';
import { GameDataManager } from './Data/GameDataManager';

/**
 * 微信用户信息接口
 */
export interface WeChatUserInfo {
    openid: string;
    nickname: string;
    avatarUrl: string;
    gender: number;
    city: string;
    province: string;
    country: string;
}

/**
 * 微信登录管理器
 * 处理微信登录、用户信息获取、数据同步等功能
 */
export class WeChatLoginManager {
    private static _instance: WeChatLoginManager = null;
    private _isLoggedIn: boolean = false;
    private _userInfo: WeChatUserInfo = null;
    private _openid: string = '';

    public static get instance(): WeChatLoginManager {
        if (!this._instance) {
            this._instance = new WeChatLoginManager();
        }
        return this._instance;
    }

    private constructor() {
        // 私有构造函数，确保单例
    }

    /**
     * 初始化微信登录（智能检查授权状态）
     */
    public async initialize(): Promise<boolean> {
        console.log("WeChatLoginManager: 开始初始化微信登录");

        try {
            // 检查是否在微信环境中
            if (typeof wx === 'undefined') {
                console.log("WeChatLoginManager: 非微信环境，使用开发模式");
                return this.initializeDevelopmentMode();
            }

            // 1. 先检查当前授权状态
            console.log("WeChatLoginManager: 检查当前授权状态");
            const authStatus = await this.checkAuthorizationStatus();
            console.log("WeChatLoginManager: 授权状态检查结果:", authStatus);

            // 2. 根据授权状态决定下一步操作
            if (authStatus.needsAuthorization) {
                console.log("WeChatLoginManager: 需要用户授权，开始授权流程");
                const loginSuccess = await this.authorizeLogin();
                console.log("WeChatLoginManager: 授权登录结果:", loginSuccess);
                return loginSuccess;
            } else {
                console.log("WeChatLoginManager: 已有用户信息，直接登录");
                const silentSuccess = await this.silentLogin();
                if (silentSuccess) {
                    // 🔧 修复：优先从云端获取用户信息，确保数据完整性
                    await this.loadUserInfoFromCloudFirst();
                    await this.syncUserData();

                    // 🔧 新增：静默尝试获取好友信息授权（不强制弹窗）
                    if (!authStatus.hasFriendInfo) {
                        console.log("WeChatLoginManager: 尝试静默获取好友信息授权");
                        await this.silentRequestFriendAuthorization();
                    }

                    return true;
                } else {
                    console.warn("WeChatLoginManager: 静默登录失败，尝试重新授权");
                    const loginSuccess = await this.authorizeLogin();
                    return loginSuccess;
                }
            }

        } catch (error) {
            console.error("WeChatLoginManager: 初始化失败", error);
            console.log("WeChatLoginManager: 使用开发模式继续");
            return this.initializeDevelopmentMode();
        }
    }

    /**
     * 检查当前授权状态
     */
    private async checkAuthorizationStatus(): Promise<{
        needsAuthorization: boolean;
        hasUserInfo: boolean;
        hasFriendInfo: boolean;
        hasClipboard: boolean;
        reason: string;
    }> {
        try {
            if (typeof wx === 'undefined' || !wx.getSetting) {
                return {
                    needsAuthorization: true,
                    hasUserInfo: false,
                    hasFriendInfo: false,
                    hasClipboard: false,
                    reason: '非微信环境'
                };
            }

            return new Promise((resolve) => {
                wx.getSetting({
                    success: (res: any) => {
                        console.log("WeChatLoginManager: 当前授权设置:", res.authSetting);

                        // 🔧 修复：检查用户信息授权
                        // 在小游戏中，getUserProfile 成功后不会在 authSetting 中显示 scope.userInfo
                        // 我们需要检查本地是否已有用户信息
                        const savedUserInfo = this.loadUserInfoFromStorage();
                        const hasUserInfo = savedUserInfo && savedUserInfo.nickname && savedUserInfo.nickname !== '微信用户';

                        // 🔧 修复：检查好友信息权限（正确的scope）
                        const hasFriendInfo = res.authSetting['scope.WxFriendInteraction'] === true;

                        // 检查剪切板权限（通常不需要显式授权）
                        const hasClipboard = true; // 剪切板权限通常在使用时自动获取

                        // 🔧 修复：更智能的授权判断逻辑
                        // 只有当用户信息和好友信息都没有时才需要重新授权
                        const needsAuthorization = !hasUserInfo;

                        const result = {
                            needsAuthorization,
                            hasUserInfo: !!hasUserInfo,
                            hasFriendInfo,
                            hasClipboard,
                            reason: needsAuthorization ?
                                '需要获取用户信息' :
                                (hasFriendInfo ? '已有完整授权' : '已有用户信息，好友信息可选')
                        };

                        console.log("WeChatLoginManager: 授权状态分析:", result);
                        console.log("WeChatLoginManager: 本地用户信息:", savedUserInfo?.nickname || '无');
                        resolve(result);
                    },
                    fail: (error: any) => {
                        console.error("WeChatLoginManager: 获取授权设置失败", error);
                        resolve({
                            needsAuthorization: true,
                            hasUserInfo: false,
                            hasFriendInfo: false,
                            hasClipboard: false,
                            reason: '获取授权设置失败'
                        });
                    }
                });
            });
        } catch (error) {
            console.error("WeChatLoginManager: 检查授权状态异常", error);
            return {
                needsAuthorization: true,
                hasUserInfo: false,
                hasFriendInfo: false,
                hasClipboard: false,
                reason: '检查授权状态异常'
            };
        }
    }

    /**
     * 优先从云端加载用户信息，确保数据完整性
     */
    private async loadUserInfoFromCloudFirst(): Promise<void> {
        try {
            console.log("WeChatLoginManager: 优先从云端获取用户信息");

            // 1. 先尝试从云端获取用户信息
            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();

            const cloudUserData = await cloudDB.getCurrentUserData();
            if (cloudUserData && cloudUserData.nickname && cloudUserData.nickname !== '微信用户') {
                console.log("WeChatLoginManager: 从云端获取到完整用户信息", cloudUserData);
                this._userInfo = {
                    openid: this._openid,
                    nickname: cloudUserData.nickname,
                    avatarUrl: cloudUserData.avatarUrl || '1_penguin_home',
                    gender: 0,
                    city: '',
                    province: '',
                    country: ''
                };
                this._isLoggedIn = true;

                // 保存到本地缓存
                this.saveUserInfoToStorage();
                return;
            }

            // 2. 云端没有完整信息，尝试从本地存储获取
            const savedUserInfo = this.loadUserInfoFromStorage();
            if (savedUserInfo && savedUserInfo.nickname && savedUserInfo.nickname !== '微信用户') {
                console.log("WeChatLoginManager: 从本地存储获取到用户信息", savedUserInfo);
                this._userInfo = savedUserInfo;
                this._isLoggedIn = true;
                return;
            }

            // 3. 都没有完整信息，需要重新授权获取
            console.log("WeChatLoginManager: 没有找到完整用户信息，需要重新授权");
            const authSuccess = await this.authorizeLogin();
            if (!authSuccess) {
                // 授权失败，使用默认信息
                this.useDefaultUserInfo();
            }

        } catch (error) {
            console.error("WeChatLoginManager: 从云端加载用户信息失败", error);
            // 降级到本地存储
            await this.loadExistingUserInfo();
        }
    }

    /**
     * 加载已有的用户信息（降级方案）
     */
    private async loadExistingUserInfo(): Promise<void> {
        try {
            // 尝试从本地存储获取用户信息
            const savedUserInfo = this.loadUserInfoFromStorage();
            if (savedUserInfo) {
                console.log("WeChatLoginManager: 从本地存储加载用户信息", savedUserInfo);
                this._userInfo = savedUserInfo;
                this._isLoggedIn = true;
                return;
            }

            // 如果本地没有，使用默认用户信息
            this.useDefaultUserInfo();
        } catch (error) {
            console.error("WeChatLoginManager: 加载用户信息失败", error);
            this.useDefaultUserInfo();
        }
    }

    /**
     * 使用默认用户信息
     */
    private useDefaultUserInfo(): void {
        console.log("WeChatLoginManager: 使用默认用户信息");
        this._userInfo = {
            openid: this._openid,
            nickname: '微信用户',
            avatarUrl: '1_penguin_home',
            gender: 0,
            city: '',
            province: '',
            country: ''
        };
        this._isLoggedIn = true;
    }

    /**
     * 从本地存储加载用户信息
     */
    private loadUserInfoFromStorage(): WeChatUserInfo | null {
        try {
            if (typeof wx !== 'undefined' && wx.getStorageSync) {
                const userInfoStr = wx.getStorageSync('wechat_user_info');
                if (userInfoStr) {
                    return JSON.parse(userInfoStr);
                }
            }
            return null;
        } catch (error) {
            console.error("WeChatLoginManager: 从本地存储加载用户信息失败", error);
            return null;
        }
    }

    /**
     * 保存用户信息到本地存储
     */
    private saveUserInfoToStorage(): void {
        try {
            if (typeof wx !== 'undefined' && wx.setStorageSync && this._userInfo) {
                wx.setStorageSync('wechat_user_info', JSON.stringify(this._userInfo));
                console.log("WeChatLoginManager: 用户信息已保存到本地存储");
            }
        } catch (error) {
            console.error("WeChatLoginManager: 保存用户信息到本地存储失败", error);
        }
    }

    /**
     * 🚀 缓存openid到本地存储（避免重复调用login云函数）
     */
    private cacheOpenidToStorage(openid: string): void {
        try {
            if (typeof wx !== 'undefined' && wx.setStorageSync) {
                wx.setStorageSync('cached_openid', openid);
                console.log("WeChatLoginManager: openid已缓存到本地存储");
            } else {
                // 备用方案：使用localStorage
                localStorage.setItem('cached_openid', openid);
                console.log("WeChatLoginManager: openid已缓存到localStorage");
            }
        } catch (error) {
            console.error("WeChatLoginManager: 缓存openid失败", error);
        }
    }

    /**
     * 开发模式初始化（用于开发者工具调试）
     */
    private initializeDevelopmentMode(): boolean {
        console.log("WeChatLoginManager: 启用开发模式");

        // 模拟登录状态
        this._isLoggedIn = true;
        this._openid = 'dev_openid_' + Date.now();
        this._userInfo = {
            openid: this._openid,
            nickname: '开发者',
            avatarUrl: '1_penguin_home',
            gender: 0,
            city: '开发城市',
            province: '开发省份',
            country: '开发国家'
        };

        console.log("WeChatLoginManager: 开发模式初始化完成");
        return true;
    }

    /**
     * 静默登录（不需要用户授权）
     */
    private async silentLogin(): Promise<boolean> {
        return new Promise((resolve) => {
            if (typeof wx === 'undefined' || !wx.login) {
                resolve(false);
                return;
            }

            wx.login({
                success: async (res) => {
                    if (res.code) {
                        console.log("WeChatLoginManager: 获取到登录code:", res.code);
                        
                        // 通过云开发获取openid
                        const openid = await this.fetchOpenIdFromCloud();
                        if (openid) {
                            this._openid = openid;
                            this._isLoggedIn = true;

                            // 🚀 缓存openid到本地存储，避免重复调用login云函数
                            this.cacheOpenidToStorage(openid);

                            // 同步用户数据
                            await this.syncUserData();
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    } else {
                        console.error("WeChatLoginManager: 登录失败", res.errMsg);
                        resolve(false);
                    }
                },
                fail: (error) => {
                    console.error("WeChatLoginManager: 登录调用失败", error);
                    resolve(false);
                }
            });
        });
    }

    /**
     * 获取用户授权并登录（强制弹出微信原生授权面板）
     */
    public async authorizeLogin(): Promise<boolean> {
        try {
            // 检查是否在微信环境中
            if (typeof wx === 'undefined') {
                console.log("WeChatLoginManager: 非微信环境，使用开发模式");
                this._openid = 'dev_openid_' + Date.now();
                this._userInfo = {
                    openid: this._openid,
                    nickname: '开发用户',
                    avatarUrl: '1_penguin_home',
                    gender: 0,
                    city: '',
                    province: '',
                    country: ''
                };
                this._isLoggedIn = true;
                return true;
            }

            // 1. 先进行静默登录获取openid
            const silentSuccess = await this.silentLogin();
            if (!silentSuccess) {
                console.error("WeChatLoginManager: 静默登录失败，无法继续");
                // 在微信环境中静默登录失败是严重问题，不应该继续
                if (typeof wx !== 'undefined' && wx.showModal) {
                    wx.showModal({
                        title: '登录失败',
                        content: '网络连接失败，请检查网络后重试',
                        showCancel: false
                    });
                }
                return false;
            }

            // 2. 强制获取用户授权信息（必须弹出微信原生授权面板）
            return new Promise((resolve) => {

                // 强制要求用户授权（包括好友信息权限）
                this.requestAllAuthorizations(resolve);
            });
        } catch (error) {
            console.error("WeChatLoginManager: 授权登录过程失败", error);
            return false;
        }
    }

    /**
     * 🔧 新增：请求所有必要的授权（用户信息 + 好友信息）
     */
    private requestAllAuthorizations(resolve: (value: boolean) => void): void {
        // 先请求用户信息授权
        this.requestUserInfoAuthorization((userInfoSuccess) => {
            if (!userInfoSuccess) {
                console.error("WeChatLoginManager: 用户信息授权失败");
                resolve(false);
                return;
            }

            // 用户信息授权成功后，请求好友信息授权
            this.requestFriendInfoAuthorization((friendInfoSuccess) => {
                if (friendInfoSuccess) {
                    console.log("WeChatLoginManager: 所有授权完成");
                } else {
                    console.warn("WeChatLoginManager: 好友信息授权失败，但用户信息授权成功，继续游戏");
                }
                // 即使好友信息授权失败，也允许继续游戏（只是好友排行榜功能不可用）
                resolve(true);
            });
        });
    }

    /**
     * 请求用户信息授权
     */
    private requestUserInfoAuthorization(resolve: (value: boolean) => void): void {
        // 检查是否支持getUserProfile（新版本微信）
        if (typeof wx !== 'undefined' && (wx as any).getUserProfile) {
            console.log("WeChatLoginManager: 弹出用户授权面板");
            (wx as any).getUserProfile({
                desc: '用于完善用户资料和游戏功能',
                success: async (res: any) => {
                    console.log("WeChatLoginManager: 获取用户信息成功", res.userInfo);
                    console.log("WeChatLoginManager: 🔍 详细用户信息 - 昵称:", res.userInfo.nickName, "头像:", res.userInfo.avatarUrl);

                    // 保存用户信息
                    this._userInfo = {
                        openid: this._openid,
                        nickname: res.userInfo.nickName,
                        avatarUrl: res.userInfo.avatarUrl,
                        gender: res.userInfo.gender,
                        city: res.userInfo.city,
                        province: res.userInfo.province,
                        country: res.userInfo.country
                    };

                    console.log("WeChatLoginManager: 🔍 保存的用户信息:", this._userInfo);

                    // 保存到本地存储
                    this.saveUserInfoToStorage();

                    // 同步用户数据到云数据库
                    await this.syncUserData();
                    resolve(true);
                },
                fail: async (error: any) => {
                    console.error("WeChatLoginManager: 用户拒绝授权", error);
                    // 🔧 修复：用户拒绝授权时也要同步数据到云数据库
                    console.log("WeChatLoginManager: 用户拒绝授权，使用默认信息继续");
                    this.useDefaultUserInfo();

                    // 保存到本地存储
                    this.saveUserInfoToStorage();

                    // 同步用户数据到云数据库
                    await this.syncUserData();
                    resolve(true); // 使用默认信息也算成功
                }
            });
        } else {
            // 旧版本微信，使用默认用户信息
            console.log("WeChatLoginManager: getUserProfile不可用，使用默认用户信息");
            this._userInfo = {
                openid: this._openid,
                nickname: '微信用户',
                avatarUrl: '1_penguin_home',
                gender: 0,
                city: '',
                province: '',
                country: ''
            };
            this.syncUserData().then(() => {
                resolve(true);
            }).catch((error) => {
                console.error("WeChatLoginManager: 同步用户数据失败", error);
                resolve(true); // 即使同步失败也继续
            });
        }
    }

    /**
     * 🔧 修复：请求好友信息授权（用于好友排行榜）
     */
    private requestFriendInfoAuthorization(resolve: (value: boolean) => void): void {
        // 检查是否在微信环境中
        if (typeof wx === 'undefined') {
            console.log("WeChatLoginManager: 非微信环境，跳过好友信息授权");
            resolve(false);
            return;
        }

        console.log("WeChatLoginManager: 开始请求好友信息授权");

        // 🔧 修复：使用正确的scope获取好友信息权限
        (wx as any).authorize({
            scope: 'scope.WxFriendInteraction',
            success: () => {
                console.log("WeChatLoginManager: ✅ 好友信息授权成功");
                resolve(true);
            },
            fail: (error: any) => {
                console.warn("WeChatLoginManager: ⚠️ 好友信息授权失败", error);
                console.log("WeChatLoginManager: 错误详情:", JSON.stringify(error));
                console.log("WeChatLoginManager: 好友排行榜功能将不可用，但不影响游戏进行");

                // 🔧 增强：如果授权失败，尝试引导用户手动开启
                if (error.errMsg && error.errMsg.includes('deny')) {
                    console.log("WeChatLoginManager: 用户拒绝了好友信息授权");
                    console.log("WeChatLoginManager: 💡 用户可以稍后在设置中手动开启好友信息权限");
                }

                resolve(false);
            }
        });
    }

    /**
     * 🔧 新增：静默请求好友信息授权（不强制弹窗）
     */
    private async silentRequestFriendAuthorization(): Promise<boolean> {
        return new Promise((resolve) => {
            // 检查是否在微信环境中
            if (typeof wx === 'undefined') {
                console.log("WeChatLoginManager: 非微信环境，跳过静默好友授权");
                resolve(false);
                return;
            }

            console.log("WeChatLoginManager: 尝试静默获取好友信息授权");

            // 使用wx.authorize静默请求好友信息权限
            (wx as any).authorize({
                scope: 'scope.WxFriendInteraction',
                success: () => {
                    console.log("WeChatLoginManager: ✅ 静默好友信息授权成功");
                    resolve(true);
                },
                fail: (error: any) => {
                    console.log("WeChatLoginManager: 静默好友信息授权失败（正常情况）", error.errMsg);
                    // 静默授权失败是正常的，不需要弹窗提示
                    resolve(false);
                }
            });
        });
    }

    /**
     * 通过云开发获取openid
     */
    private async fetchOpenIdFromCloud(): Promise<string> {
        try {
            if (typeof wx === 'undefined' || !(wx as any).cloud) {
                return '';
            }

            // 使用云开发获取openid
            const result = await (wx as any).cloud.callFunction({
                name: 'login',
                data: {}
            });

            if (result.result && result.result.openid) {
                console.log("WeChatLoginManager: 获取openid成功");
                return result.result.openid;
            } else {
                console.error("WeChatLoginManager: 获取openid失败", result);
                return '';
            }
        } catch (error) {
            console.error("WeChatLoginManager: 调用登录云函数失败", error);
            return '';
        }
    }

    /**
     * 同步用户数据到云数据库
     */
    private async syncUserData(): Promise<void> {
        try {
            console.log("WeChatLoginManager: 开始同步用户数据");

            // 检查是否有有效的openid
            if (!this._openid || this._openid.startsWith('dev_openid_')) {
                console.log("WeChatLoginManager: 无有效openid，跳过云端数据同步");
                return;
            }

            const cloudDB = CloudDatabaseManager.instance;
            await cloudDB.initialize();

            // 🔧 修复：检查云端是否已有用户数据，实现真正的"只初始化一次"
            const existingData = await cloudDB.getCurrentUserData();

            console.log("WeChatLoginManager: 检查云端用户数据", existingData);

            if (existingData && existingData.inviteCode && existingData.inviteCode !== 'UNKNOWN') {
                // 云端有完整数据（有有效邀请码），说明用户已初始化过
                console.log(`WeChatLoginManager: 发现云端完整数据，用户已初始化 - 昵称:${existingData.nickname}, 邀请码:${existingData.inviteCode}`);

                // 🔧 重要修复：检查是否需要更新云端的用户信息
                // 如果本地有真实的用户信息，而云端是默认信息，则更新云端
                if (this._userInfo && this._userInfo.nickname && this._userInfo.nickname !== '微信用户' &&
                    existingData.nickname === '微信用户') {
                    console.log("WeChatLoginManager: 🔧 本地有真实用户信息，云端是默认信息，更新云端");
                    console.log("WeChatLoginManager: 本地用户信息:", this._userInfo.nickname, this._userInfo.avatarUrl);
                    console.log("WeChatLoginManager: 云端用户信息:", existingData.nickname, existingData.avatarUrl);

                    // 更新云端用户信息
                    const cloudDB = CloudDatabaseManager.instance;
                    const updateSuccess = await cloudDB.updatePlayerProfile(this._userInfo.nickname, this._userInfo.avatarUrl);
                    if (updateSuccess) {
                        console.log("WeChatLoginManager: ✅ 云端用户信息更新成功");
                    } else {
                        console.error("WeChatLoginManager: ❌ 云端用户信息更新失败");
                    }
                } else {
                    // 同步云端数据到本地
                    await this.syncCloudDataToLocal(existingData);

                    // 只有当云端有真实用户信息时才更新本地
                    if (existingData.nickname && existingData.nickname !== '微信用户') {
                        this._userInfo = {
                            openid: this._openid,
                            nickname: existingData.nickname,
                            avatarUrl: existingData.avatarUrl || '1_penguin_home',
                            gender: 0,
                            city: '',
                            province: '',
                            country: ''
                        };
                        this.saveUserInfoToStorage();
                        console.log("WeChatLoginManager: 已更新本地用户信息为云端数据");
                    }
                }

                console.log("WeChatLoginManager: 用户已初始化，跳过重新初始化");
            } else {
                // 云端没有完整数据，说明是新用户或需要重新初始化
                console.log("WeChatLoginManager: 云端无完整数据，需要初始化用户");
                await this.initializeNewUser();
            }

        } catch (error) {
            console.error("WeChatLoginManager: 同步用户数据失败", error);
        }
    }

    /**
     * 将云端数据同步到本地
     */
    private async syncCloudDataToLocal(cloudData: any): Promise<void> {
        try {
            console.log("WeChatLoginManager: 同步云端数据到本地", cloudData);

            // 临时禁用GameDataManager的自动同步，避免循环调用
            const gameDataManager = GameDataManager.instance;
            if (gameDataManager && typeof gameDataManager.setAutoSyncEnabled === 'function') {
                gameDataManager.setAutoSyncEnabled(false);
            }

            // 同步金币
            if (cloudData.coins !== undefined) {
                GameData.setCoin(cloudData.coins);
                console.log(`WeChatLoginManager: 同步金币 ${cloudData.coins}`);
            }

            // 同步各关卡分数
            if (cloudData.topScores) {
                Object.keys(cloudData.topScores).forEach(mode => {
                    const scores = cloudData.topScores[mode];
                    if (Array.isArray(scores) && scores.length > 0) {
                        const bestScore = Math.max(...scores);
                        GameData.setBestScore(parseInt(mode), bestScore);
                        console.log(`WeChatLoginManager: 同步关卡${mode}最高分 ${bestScore}`);
                    }
                });
            }

            // 恢复自动同步
            if (gameDataManager && typeof gameDataManager.setAutoSyncEnabled === 'function') {
                gameDataManager.setAutoSyncEnabled(true);
            }

            // 同步邀请码
            if (cloudData.inviteCode) {
                InviteCodeManager.setPlayerInviteCode(cloudData.inviteCode);
                console.log(`WeChatLoginManager: 同步邀请码 ${cloudData.inviteCode}`);
            }

            // 更新用户信息
            if (cloudData.nickname && !this._userInfo) {
                this._userInfo = {
                    openid: this._openid,
                    nickname: cloudData.nickname,
                    avatarUrl: cloudData.avatarUrl || '',
                    gender: 0,
                    city: '',
                    province: '',
                    country: ''
                };
            }

        } catch (error) {
            console.error("WeChatLoginManager: 同步云端数据到本地失败", error);
        }
    }

    /**
     * 上传本地数据到云端（首次登录）
     */
    private async uploadLocalDataToCloud(): Promise<void> {
        try {
            console.log("WeChatLoginManager: 首次登录，上传本地数据到云端");

            const cloudDB = CloudDatabaseManager.instance;

            // 为新用户生成唯一邀请码
            const inviteCode = await this.generateUniqueInviteCode();
            if (!inviteCode) {
                console.error("WeChatLoginManager: 生成邀请码失败");
                return;
            }

            // 保存邀请码到本地
            InviteCodeManager.setPlayerInviteCode(inviteCode);

            // 🔍 详细检查用户信息状态
            console.log("WeChatLoginManager: 🔍 当前用户信息状态:", this._userInfo);
            console.log("WeChatLoginManager: 🔍 用户昵称:", this._userInfo?.nickname);
            console.log("WeChatLoginManager: 🔍 用户头像:", this._userInfo?.avatarUrl);

            // 准备用户数据
            const userData = {
                nickname: this._userInfo?.nickname || '新玩家',
                avatarUrl: this._userInfo?.avatarUrl || '1_penguin_home',
                coins: GameData.getCoin(),
                inviteCode: inviteCode, // 使用新生成的唯一邀请码
                hasUsedInviteCode: false,
                topScores: {
                    [GameMode.NORMAL_EASY]: GameData.getTopScores(GameMode.NORMAL_EASY),
                    [GameMode.NORMAL_STANDARD]: GameData.getTopScores(GameMode.NORMAL_STANDARD),
                    [GameMode.NORMAL_HARD]: GameData.getTopScores(GameMode.NORMAL_HARD),
                    [GameMode.CHALLENGE_WIND]: GameData.getTopScores(GameMode.CHALLENGE_WIND),
                    [GameMode.CHALLENGE_FOG]: GameData.getTopScores(GameMode.CHALLENGE_FOG),
                    [GameMode.CHALLENGE_SNOW]: GameData.getTopScores(GameMode.CHALLENGE_SNOW)
                }
            };

            console.log("WeChatLoginManager: 🔍 准备上传的用户数据 - 昵称:", userData.nickname, "头像:", userData.avatarUrl);
            console.log("WeChatLoginManager: 准备上传的完整用户数据", userData);

            // 上传到云数据库
            const success = await cloudDB.createOrUpdateUserData(userData);
            if (success) {
                console.log(`WeChatLoginManager: 用户数据上传成功，邀请码: ${inviteCode}`);
            } else {
                console.error("WeChatLoginManager: 用户数据上传失败");
            }

        } catch (error) {
            console.error("WeChatLoginManager: 上传本地数据失败", error);
        }
    }

    /**
     * 初始化新用户（确保只初始化一次）
     */
    private async initializeNewUser(): Promise<void> {
        try {
            console.log("WeChatLoginManager: 开始初始化新用户");

            const cloudDB = CloudDatabaseManager.instance;

            // 🔧 再次检查云端是否已有数据（防止重复初始化）
            const existingData = await cloudDB.getCurrentUserData();
            if (existingData && existingData.inviteCode && existingData.inviteCode !== 'UNKNOWN') {
                console.log(`WeChatLoginManager: 发现用户已存在，跳过初始化 - 邀请码:${existingData.inviteCode}`);
                await this.syncCloudDataToLocal(existingData);
                return;
            }

            // 🔧 重要修复：检查用户信息状态，确保有真实的用户信息
            if (!this._userInfo || this._userInfo.nickname === '微信用户' || !this._userInfo.nickname) {
                console.error("WeChatLoginManager: ❌ 用户信息不完整，无法初始化用户");
                console.error("WeChatLoginManager: 当前用户信息:", this._userInfo);
                console.error("WeChatLoginManager: 这表明授权流程可能有问题，请检查授权逻辑");
                return;
            }

            console.log("WeChatLoginManager: ✅ 用户信息完整，继续初始化 - 昵称:", this._userInfo.nickname);

            // 调用原有的上传逻辑
            await this.uploadLocalDataToCloud();

        } catch (error) {
            console.error("WeChatLoginManager: 初始化新用户失败", error);
        }
    }

    /**
     * 生成唯一邀请码
     */
    private async generateUniqueInviteCode(): Promise<string | null> {
        try {
            console.log("WeChatLoginManager: 开始生成唯一邀请码");

            if (typeof wx !== 'undefined' && (wx as any).cloud && (wx as any).cloud.callFunction) {
                const result = await (wx as any).cloud.callFunction({
                    name: 'generateInviteCode',
                    data: {}
                });

                if (result.result && result.result.success) {
                    console.log(`WeChatLoginManager: 成功生成唯一邀请码: ${result.result.inviteCode} (尝试${result.result.attempts}次)`);
                    return result.result.inviteCode;
                } else {
                    console.error('WeChatLoginManager: 生成邀请码失败', result.result?.error);
                    return null;
                }
            } else {
                console.error('WeChatLoginManager: 微信云函数不可用');
                return null;
            }
        } catch (error) {
            console.error('WeChatLoginManager: 调用生成邀请码云函数失败', error);
            return null;
        }
    }

    /**
     * 获取登录状态
     */
    public isLoggedIn(): boolean {
        return this._isLoggedIn;
    }

    /**
     * 获取用户信息
     */
    public getUserInfo(): WeChatUserInfo | null {
        return this._userInfo;
    }

    /**
     * 获取openid
     */
    public getOpenId(): string {
        return this._openid;
    }

    /**
     * 登出
     */
    public logout(): void {
        this._isLoggedIn = false;
        this._userInfo = null;
        this._openid = '';
        console.log("WeChatLoginManager: 用户已登出");
    }
}
