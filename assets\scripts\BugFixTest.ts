import { _decorator, Component, Node, Button, Label } from 'cc';
import { GameData, BirdType, GameMode } from './GameData';
import { ItemManager, ItemType } from './ItemManager';
import { EnergyManager } from './EnergyManager';
import { LeaderboardUI } from './UI/LeaderboardUI';
const { ccclass, property } = _decorator;

/**
 * Bug修复测试类
 * 测试金币四舍五入和复活双倍金币卡补偿功能
 */
@ccclass('BugFixTest')
export class BugFixTest extends Component {

    @property(Button)
    clearRecordsBtn: Button = null;

    @property(Button)
    testEnergyBtn: Button = null;

    @property(Button)
    refreshMedalsBtn: Button = null;

    @property(Button)
    runAllTestsBtn: Button = null;

    @property(Label)
    statusLabel: Label = null;

    @property(LeaderboardUI)
    leaderboardUI: LeaderboardUI = null;

    start() {
        console.log("=== Bug修复测试开始 ===");

        // 绑定按钮事件
        this.bindButtonEvents();

        // 更新状态显示
        this.updateStatus();

        // 自动运行所有测试（可选）
        // this.runAllTests();
    }

    /**
     * 绑定按钮事件
     */
    private bindButtonEvents() {
        if (this.clearRecordsBtn) {
            this.clearRecordsBtn.node.on('click', this.onClearRecordsClick, this);
        }

        if (this.testEnergyBtn) {
            this.testEnergyBtn.node.on('click', this.onTestEnergyClick, this);
        }

        if (this.refreshMedalsBtn) {
            this.refreshMedalsBtn.node.on('click', this.onRefreshMedalsClick, this);
        }

        if (this.runAllTestsBtn) {
            this.runAllTestsBtn.node.on('click', this.onRunAllTestsClick, this);
        }
    }

    /**
     * 清空游戏记录按钮点击事件
     */
    onClearRecordsClick() {
        console.log("BugFixTest: 清空游戏记录");

        // 清空所有游戏记录
        GameData.clearAllGameRecords();

        // 刷新排行榜显示
        if (this.leaderboardUI) {
            // 强制刷新个人记录面板
            this.leaderboardUI.showPersonalRecord();
            console.log("BugFixTest: 已刷新排行榜显示");
        }

        this.updateStatus();
        console.log("BugFixTest: 游戏记录清空完成，请检查奖牌是否正常显示");
    }

    /**
     * 测试体力消耗按钮点击事件
     */
    onTestEnergyClick() {
        console.log("BugFixTest: 测试体力消耗");

        const energyManager = EnergyManager.getInstance();
        if (!energyManager) {
            console.error("BugFixTest: 未找到EnergyManager实例");
            this.updateStatus("错误：未找到EnergyManager实例");
            return;
        }

        const beforeEnergy = energyManager.getCurrentEnergy();
        console.log(`BugFixTest: 消耗前体力: ${beforeEnergy}`);

        // 尝试消耗体力
        const success = energyManager.consumeEnergy();
        const afterEnergy = energyManager.getCurrentEnergy();

        console.log(`BugFixTest: 消耗后体力: ${afterEnergy}, 消耗成功: ${success}`);

        if (success) {
            this.updateStatus(`体力消耗成功: ${beforeEnergy} -> ${afterEnergy}`);
        } else {
            this.updateStatus(`体力消耗失败: 当前体力不足 (${beforeEnergy})`);
        }
    }

    /**
     * 刷新奖牌显示按钮点击事件
     */
    onRefreshMedalsClick() {
        console.log("BugFixTest: 刷新奖牌显示");

        if (this.leaderboardUI) {
            // 强制刷新个人记录面板，这会触发奖牌更新
            this.leaderboardUI.showPersonalRecord();
            this.updateStatus("奖牌显示已刷新");
            console.log("BugFixTest: 奖牌显示刷新完成");
        } else {
            this.updateStatus("错误：未找到LeaderboardUI组件");
            console.error("BugFixTest: 未找到LeaderboardUI组件");
        }
    }

    /**
     * 运行所有测试按钮点击事件
     */
    onRunAllTestsClick() {
        console.log("BugFixTest: 运行所有测试");
        this.runAllTests();
        this.updateStatus("所有测试已完成，请查看控制台");
    }

    /**
     * 更新状态显示
     */
    updateStatus(message?: string) {
        if (!this.statusLabel) return;

        if (message) {
            this.statusLabel.string = message;
            return;
        }

        // 获取当前状态信息
        const energyManager = EnergyManager.getInstance();
        const currentEnergy = energyManager ? energyManager.getCurrentEnergy() : "未知";

        // 获取游戏记录状态
        const records = GameData.getAllGameRecords();
        let hasRecords = false;
        for (const modeName in records) {
            if (records.hasOwnProperty(modeName)) {
                const record = records[modeName];
                if (record.bestScore > 0) {
                    hasRecords = true;
                    break;
                }
            }
        }

        this.statusLabel.string = `体力: ${currentEnergy}/100 | 记录: ${hasRecords ? '有' : '无'}`;
    }

    private runAllTests() {
        this.testCoinRounding();
        this.testReviveDoubleCoinCompensation();
        this.testPipeSpawnerTimerFix();
        this.testMedalDisplayFix();
        this.testEnergyConsumptionFix();
    }

    /**
     * 测试金币四舍五入功能
     */
    private testCoinRounding() {
        console.log("\n--- 测试1: 金币四舍五入测试 ---");
        
        // 重置状态
        GameData.resetSessionCoins();
        GameData.clearLuckyDiceMultiplier();
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        ItemManager.deactivateItemEffect(ItemType.LUCKY_DICE);
        
        // 设置测试环境：金色小鸟 + 标准关卡 + 幸运骰子(1倍)
        GameData.setCurrentGameMode(GameMode.NORMAL_STANDARD);
        GameData.setSelectedBirdType(BirdType.GOLD);
        ItemManager.setItemCount(ItemType.LUCKY_DICE, 1);
        ItemManager.activateItemEffect(ItemType.LUCKY_DICE);
        GameData.setLuckyDiceMultiplier(1);
        
        // 测试案例1: 收集3个金币
        console.log("\n测试案例1: 收集3个金币");
        GameData.resetSessionCoins();
        GameData.addCoin(3);
        
        const sessionCoins1 = GameData.getSessionCoins();
        const finalCoins1 = GameData.getFinalSessionCoins();
        const multiplier1 = GameData.getCurrentCoinMultiplier();
        
        console.log(`原始金币: ${sessionCoins1}`);
        console.log(`倍率: ${multiplier1} (1.2 × 1 × 1.5 × 1)`);
        console.log(`计算结果: ${sessionCoins1 * multiplier1}`);
        console.log(`最终金币: ${finalCoins1}`);
        
        // 3 * 1.8 = 5.4，四舍五入应该是5
        if (sessionCoins1 === 3 && finalCoins1 === 5 && Math.abs(multiplier1 - 1.8) < 0.001) {
            console.log("✅ 案例1通过 (5.4 → 5)");
        } else {
            console.error(`❌ 案例1失败: 期望5, 实际${finalCoins1}`);
        }
        
        // 测试案例2: 收集7个金币
        console.log("\n测试案例2: 收集7个金币");
        GameData.resetSessionCoins();
        GameData.addCoin(7);
        
        const sessionCoins2 = GameData.getSessionCoins();
        const finalCoins2 = GameData.getFinalSessionCoins();
        
        console.log(`原始金币: ${sessionCoins2}`);
        console.log(`计算结果: ${sessionCoins2 * multiplier1}`);
        console.log(`最终金币: ${finalCoins2}`);
        
        // 7 * 1.8 = 12.6，四舍五入应该是13
        if (sessionCoins2 === 7 && finalCoins2 === 13) {
            console.log("✅ 案例2通过 (12.6 → 13)");
        } else {
            console.error(`❌ 案例2失败: 期望13, 实际${finalCoins2}`);
        }
        
        // 测试案例3: 收集5个金币
        console.log("\n测试案例3: 收集5个金币");
        GameData.resetSessionCoins();
        GameData.addCoin(5);
        
        const sessionCoins3 = GameData.getSessionCoins();
        const finalCoins3 = GameData.getFinalSessionCoins();
        
        console.log(`原始金币: ${sessionCoins3}`);
        console.log(`计算结果: ${sessionCoins3 * multiplier1}`);
        console.log(`最终金币: ${finalCoins3}`);
        
        // 5 * 1.8 = 9.0，应该是9
        if (sessionCoins3 === 5 && finalCoins3 === 9) {
            console.log("✅ 案例3通过 (9.0 → 9)");
        } else {
            console.error(`❌ 案例3失败: 期望9, 实际${finalCoins3}`);
        }
        
        console.log("✅ 金币四舍五入测试完成");
    }

    /**
     * 测试复活双倍金币卡补偿功能
     */
    private testReviveDoubleCoinCompensation() {
        console.log("\n--- 测试2: 复活双倍金币卡补偿测试 ---");
        
        // 重置状态
        GameData.resetSessionCoins();
        GameData.resetSessionReviveCount();
        GameData.clearReviveState();
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        
        // 设置初始道具数量
        ItemManager.setItemCount(ItemType.DOUBLE_COIN, 2);
        ItemManager.setItemCount(ItemType.REVIVE_COIN, 1);
        
        console.log("\n=== 模拟游戏流程 ===");
        
        // 第一步：激活双倍金币卡
        console.log("\n1. 激活双倍金币卡");
        ItemManager.activateItemEffect(ItemType.DOUBLE_COIN);
        console.log(`双倍金币卡数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`双倍金币卡激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);
        
        // 第二步：模拟第一次游戏结束（消耗双倍金币卡）
        console.log("\n2. 第一次游戏结束");
        ItemManager.consumeActiveItems();
        console.log(`双倍金币卡数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`双倍金币卡激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);
        
        // 验证第一次消耗
        const countAfterFirstDeath = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const activeAfterFirstDeath = ItemManager.isItemActive(ItemType.DOUBLE_COIN);
        
        if (countAfterFirstDeath === 1 && activeAfterFirstDeath === true) {
            console.log("✅ 第一次死亡正确消耗双倍金币卡");
        } else {
            console.error("❌ 第一次死亡消耗异常");
        }
        
        // 第三步：模拟复活（应该补偿双倍金币卡）
        console.log("\n3. 使用复活币复活");
        
        // 模拟复活逻辑中的补偿部分
        if (ItemManager.isItemActive(ItemType.DOUBLE_COIN)) {
            const currentDoubleCoinCount = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
            ItemManager.setItemCount(ItemType.DOUBLE_COIN, currentDoubleCoinCount + 1);
            console.log(`复活补偿：双倍金币卡数量 +1，当前数量: ${currentDoubleCoinCount + 1}`);
        }
        
        console.log(`复活后双倍金币卡数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`复活后双倍金币卡激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);
        
        // 验证复活补偿
        const countAfterRevive = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const activeAfterRevive = ItemManager.isItemActive(ItemType.DOUBLE_COIN);
        
        if (countAfterRevive === 2 && activeAfterRevive === true) {
            console.log("✅ 复活正确补偿双倍金币卡");
        } else {
            console.error("❌ 复活补偿异常");
        }
        
        // 第四步：模拟第二次游戏结束（再次消耗双倍金币卡）
        console.log("\n4. 第二次游戏结束");
        ItemManager.consumeActiveItems();
        console.log(`双倍金币卡数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`双倍金币卡激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);
        
        // 验证第二次消耗
        const countAfterSecondDeath = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const activeAfterSecondDeath = ItemManager.isItemActive(ItemType.DOUBLE_COIN);
        
        if (countAfterSecondDeath === 1 && activeAfterSecondDeath === true) {
            console.log("✅ 第二次死亡正确消耗双倍金币卡");
        } else {
            console.error("❌ 第二次死亡消耗异常");
        }
        
        // 总结测试结果
        console.log("\n=== 测试总结 ===");
        console.log("预期流程：");
        console.log("- 初始：2张双倍金币卡");
        console.log("- 第一次死亡：消耗1张，剩余1张");
        console.log("- 复活：补偿1张，恢复到2张");
        console.log("- 第二次死亡：消耗1张，剩余1张");
        console.log("- 结果：一局游戏只消耗了1张双倍金币卡");
        
        if (countAfterSecondDeath === 1) {
            console.log("✅ 复活双倍金币卡补偿测试通过");
        } else {
            console.error("❌ 复活双倍金币卡补偿测试失败");
        }
        
        console.log("=== Bug修复测试结束 ===");
    }

    /**
     * 测试管道移动控制修复功能
     * 验证困难关卡和大风吹关卡下第一列管道不会在准备阶段移动
     */
    private testPipeSpawnerTimerFix() {
        console.log("\n--- 测试3: 管道移动控制修复测试 ---");

        // 模拟PipeSpawner的行为
        const mockPipeSpawner = {
            timer: 2,
            _isSpawning: false,
            _needsTimerAdjustment: false,
            _backgroundSet: 1,
            _currentSpawnRate: 3,

            // 模拟pause方法
            pause: function() {
                this._isSpawning = false;
                this.timer = 2; // 重置timer到初始值
                console.log(`PipeSpawner.pause(): timer重置为${this.timer}, _isSpawning=${this._isSpawning}`);
            },

            // 模拟adjustTimerIfNeeded方法
            adjustTimerIfNeeded: function() {
                if (this._needsTimerAdjustment && this._isSpawning) {
                    if (this._backgroundSet === 2 || this._backgroundSet === 3) {
                        const hardDifficultyInterval = 3 * 0.667; // 2.001秒
                        this.timer = hardDifficultyInterval - 0.1; // 提前0.1秒
                        console.log(`调整timer: ${this.timer.toFixed(2)}`);
                    }
                    this._needsTimerAdjustment = false;
                }
            },

            // 模拟start方法
            start: function() {
                this._isSpawning = true;
                this.adjustTimerIfNeeded();
                console.log(`PipeSpawner.start(): _isSpawning=${this._isSpawning}, timer=${this.timer.toFixed(2)}`);
            }
        };

        console.log("\n测试案例1: 正常情况下的准备阶段");
        mockPipeSpawner.timer = 2;
        mockPipeSpawner._isSpawning = false;
        mockPipeSpawner._needsTimerAdjustment = false;
        mockPipeSpawner._backgroundSet = 1;

        mockPipeSpawner.pause();

        if (mockPipeSpawner.timer === 2 && mockPipeSpawner._isSpawning === false) {
            console.log("✅ 案例1通过: 准备阶段timer正确重置");
        } else {
            console.error("❌ 案例1失败: 准备阶段timer未正确重置");
        }

        console.log("\n测试案例2: 困难难度第二套背景的timer调整");
        mockPipeSpawner.timer = 2;
        mockPipeSpawner._isSpawning = false;
        mockPipeSpawner._needsTimerAdjustment = true;
        mockPipeSpawner._backgroundSet = 2;

        // 先暂停（模拟准备阶段）
        mockPipeSpawner.pause();
        const timerAfterPause = mockPipeSpawner.timer;

        // 再启动（模拟游戏开始）
        mockPipeSpawner.start();
        const timerAfterStart = mockPipeSpawner.timer;

        if (timerAfterPause === 2 && timerAfterStart < 2) {
            console.log("✅ 案例2通过: timer只在游戏开始时调整，准备阶段保持初始值");
        } else {
            console.error(`❌ 案例2失败: 暂停后timer=${timerAfterPause}, 启动后timer=${timerAfterStart.toFixed(2)}`);
        }

        console.log("\n测试案例3: 第一套背景不需要调整timer");
        mockPipeSpawner.timer = 2;
        mockPipeSpawner._isSpawning = false;
        mockPipeSpawner._needsTimerAdjustment = true;
        mockPipeSpawner._backgroundSet = 1;

        mockPipeSpawner.start();

        if (mockPipeSpawner.timer === 2) {
            console.log("✅ 案例3通过: 第一套背景timer不被调整");
        } else {
            console.error(`❌ 案例3失败: 第一套背景timer被错误调整为${mockPipeSpawner.timer.toFixed(2)}`);
        }

        console.log("\n测试案例4: 验证isPaused方法包含Ready状态");

        // 模拟GameManager的isPaused方法
        const mockGameManager = {
            curGS: 0, // Ready = 0, Gaming = 1, Paused = 2, GameOver = 3

            isPaused: function() {
                return this.curGS === 0 || this.curGS === 2 || this.curGS === 3; // Ready || Paused || GameOver
            }
        };

        // 测试Ready状态
        mockGameManager.curGS = 0; // Ready
        if (mockGameManager.isPaused()) {
            console.log("✅ Ready状态正确返回暂停");
        } else {
            console.error("❌ Ready状态未返回暂停");
        }

        // 测试Gaming状态
        mockGameManager.curGS = 1; // Gaming
        if (!mockGameManager.isPaused()) {
            console.log("✅ Gaming状态正确返回非暂停");
        } else {
            console.error("❌ Gaming状态错误返回暂停");
        }

        // 测试Paused状态
        mockGameManager.curGS = 2; // Paused
        if (mockGameManager.isPaused()) {
            console.log("✅ Paused状态正确返回暂停");
        } else {
            console.error("❌ Paused状态未返回暂停");
        }

        console.log("✅ 管道移动控制修复测试完成");
        console.log("\n修复说明:");
        console.log("- isPaused()方法现在包含Ready状态，确保准备阶段管道不移动");
        console.log("- 每次进入准备阶段都清理场景中的管道，防止遗留管道移动");
        console.log("- BackgroundManager禁用已存在管道的移动组件");
        console.log("- 这样确保了管道只在游戏开始后才会移动");
    }

    /**
     * 测试奖牌显示修复功能
     */
    private testMedalDisplayFix() {
        console.log("\n--- 测试4: 奖牌显示修复测试 ---");

        // 清空所有游戏记录
        GameData.clearAllGameRecords();
        console.log("已清空所有游戏记录");

        // 模拟LeaderboardUI的updateMedalsDisplay方法
        const mockLeaderboardUI = {
            updateMedalsDisplay: function() {
                console.log("模拟奖牌显示更新...");

                // 获取所有模式的最高分
                const allModes = [
                    GameMode.NORMAL_EASY,
                    GameMode.NORMAL_STANDARD,
                    GameMode.NORMAL_HARD,
                    GameMode.CHALLENGE_WIND,
                    GameMode.CHALLENGE_FOG,
                    GameMode.CHALLENGE_SNOW
                ];

                // 收集所有最高分
                const allBestScores = allModes.map(mode => GameData.getBestScore(mode));
                console.log(`所有模式最高分: [${allBestScores.join(', ')}]`);

                // 排序获取前三名
                const sortedScores = [...allBestScores].sort((a, b) => b - a);
                console.log(`排序后前三分: [${sortedScores.slice(0, 3).join(', ')}]`);

                // 修复后的逻辑：金银铜牌始终显示
                const goldVisible = true;    // 金牌始终显示
                const silverVisible = true;  // 银牌始终显示
                const bronzeVisible = true;  // 铜牌始终显示

                console.log(`奖牌显示状态: 金牌=${goldVisible}, 银牌=${silverVisible}, 铜牌=${bronzeVisible}`);

                return { goldVisible, silverVisible, bronzeVisible };
            }
        };

        // 测试清空记录后的奖牌显示
        const result = mockLeaderboardUI.updateMedalsDisplay();

        if (result.goldVisible && result.silverVisible && result.bronzeVisible) {
            console.log("✅ 奖牌显示修复测试通过：金银铜牌均正常显示");
        } else {
            console.error("❌ 奖牌显示修复测试失败：部分奖牌未显示");
        }

        console.log("\n修复说明:");
        console.log("- 修改updateMedalsDisplay方法，金银铜牌始终显示");
        console.log("- 不再根据分数是否大于0来决定奖牌显示");
        console.log("- 确保即使在清空记录后，奖牌图标也能正常显示");
    }

    /**
     * 测试体力消耗修复功能
     */
    private testEnergyConsumptionFix() {
        console.log("\n--- 测试5: 体力消耗修复测试 ---");

        const energyManager = EnergyManager.getInstance();
        if (!energyManager) {
            console.error("❌ 未找到EnergyManager实例，跳过测试");
            return;
        }

        // 模拟GameManager的体力消耗逻辑
        const mockGameManager = {
            _energyConsumed: false,

            // 模拟transitionToReadyState方法中的重置逻辑
            resetEnergyConsumedFlag: function() {
                this._energyConsumed = false;
                console.log("GameManager: 体力消耗标志已重置，下一局游戏将消耗体力");
            },

            // 模拟transitionToGamingState方法中的体力消耗逻辑
            tryConsumeEnergy: function() {
                console.log(`体力消耗检查 - 已消耗标志: ${this._energyConsumed}`);

                if (!this._energyConsumed) {
                    const beforeEnergy = energyManager.getCurrentEnergy();
                    const success = energyManager.consumeEnergy();
                    const afterEnergy = energyManager.getCurrentEnergy();

                    if (success) {
                        this._energyConsumed = true;
                        console.log(`成功消耗体力: ${beforeEnergy} -> ${afterEnergy}`);
                        return true;
                    } else {
                        console.log(`体力不足，无法开始游戏: ${beforeEnergy}`);
                        return false;
                    }
                } else {
                    console.log("本局游戏已消耗过体力，不再重复消耗");
                    return true;
                }
            }
        };

        console.log("\n测试案例1: 第一次游戏消耗体力");
        const initialEnergy = energyManager.getCurrentEnergy();
        console.log(`初始体力: ${initialEnergy}`);

        // 重置标志（模拟进入Ready状态）
        mockGameManager.resetEnergyConsumedFlag();

        // 尝试消耗体力（模拟进入Gaming状态）
        const firstConsumeSuccess = mockGameManager.tryConsumeEnergy();
        const energyAfterFirst = energyManager.getCurrentEnergy();

        if (firstConsumeSuccess && energyAfterFirst === initialEnergy - 4) {
            console.log("✅ 第一次游戏正确消耗体力");
        } else {
            console.error(`❌ 第一次游戏体力消耗异常: 期望${initialEnergy - 4}, 实际${energyAfterFirst}`);
        }

        console.log("\n测试案例2: 同一局游戏重复尝试消耗体力");
        const secondConsumeSuccess = mockGameManager.tryConsumeEnergy();
        const energyAfterSecond = energyManager.getCurrentEnergy();

        if (secondConsumeSuccess && energyAfterSecond === energyAfterFirst) {
            console.log("✅ 同一局游戏不会重复消耗体力");
        } else {
            console.error(`❌ 同一局游戏重复消耗体力: 期望${energyAfterFirst}, 实际${energyAfterSecond}`);
        }

        console.log("\n测试案例3: 新一局游戏重新消耗体力");
        // 重置标志（模拟新一局游戏开始）
        mockGameManager.resetEnergyConsumedFlag();

        const thirdConsumeSuccess = mockGameManager.tryConsumeEnergy();
        const energyAfterThird = energyManager.getCurrentEnergy();

        if (thirdConsumeSuccess && energyAfterThird === energyAfterSecond - 4) {
            console.log("✅ 新一局游戏正确消耗体力");
        } else {
            console.error(`❌ 新一局游戏体力消耗异常: 期望${energyAfterSecond - 4}, 实际${energyAfterThird}`);
        }

        console.log("\n修复说明:");
        console.log("- 在transitionToReadyState方法中重置_energyConsumed标志");
        console.log("- 在transitionToGamingState方法中添加详细的调试日志");
        console.log("- 确保每局游戏只消耗一次体力，新局游戏重新消耗");
        console.log("- 复活状态下不消耗体力");
    }

    /**
     * 静态方法，可以从控制台手动调用
     */
    public static runManualTest() {
        console.log("=== 手动运行Bug修复测试 ===");
        const testInstance = new BugFixTest();
        testInstance.runAllTests();
    }

    onDestroy() {
        // 清理事件监听
        if (this.clearRecordsBtn) {
            this.clearRecordsBtn.node.off('click', this.onClearRecordsClick, this);
        }
        if (this.testEnergyBtn) {
            this.testEnergyBtn.node.off('click', this.onTestEnergyClick, this);
        }
        if (this.refreshMedalsBtn) {
            this.refreshMedalsBtn.node.off('click', this.onRefreshMedalsClick, this);
        }
        if (this.runAllTestsBtn) {
            this.runAllTestsBtn.node.off('click', this.onRunAllTestsClick, this);
        }

        this.unscheduleAllCallbacks();
    }
}
