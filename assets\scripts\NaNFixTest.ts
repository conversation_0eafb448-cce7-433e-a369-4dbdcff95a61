import { _decorator, Component, Node } from 'cc';
import { GameData, GameMode } from './GameData';
import { GameDifficulty } from './GameDifficulty';
import { ChallengeMode } from './ChallengeMode';

const { ccclass, property } = _decorator;

/**
 * NaN问题修复测试
 */
@ccclass('NaNFixTest')
export class NaNFixTest extends Component {

    onLoad() {
        console.log("=== NaN问题修复测试开始 ===");
        
        // 延迟执行
        this.scheduleOnce(() => {
            this.runNaNTest();
        }, 1.0);
    }

    private runNaNTest() {
        console.log("\n🧪 测试NaN问题修复");
        
        // 1. 测试正常情况
        this.testNormalCase();
        
        // 2. 模拟NaN情况
        this.testNaNCase();
        
        // 3. 测试轻松模式
        this.testEasyMode();
    }

    private testNormalCase() {
        console.log("\n--- 测试正常情况 ---");
        
        // 设置正常值
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_EASY);
        ChallengeMode.clearMode();
        
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        const gameMode = GameData.determineGameMode(difficulty, challengeMode);
        
        console.log(`正常情况: 难度=${difficulty}, 挑战=${challengeMode}, 模式=${gameMode}(${GameData.getGameModeName(gameMode)})`);
        
        if (gameMode === GameMode.NORMAL_EASY) {
            console.log("✅ 正常情况测试通过");
        } else {
            console.log("❌ 正常情况测试失败");
        }
    }

    private testNaNCase() {
        console.log("\n--- 测试NaN情况 ---");
        
        // 模拟localStorage中有无效值
        localStorage.setItem("GameDifficulty", "invalid");
        localStorage.setItem("ChallengeMode", "invalid");
        
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        const gameMode = GameData.determineGameMode(difficulty, challengeMode);
        
        console.log(`NaN情况: 难度=${difficulty}, 挑战=${challengeMode}, 模式=${gameMode}(${GameData.getGameModeName(gameMode)})`);
        
        if (!isNaN(difficulty) && !isNaN(challengeMode)) {
            console.log("✅ NaN处理测试通过");
        } else {
            console.log("❌ NaN处理测试失败");
        }
    }

    private testEasyMode() {
        console.log("\n--- 测试轻松模式 ---");
        
        // 清除可能的无效值
        localStorage.removeItem("GameDifficulty");
        localStorage.removeItem("ChallengeMode");
        
        // 重新设置轻松模式
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_EASY);
        ChallengeMode.clearMode();
        
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        const gameMode = GameData.determineGameMode(difficulty, challengeMode);
        
        console.log(`轻松模式: 难度=${difficulty}, 挑战=${challengeMode}, 模式=${gameMode}(${GameData.getGameModeName(gameMode)})`);
        
        if (gameMode === GameMode.NORMAL_EASY) {
            console.log("✅ 轻松模式测试通过");
        } else {
            console.log("❌ 轻松模式测试失败");
        }
        
        // 模拟分数保存
        GameData.setCurrentGameMode(gameMode);
        GameData.resetScore();
        GameData.addScore(5);
        GameData.saveScore(gameMode);
        
        // 验证保存结果
        const easyBest = GameData.getBestScore(GameMode.NORMAL_EASY);
        const standardBest = GameData.getBestScore(GameMode.NORMAL_STANDARD);
        
        console.log(`保存结果: 轻松模式=${easyBest}, 标准模式=${standardBest}`);
        
        if (easyBest >= 5 && standardBest !== 5) {
            console.log("✅ 分数保存测试通过");
        } else {
            console.log("❌ 分数保存测试失败");
        }
    }

    /**
     * 检查localStorage状态
     */
    public checkLocalStorage() {
        console.log("\n📁 localStorage状态:");
        console.log(`GameDifficulty: "${localStorage.getItem("GameDifficulty")}"`);
        console.log(`ChallengeMode: "${localStorage.getItem("ChallengeMode")}"`);
        
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        console.log(`解析结果: 难度=${difficulty}, 挑战=${challengeMode}`);
        console.log(`类型检查: 难度isNaN=${isNaN(difficulty)}, 挑战isNaN=${isNaN(challengeMode)}`);
    }

    /**
     * 清理localStorage
     */
    public cleanLocalStorage() {
        localStorage.removeItem("GameDifficulty");
        localStorage.removeItem("ChallengeMode");
        console.log("✅ 已清理localStorage");
    }

    /**
     * 手动测试
     */
    public manualTest() {
        this.runNaNTest();
    }
}
