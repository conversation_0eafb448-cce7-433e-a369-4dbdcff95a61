{"__version__": "1.3.7", "bundleConfig": {"custom": {"default": {"displayName": "i18n:builder.asset_bundle.defaultConfig", "configs": {"native": {"preferredOptions": {"isRemote": false, "compressionType": "merge_dep"}}, "web": {"preferredOptions": {"isRemote": false, "compressionType": "merge_dep"}, "fallbackOptions": {"compressionType": "merge_dep"}}, "miniGame": {"fallbackOptions": {"isRemote": true, "compressionType": "merge_dep"}, "configMode": "fallback"}}}}}}