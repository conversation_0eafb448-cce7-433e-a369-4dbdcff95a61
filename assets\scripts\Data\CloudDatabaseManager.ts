import { _decorator } from 'cc';
import { GameMode } from '../GameData';
import { WeChatLoginManager } from '../WeChatLoginManager';
const { ccclass } = _decorator;

/**
 * 云数据库中的玩家数据接口
 */
export interface CloudPlayerData {
    _id?: string;                    // 云数据库自动生成的ID
    _openid?: string;               // 微信用户openid（自动生成）
    deviceId?: string;              // 设备唯一标识（用于区分相同openid的用户）
    nickname: string;               // 玩家昵称
    avatarUrl: string;              // 头像URL
    coins: number;                  // 金币数量
    inviteCode: string;             // 邀请码
    topScores: {                    // 每个关卡的最高三次分数
        [GameMode.NORMAL_EASY]: number[];
        [GameMode.NORMAL_STANDARD]: number[];
        [GameMode.NORMAL_HARD]: number[];
        [GameMode.CHALLENGE_WIND]: number[];
        [GameMode.CHALLENGE_FOG]: number[];
        [GameMode.CHALLENGE_SNOW]: number[];
    };
    createdAt?: Date;               // 创建时间（云数据库自动生成）
    updatedAt?: Date;               // 更新时间（云数据库自动生成）
}

/**
 * 微信云开发数据库管理器
 * 负责与云数据库进行交互，管理玩家数据
 */
@ccclass('CloudDatabaseManager')
export class CloudDatabaseManager {
    private static _instance: CloudDatabaseManager = null;
    private _db: any = null;
    private _isInitialized: boolean = false;
    private readonly COLLECTION_NAME = 'players'; // 玩家数据集合名称

    public static get instance(): CloudDatabaseManager {
        if (!this._instance) {
            this._instance = new CloudDatabaseManager();
        }
        return this._instance;
    }

    /**
     * 初始化云数据库
     */
    public async initialize(): Promise<boolean> {
        try {
            // 检查微信云开发是否可用
            if (typeof wx === 'undefined' || !wx.cloud) {
                console.error('CloudDatabaseManager: 微信云开发不可用');
                return false;
            }

            // 初始化云开发（确保在任何数据库操作之前调用）
            try {
                await wx.cloud.init({
                    env: 'cloud1-9gx9hhw6416d5994' // 您的环境ID
                });
                console.log('CloudDatabaseManager: wx.cloud.init 调用成功');
            } catch (initError) {
                console.error('CloudDatabaseManager: wx.cloud.init 失败', initError);
                return false;
            }

            // 获取数据库引用
            this._db = wx.cloud.database();
            this._isInitialized = true;

            console.log('CloudDatabaseManager: 云数据库初始化成功');

            // 自动检查并上传初始数据（已注释，避免重复上传）
            // await this.autoUploadInitialDataIfNeeded();

            return true;
        } catch (error) {
            console.error('CloudDatabaseManager: 初始化失败', error);
            return false;
        }
    }

    /**
     * 自动检查并上传初始数据（如果数据库为空）
     */
    private async autoUploadInitialDataIfNeeded(): Promise<void> {
        try {
            // 检查数据库中是否已有足够的数据
            const existingPlayers = await this.getPlayersData(10, 0);
            if (existingPlayers.length < 5) {
                console.log(`CloudDatabaseManager: 数据库中只有${existingPlayers.length}条数据，自动上传200条初始数据...`);
                const success = await this.uploadMockPlayersData(200);
                if (success) {
                    console.log('CloudDatabaseManager: 初始数据上传完成！');
                } else {
                    console.error('CloudDatabaseManager: 初始数据上传失败！');
                }
            } else {
                console.log(`CloudDatabaseManager: 数据库中已有${existingPlayers.length}条数据，跳过初始化上传`);
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 检查初始数据时出错', error);
        }
    }

    /**
     * 检查是否已初始化
     */
    private checkInitialized(): boolean {
        if (!this._isInitialized || !this._db) {
            console.error('CloudDatabaseManager: 数据库未初始化，请先调用initialize()');
            return false;
        }
        return true;
    }

    /**
     * 生成随机邀请码
     */
    private generateInviteCode(): string {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * 生成随机昵称
     */
    private generateRandomNickname(index: number): string {
        const names = ['小鸟', '企鹅', '海豚', '熊猫', '狐狸', '兔子', '松鼠', '猫咪', '老鹰', '燕子', '麻雀', '鸽子', '翠鸟', '天鹅', '孔雀', '鹦鹉'];

        const name = names[index % names.length];
        const number = String(index).padStart(3, '0');

        return `${name}${number}`;
    }

    /**
     * 生成随机分数
     */
    private generateRandomScore(min: number, max: number): number {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 生成每个关卡的最高三次分数
     */
    private generateTopScores(): CloudPlayerData['topScores'] {
        return {
            [GameMode.NORMAL_EASY]: [
                this.generateRandomScore(80, 200),
                this.generateRandomScore(60, 180),
                this.generateRandomScore(40, 160)
            ].sort((a, b) => b - a), // 降序排列
            [GameMode.NORMAL_STANDARD]: [
                this.generateRandomScore(60, 150),
                this.generateRandomScore(40, 130),
                this.generateRandomScore(20, 110)
            ].sort((a, b) => b - a),
            [GameMode.NORMAL_HARD]: [
                this.generateRandomScore(40, 100),
                this.generateRandomScore(20, 80),
                this.generateRandomScore(10, 60)
            ].sort((a, b) => b - a),
            [GameMode.CHALLENGE_WIND]: [
                this.generateRandomScore(30, 80),
                this.generateRandomScore(15, 65),
                this.generateRandomScore(5, 50)
            ].sort((a, b) => b - a),
            [GameMode.CHALLENGE_FOG]: [
                this.generateRandomScore(30, 80),
                this.generateRandomScore(15, 65),
                this.generateRandomScore(5, 50)
            ].sort((a, b) => b - a),
            [GameMode.CHALLENGE_SNOW]: [
                this.generateRandomScore(30, 80),
                this.generateRandomScore(15, 65),
                this.generateRandomScore(5, 50)
            ].sort((a, b) => b - a)
        };
    }

    /**
     * 批量上传模拟玩家数据到云数据库
     */
    public async uploadMockPlayersData(count: number = 200): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            console.log(`CloudDatabaseManager: 开始生成并上传${count}个模拟玩家数据...`);

            // 逐个上传，避免批量上传的数组问题
            let successCount = 0;

            for (let i = 0; i < count; i++) {
                const playerData: CloudPlayerData = {
                    nickname: this.generateRandomNickname(i),
                    avatarUrl: '1_penguin_home', // 暂时使用统一头像
                    coins: this.generateRandomScore(1000, 10000),
                    inviteCode: this.generateInviteCode(),
                    topScores: this.generateTopScores()
                };

                try {
                    // 单个上传，传入对象而不是数组
                    const result = await this._db.collection(this.COLLECTION_NAME).add({
                        data: playerData
                    });

                    if (result.errMsg === 'collection.add:ok') {
                        successCount++;
                        if ((i + 1) % 10 === 0) {
                            console.log(`CloudDatabaseManager: 已上传${i + 1}/${count}个玩家数据`);
                        }
                    } else {
                        console.error(`CloudDatabaseManager: 第${i + 1}个玩家数据上传失败`, result);
                    }
                } catch (singleError) {
                    console.error(`CloudDatabaseManager: 第${i + 1}个玩家数据上传异常`, singleError);
                }

                // 每10个数据后添加短暂延迟，避免请求过于频繁
                if ((i + 1) % 10 === 0 && i < count - 1) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
            }

            console.log(`CloudDatabaseManager: 上传完成，成功${successCount}/${count}个模拟玩家数据`);
            return successCount > 0;
        } catch (error) {
            console.error('CloudDatabaseManager: 上传模拟数据失败', error);
            return false;
        }
    }

    /**
     * 获取指定关卡的全服排行榜数据（按分数排序）
     * @param gameMode 游戏模式
     * @param limit 返回数量限制，默认100，最大100
     * @returns 排行榜数据
     */
    public async getGlobalLeaderboard(gameMode: GameMode, limit: number = 100): Promise<CloudPlayerData[]> {
        if (!this.checkInitialized()) {
            return [];
        }

        try {
            console.log(`CloudDatabaseManager: 获取关卡${gameMode}的全服排行榜，前${limit}名`);

            // 调用云函数获取按分数排序的排行榜
            if (typeof wx !== 'undefined' && wx.cloud && wx.cloud.callFunction) {
                const callData = {
                    gameMode: gameMode,
                    limit: Math.min(limit, 100),
                    offset: 0
                };
                console.log(`CloudDatabaseManager: 调用云函数参数:`, callData);
                console.log(`CloudDatabaseManager: gameMode类型: ${typeof gameMode}, 值: ${gameMode}`);

                const result = await wx.cloud.callFunction({
                    name: 'getLeaderboard',
                    data: callData
                });

                if (result.result && result.result.success) {
                    const players = result.result.data.players.map((player: any) => ({
                        _id: player._id,
                        nickname: player.nickname,
                        avatarUrl: player.avatarUrl,
                        coins: player.coins,
                        topScores: player.topScores || {},
                        rank: player.rank,
                        maxScore: player.maxScore
                    }));

                    console.log(`CloudDatabaseManager: 成功获取关卡${gameMode}的排行榜数据，共${players.length}条`);
                    console.log(`CloudDatabaseManager: 云函数原始返回数据量: ${result.result.data.players.length}`);
                    if (players.length > 0) {
                        console.log(`CloudDatabaseManager: 排行榜第1名: ${players[0].nickname}, 分数: ${players[0].maxScore}`);
                        if (players.length > 1) {
                            console.log(`CloudDatabaseManager: 排行榜最后1名: ${players[players.length-1].nickname}, 分数: ${players[players.length-1].maxScore}`);
                        }
                    }
                    return players;
                } else {
                    console.error('CloudDatabaseManager: 云函数返回错误', result.result?.error);
                    return [];
                }
            } else {
                console.warn('CloudDatabaseManager: 微信云函数不可用，使用本地查询');
                return await this.getLeaderboardLocal(gameMode, limit);
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 调用排行榜云函数异常', error);
            return [];
        }
    }

    /**
     * 批量获取所有关卡的全服排行榜数据
     * @param limit 每个关卡返回数量限制，默认100
     * @returns 所有关卡的排行榜数据
     */
    public async getAllGlobalLeaderboards(limit: number = 100): Promise<{[key: number]: CloudPlayerData[]}> {
        if (!this.checkInitialized()) {
            return {};
        }

        try {
            console.log(`CloudDatabaseManager: 批量获取所有关卡的全服排行榜，每个关卡前${limit}名`);

            // 调用云函数批量获取所有关卡的排行榜
            if (typeof wx !== 'undefined' && wx.cloud && wx.cloud.callFunction) {
                const callData = {
                    getAllModes: true,
                    limit: Math.min(limit, 100),
                    offset: 0
                };
                console.log(`CloudDatabaseManager: 调用批量云函数参数:`, callData);

                const result = await wx.cloud.callFunction({
                    name: 'getLeaderboard',
                    data: callData
                });

                if (result.result && result.result.success) {
                    const allModesData: {[key: number]: CloudPlayerData[]} = {};
                    const rawData = result.result.data.allModes;

                    // 处理每个关卡的数据
                    Object.keys(rawData).forEach(modeStr => {
                        const mode = parseInt(modeStr);
                        const players = rawData[mode].map((player: any) => ({
                            _id: player._id,
                            nickname: player.nickname,
                            avatarUrl: player.avatarUrl,
                            coins: player.coins,
                            topScores: player.topScores || {},
                            rank: player.rank,
                            maxScore: player.maxScore
                        }));
                        allModesData[mode] = players;
                    });

                    const totalCount = Object.values(allModesData).reduce((sum, players) => sum + players.length, 0);
                    console.log(`CloudDatabaseManager: 成功批量获取所有关卡排行榜数据，总计${totalCount}条`);

                    // 打印每个关卡的数据量
                    Object.keys(allModesData).forEach(modeStr => {
                        const mode = parseInt(modeStr);
                        console.log(`CloudDatabaseManager: 关卡${mode}获取${allModesData[mode].length}条数据`);
                    });

                    return allModesData;
                } else {
                    console.error('CloudDatabaseManager: 批量云函数返回错误', result.result?.error);
                    return {};
                }
            } else {
                console.warn('CloudDatabaseManager: 微信云函数不可用，无法批量获取');
                return {};
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 调用批量排行榜云函数异常', error);
            return {};
        }
    }

    /**
     * 从缓存集合获取所有关卡的排行榜数据（本地API，不调用云函数）
     * @returns 所有关卡的排行榜数据
     */
    public async getAllGlobalLeaderboardsFromCache(): Promise<{[key: number]: CloudPlayerData[]}> {
        if (!this.checkInitialized()) {
            return {};
        }

        try {
            console.log(`CloudDatabaseManager: 从缓存集合获取所有关卡排行榜数据`);

            // 开发模式下返回空数据
            const wxContext = await this.getWxContext();
            if (wxContext && wxContext.openid && wxContext.openid.startsWith('dev_openid_')) {
                console.log('CloudDatabaseManager: 开发模式，跳过缓存查询');
                return {};
            }

            // 查询缓存集合
            const result = await this._db.collection('leaderboard_cache')
                .orderBy('gameMode', 'asc')
                .get();

            if (result.errMsg === 'collection.get:ok') {
                const allModesData: {[key: number]: CloudPlayerData[]} = {};

                // 处理每个关卡的缓存数据
                result.data.forEach((cacheItem: any) => {
                    const mode = cacheItem.gameMode;
                    const players = cacheItem.players.map((player: any) => ({
                        _id: `cache_${mode}_${player.rank}`, // 生成临时ID
                        nickname: player.nickname,
                        avatarUrl: player.avatarUrl,
                        coins: 0, // 缓存中不存储金币信息
                        topScores: {}, // 缓存中不包含完整topScores，只有maxScore
                        rank: player.rank,
                        maxScore: player.maxScore
                    }));
                    allModesData[mode] = players;

                    console.log(`CloudDatabaseManager: 从缓存获取关卡${mode}数据${players.length}条，更新时间: ${cacheItem.updateTime}`);
                });

                const totalCount = Object.values(allModesData).reduce((sum, players) => sum + players.length, 0);
                console.log(`CloudDatabaseManager: 成功从缓存获取所有关卡排行榜数据，总计${totalCount}条`);

                return allModesData;
            } else {
                console.error('CloudDatabaseManager: 查询排行榜缓存失败', result);
                return {};
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 从缓存获取排行榜数据异常', error);
            return {};
        }
    }

    /**
     * 获取排行榜缓存的最后更新时间
     * @returns 最后更新时间的时间戳数组，按关卡顺序
     */
    public async getLeaderboardCacheUpdateTimes(): Promise<{[key: number]: number}> {
        if (!this.checkInitialized()) {
            return {};
        }

        try {
            // 开发模式下返回当前时间
            const wxContext = await this.getWxContext();
            if (wxContext && wxContext.openid && wxContext.openid.startsWith('dev_openid_')) {
                const now = Date.now();
                return {0: now, 1: now, 2: now, 3: now, 4: now, 5: now};
            }

            const result = await this._db.collection('leaderboard_cache')
                .field({
                    gameMode: true,
                    updateTime: true
                })
                .get();

            if (result.errMsg === 'collection.get:ok') {
                const updateTimes: {[key: number]: number} = {};

                result.data.forEach((cacheItem: any) => {
                    const mode = cacheItem.gameMode;
                    const updateTime = new Date(cacheItem.updateTime).getTime();
                    updateTimes[mode] = updateTime;
                });

                console.log(`CloudDatabaseManager: 获取缓存更新时间:`, updateTimes);
                return updateTimes;
            } else {
                console.error('CloudDatabaseManager: 查询缓存更新时间失败', result);
                return {};
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 获取缓存更新时间异常', error);
            return {};
        }
    }

    /**
     * 测试云函数是否正常工作
     */
    public async testCloudFunction(gameMode: GameMode): Promise<void> {
        console.log(`CloudDatabaseManager: 测试云函数获取关卡${gameMode}的排行榜`);

        try {
            if (typeof wx !== 'undefined' && wx.cloud && wx.cloud.callFunction) {
                const result = await wx.cloud.callFunction({
                    name: 'getLeaderboard',
                    data: {
                        gameMode: gameMode,
                        limit: 100,
                        offset: 0
                    }
                });

                console.log('CloudDatabaseManager: 云函数调用结果:', result);

                if (result.result && result.result.success) {
                    console.log(`CloudDatabaseManager: 云函数成功返回${result.result.data.players.length}条数据`);
                    console.log('CloudDatabaseManager: 前5名数据:', result.result.data.players.slice(0, 5));
                } else {
                    console.error('CloudDatabaseManager: 云函数返回错误:', result.result?.error);
                }
            } else {
                console.warn('CloudDatabaseManager: 微信云函数不可用');
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 测试云函数异常', error);
        }
    }

    /**
     * 获取当前用户数据（公共接口，包含开发模式检查）
     */
    public async getCurrentUserData(): Promise<any> {
        if (!this.checkInitialized()) {
            return null;
        }

        try {
            // 获取当前用户的openid
            const wxContext = await this.getWxContext();
            if (!wxContext || !wxContext.openid) {
                console.error('CloudDatabaseManager: 无法获取用户openid');
                return null;
            }

            // 开发模式下跳过云数据库查询
            if (wxContext.openid.startsWith('dev_openid_')) {
                console.log('CloudDatabaseManager: 开发模式，跳过云数据库查询');
                return null;
            }

            return await this.getCurrentUserDataInternal();
        } catch (error) {
            console.error('CloudDatabaseManager: 获取当前用户数据失败', error);
            return null;
        }
    }

    /**
     * 获取当前用户数据的内部实现（不包含开发模式检查）
     */
    private async getCurrentUserDataInternal(): Promise<any> {
        try {
            // 🔧 修改：直接使用_openid查询，不再使用本地保存的用户ID
            const wxContext = await this.getWxContext();
            if (!wxContext || !wxContext.openid) {
                console.log('CloudDatabaseManager: 无法获取微信上下文');
                return null;
            }

            console.log(`CloudDatabaseManager: 通过openid查询: ${wxContext.openid}`);
            const result = await this._db.collection(this.COLLECTION_NAME)
                .where({
                    _openid: wxContext.openid
                })
                .get();

            console.log(`CloudDatabaseManager: openid查询结果 - errMsg: ${result.errMsg}, 数据条数: ${result.data.length}`);

            if (result.errMsg === 'collection.get:ok' && result.data.length > 0) {
                const userData = result.data[0];
                console.log(`CloudDatabaseManager: 通过openid查询成功 - 昵称: ${userData.nickname}, ID: ${userData._id}`);
                return userData;
            }

            console.log('CloudDatabaseManager: 未找到当前用户数据');
            return null;
        } catch (error) {
            console.error('CloudDatabaseManager: 获取当前用户数据失败', error);
            return null;
        }
    }



    /**
     * 创建或更新用户数据
     */
    public async createOrUpdateUserData(userData: any): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 获取当前用户的openid
            const wxContext = await this.getWxContext();
            if (!wxContext || !wxContext.openid) {
                console.error('CloudDatabaseManager: 无法获取用户openid');
                return false;
            }

            // 开发模式下跳过云数据库操作
            if (wxContext.openid.startsWith('dev_openid_')) {
                console.log('CloudDatabaseManager: 开发模式，跳过云数据库写入操作');
                return true; // 返回true表示"成功"
            }

            // 检查用户是否已存在（开发模式下跳过）
            let existingUser = null;
            if (!wxContext.openid.startsWith('dev_openid_')) {
                existingUser = await this.getCurrentUserDataInternal();
            }

            if (existingUser) {
                // 🔧 修改：使用_openid更新现有用户数据
                console.log('CloudDatabaseManager: 🔍 更新现有用户数据 - 昵称:', userData.nickname, '头像:', userData.avatarUrl);

                const result = await this._db.collection(this.COLLECTION_NAME)
                    .where({
                        _openid: wxContext.openid
                    })
                    .update({
                        data: userData
                    });

                if (result.stats.updated > 0) {
                    console.log('CloudDatabaseManager: ✅ 用户数据更新成功 - 昵称:', userData.nickname);
                    return true;
                } else {
                    console.error('CloudDatabaseManager: ❌ 用户数据更新失败', result);
                    return false;
                }
            } else {
                // 创建新用户数据
                console.log('CloudDatabaseManager: 🔍 创建新用户数据 - 昵称:', userData.nickname, '头像:', userData.avatarUrl);

                const result = await this._db.collection(this.COLLECTION_NAME)
                    .add({
                        data: {
                            ...userData,
                            _openid: wxContext.openid,
                            createTime: new Date(),
                            updateTime: new Date()
                        }
                    });

                console.log(`CloudDatabaseManager: 创建用户数据 - openid: ${wxContext.openid}`);

                if (result.errMsg === 'collection.add:ok') {
                    console.log(`CloudDatabaseManager: 用户数据创建成功，新用户ID: ${result._id}`);
                    return true;
                } else {
                    console.error('CloudDatabaseManager: 用户数据创建失败', result);
                    return false;
                }
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 创建或更新用户数据失败', error);
            return false;
        }
    }

    /**
     * 获取微信上下文信息
     */
    public async getWxContext(): Promise<any> {
        try {
            if (typeof wx !== 'undefined' && wx.cloud && wx.cloud.callFunction) {
                const result = await wx.cloud.callFunction({
                    name: 'login',
                    data: {}
                });

                console.log('CloudDatabaseManager: login云函数返回结果', result);

                if (result.result && result.result.success) {
                    const context = {
                        openid: result.result.openid,
                        appid: result.result.appid,
                        unionid: result.result.unionid
                    };

                    console.log('CloudDatabaseManager: 解析的微信上下文', context);

                    // 🔧 检查openid是否有效
                    if (!context.openid || context.openid === 'undefined') {
                        console.error('CloudDatabaseManager: 获取到无效的openid', context.openid);
                        return null;
                    }

                    return context;
                } else {
                    console.error('CloudDatabaseManager: login云函数调用失败', result);
                }
            } else {
                console.warn('CloudDatabaseManager: 微信云开发环境不可用');
            }
            return null;
        } catch (error) {
            console.error('CloudDatabaseManager: 获取微信上下文失败', error);
            return null;
        }
    }

    /**
     * 查询玩家数据（按金币排序，用于兼容性）
     * @deprecated 建议使用 getGlobalLeaderboard 获取按分数排序的数据
     */
    public async getPlayersData(limit: number = 100, skip: number = 0): Promise<CloudPlayerData[]> {
        if (!this.checkInitialized()) {
            return [];
        }

        try {
            console.log(`CloudDatabaseManager: 查询${limit}条玩家数据（按金币排序）`);

            // 微信小程序云数据库默认限制是20条，需要分批查询
            if (limit <= 20) {
                const result = await this._db.collection(this.COLLECTION_NAME)
                    .limit(limit)
                    .skip(skip)
                    .orderBy('coins', 'desc')
                    .get();

                if (result.errMsg === 'collection.get:ok') {
                    console.log(`CloudDatabaseManager: 成功获取${result.data.length}条玩家数据`);
                    return result.data as CloudPlayerData[];
                } else {
                    console.error('CloudDatabaseManager: 查询玩家数据失败', result);
                    return [];
                }
            } else {
                // 分批查询，每批20条
                console.log(`CloudDatabaseManager: 需要分批查询${limit}条数据，每批20条`);
                const allPlayers: CloudPlayerData[] = [];
                let currentSkip = skip;
                let remainingLimit = limit;

                while (remainingLimit > 0) {
                    const batchLimit = Math.min(remainingLimit, 20);
                    console.log(`CloudDatabaseManager: 查询第${Math.floor((currentSkip - skip)/20) + 1}批，跳过${currentSkip}条，获取${batchLimit}条`);

                    const result = await this._db.collection(this.COLLECTION_NAME)
                        .limit(batchLimit)
                        .skip(currentSkip)
                        .orderBy('coins', 'desc')
                        .get();

                    if (result.errMsg === 'collection.get:ok') {
                        const batchData = result.data as CloudPlayerData[];
                        allPlayers.push(...batchData);
                        console.log(`CloudDatabaseManager: 第${Math.floor((currentSkip - skip)/20) + 1}批获取${batchData.length}条数据 (请求${batchLimit}条)`);

                        // 显示这批数据的详细信息
                        if (batchData.length > 0) {
                            console.log(`CloudDatabaseManager: 这批数据示例 - 第1个: ${batchData[0].nickname}, 金币: ${batchData[0].coins}`);
                            if (batchData.length > 1) {
                                console.log(`CloudDatabaseManager: 这批数据示例 - 最后1个: ${batchData[batchData.length-1].nickname}, 金币: ${batchData[batchData.length-1].coins}`);
                            }
                        }

                        // 如果返回的数据少于请求的数量，说明没有更多数据了
                        if (batchData.length < batchLimit) {
                            console.log(`CloudDatabaseManager: 数据已全部获取，总计${allPlayers.length}条 (最后一批只有${batchData.length}条，少于请求的${batchLimit}条)`);
                            break;
                        }

                        currentSkip += batchLimit;
                        remainingLimit -= batchLimit;
                        console.log(`CloudDatabaseManager: 继续查询下一批，当前已获取${allPlayers.length}条，还需${remainingLimit}条`);
                    } else {
                        console.error('CloudDatabaseManager: 分批查询玩家数据失败', result);
                        break;
                    }
                }

                console.log(`CloudDatabaseManager: 分批查询完成，共获取${allPlayers.length}条玩家数据`);
                return allPlayers;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 查询玩家数据异常', error);
            return [];
        }
    }

    /**
     * 根据关卡获取排行榜数据（使用云函数）
     */
    public async getLeaderboard(gameMode: GameMode, limit: number = 50): Promise<CloudPlayerData[]> {
        if (!this.checkInitialized()) {
            return [];
        }

        try {
            // 调用云函数获取排行榜
            if (typeof wx !== 'undefined' && wx.cloud && wx.cloud.callFunction) {
                const result = await wx.cloud.callFunction({
                    name: 'getLeaderboard',
                    data: {
                        gameMode: gameMode,
                        limit: limit,
                        offset: 0
                    }
                });

                if (result.result && result.result.success) {
                    console.log(`CloudDatabaseManager: 成功获取关卡${gameMode}的排行榜数据，共${result.result.data.players.length}条`);
                    return result.result.data.players;
                } else {
                    console.error('CloudDatabaseManager: 云函数返回错误', result.result?.error);
                    return [];
                }
            } else {
                console.warn('CloudDatabaseManager: 微信云函数不可用，使用本地查询');
                return await this.getLeaderboardLocal(gameMode, limit);
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 调用排行榜云函数异常', error);
            return [];
        }
    }

    /**
     * 本地排行榜查询（备用方案）
     */
    private async getLeaderboardLocal(gameMode: GameMode, limit: number): Promise<CloudPlayerData[]> {
        try {
            const result = await this._db.collection(this.COLLECTION_NAME)
                .limit(Math.min(limit * 2, 200)) // 限制查询数量
                .get();

            if (result.errMsg === 'collection.get:ok') {
                const players = result.data as CloudPlayerData[];

                const sortedPlayers = players
                    .filter(player => player.topScores && player.topScores[gameMode] && player.topScores[gameMode].length > 0)
                    .sort((a, b) => {
                        const scoreA = Math.max(...a.topScores[gameMode]);
                        const scoreB = Math.max(...b.topScores[gameMode]);
                        return scoreB - scoreA;
                    })
                    .slice(0, limit);

                return sortedPlayers;
            } else {
                return [];
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 本地排行榜查询失败', error);
            return [];
        }
    }

    /**
     * 清空玩家数据集合（仅用于测试）
     */
    public async clearPlayersData(): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 注意：云数据库不支持直接清空集合，需要逐条删除
            console.log('CloudDatabaseManager: 开始清空玩家数据...');
            
            // 先查询所有数据
            const result = await this._db.collection(this.COLLECTION_NAME).get();
            
            if (result.errMsg === 'collection.get:ok' && result.data.length > 0) {
                // 分批删除
                const batchSize = 20;
                const batches = Math.ceil(result.data.length / batchSize);
                
                for (let batch = 0; batch < batches; batch++) {
                    const startIndex = batch * batchSize;
                    const endIndex = Math.min(startIndex + batchSize, result.data.length);
                    
                    const deletePromises = [];
                    for (let i = startIndex; i < endIndex; i++) {
                        const player = result.data[i];
                        deletePromises.push(
                            this._db.collection(this.COLLECTION_NAME).doc(player._id).remove()
                        );
                    }
                    
                    await Promise.all(deletePromises);
                    console.log(`CloudDatabaseManager: 删除第${batch + 1}批数据 (${startIndex + 1}-${endIndex})`);
                    
                    // 添加延迟
                    if (batch < batches - 1) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }
                
                console.log(`CloudDatabaseManager: 成功清空${result.data.length}条玩家数据`);
            } else {
                console.log('CloudDatabaseManager: 没有数据需要清空');
            }
            
            return true;
        } catch (error) {
            console.error('CloudDatabaseManager: 清空数据失败', error);
            return false;
        }
    }

    /**
     * 更新玩家分数（本地数据库操作）
     */
    public async updatePlayerScore(gameMode: GameMode, score: number): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 开发模式下跳过
            const wxContext = await this.getWxContext();
            if (wxContext && wxContext.openid && wxContext.openid.startsWith('dev_openid_')) {
                console.log('CloudDatabaseManager: 开发模式，跳过分数更新');
                return true;
            }

            // 获取当前用户数据
            const currentUser = await this.getCurrentUserDataInternal();
            if (!currentUser) {
                console.error('CloudDatabaseManager: 未找到当前用户数据');
                return false;
            }

            // 获取当前关卡的分数数组
            const currentScores = currentUser.topScores[gameMode] || [];

            // 添加新分数并保持最高的3个分数
            const newScores = [...currentScores, score]
                .sort((a, b) => b - a) // 降序排列
                .slice(0, 3); // 只保留前3个

            // 🔧 修改：使用_openid更新数据库
            const updateResult = await this._db.collection(this.COLLECTION_NAME)
                .where({
                    _openid: wxContext.openid
                })
                .update({
                    data: {
                        [`topScores.${gameMode}`]: newScores,
                        updatedAt: new Date()
                    }
                });

            if (updateResult.stats.updated === 1) {
                const isNewRecord = newScores[0] === score;
                console.log(`CloudDatabaseManager: 成功更新分数 ${gameMode}: ${score}${isNewRecord ? ' 🎉 新纪录！' : ''}`);
                return true;
            } else {
                console.error('CloudDatabaseManager: 更新分数失败');
                return false;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 更新分数异常', error);
            return false;
        }
    }

    /**
     * 🚀 终极优化：一次调用更新所有玩家数据（所有关卡分数+金币）
     * 这是最高效的同步方式，只需要1次云调用
     */
    public async updateAllPlayerData(allTopScores: any, coins: number): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 开发模式下跳过
            if (typeof wx !== 'undefined') {
                // 🔧 优化：直接使用缓存的openid，避免调用login云函数
                const cachedOpenid = this.getCachedOpenid();
                if (!cachedOpenid) {
                    console.error('CloudDatabaseManager: 无法获取缓存的openid');
                    return false;
                }

                if (cachedOpenid.startsWith('dev_openid_')) {
                    console.log('CloudDatabaseManager: 开发模式，跳过所有数据更新');
                    return true;
                }

                // 🚀 终极优化：直接更新，无需查询当前数据
                // 使用本地数据直接覆盖云端数据，避免查询操作
                const updateResult = await this._db.collection(this.COLLECTION_NAME)
                    .where({
                        _openid: cachedOpenid
                    })
                    .update({
                        data: {
                            topScores: allTopScores,
                            coins: coins,
                            updatedAt: new Date()
                        }
                    });

                if (updateResult.stats.updated === 1) {
                    console.log(`CloudDatabaseManager: 🚀 一次调用成功更新所有数据 - 金币:${coins}, 所有关卡分数已同步`);
                    return true;
                } else {
                    console.error('CloudDatabaseManager: 一次性更新失败');
                    return false;
                }
            } else {
                console.log('CloudDatabaseManager: 非微信环境，跳过云数据库操作');
                return true;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 一次性更新异常', error);
            return false;
        }
    }

    /**
     * 获取缓存的openid（避免重复调用login云函数）
     */
    private getCachedOpenid(): string | null {
        // 🔧 修复：直接导入WeChatLoginManager获取openid
        try {
            const loginManager = WeChatLoginManager.instance;
            if (loginManager && loginManager.getOpenId) {
                const openid = loginManager.getOpenId();
                console.log(`CloudDatabaseManager: 从WeChatLoginManager获取openid: ${openid}`);
                return openid;
            }
        } catch (error) {
            console.warn('CloudDatabaseManager: 从WeChatLoginManager获取openid失败', error);
        }

        // 备用方案：从本地存储获取
        try {
            const cachedOpenid = localStorage.getItem('cached_openid');
            console.log(`CloudDatabaseManager: 从localStorage获取openid: ${cachedOpenid}`);
            return cachedOpenid;
        } catch (error) {
            console.error('CloudDatabaseManager: 获取缓存openid失败', error);
            return null;
        }
    }

    /**
     * 🎯 智能优化：仅更新玩家金币（每局必须调用）
     */
    public async updatePlayerCoinsOnly(coins: number): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 开发模式下跳过
            if (typeof wx !== 'undefined') {
                const cachedOpenid = this.getCachedOpenid();
                if (!cachedOpenid) {
                    console.error('CloudDatabaseManager: 无法获取缓存的openid');
                    return false;
                }

                if (cachedOpenid.startsWith('dev_openid_')) {
                    console.log('CloudDatabaseManager: 开发模式，跳过金币更新');
                    return true;
                }

                // 🎯 仅更新金币
                const updateResult = await this._db.collection(this.COLLECTION_NAME)
                    .where({
                        _openid: cachedOpenid
                    })
                    .update({
                        data: {
                            coins: coins,
                            updatedAt: new Date()
                        }
                    });

                // 🔧 修复误报：检查更新结果的多种成功情况
                const isSuccess = updateResult.stats.updated >= 1 ||
                                updateResult.errMsg === 'collection.update:ok' ||
                                updateResult.errMsg.includes('ok');

                if (isSuccess) {
                    console.log(`CloudDatabaseManager: 🎯 金币更新成功 - 金币:${coins}`);
                    return true;
                } else {
                    console.error('CloudDatabaseManager: 金币更新失败 - 更新条数:', updateResult.stats.updated);
                    console.error('CloudDatabaseManager: 错误信息:', updateResult.errMsg);
                    return false;
                }
            } else {
                console.log('CloudDatabaseManager: 非微信环境，跳过云数据库操作');
                return true;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 金币更新异常', error);
            return false;
        }
    }

    /**
     * 🎯 终极优化：一次性更新金币和所有游戏记录（每局游戏调用）
     */
    public async updatePlayerCoinsAndAllScores(coins: number, allTopScores: any): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 开发模式下跳过
            if (typeof wx !== 'undefined') {
                const cachedOpenid = this.getCachedOpenid();
                if (!cachedOpenid) {
                    console.error('CloudDatabaseManager: 无法获取缓存的openid');
                    return false;
                }

                if (cachedOpenid.startsWith('dev_openid_')) {
                    console.log('CloudDatabaseManager: 开发模式，跳过数据更新');
                    return true;
                }

                // 🎯 一次性更新金币和所有游戏记录
                console.log('CloudDatabaseManager: 开始一次性更新金币和游戏记录到云数据库');
                console.log('CloudDatabaseManager: 要更新的金币:', coins);
                console.log('CloudDatabaseManager: 要更新的游戏记录:', allTopScores);

                const updateResult = await this._db.collection(this.COLLECTION_NAME)
                    .where({
                        _openid: cachedOpenid
                    })
                    .update({
                        data: {
                            coins: coins,
                            topScores: allTopScores,
                            updatedAt: new Date()
                        }
                    });

                console.log('CloudDatabaseManager: 🔍 更新操作完整结果:', updateResult);

                // 🔧 修复误报：检查更新结果的多种成功情况
                const isSuccess = updateResult.stats.updated >= 1 ||
                                updateResult.errMsg === 'collection.update:ok' ||
                                updateResult.errMsg.includes('ok');

                if (isSuccess) {
                    console.log(`CloudDatabaseManager: ✅ 金币和游戏记录一次性更新成功`);
                    console.log(`CloudDatabaseManager: 🎯 优化效果：1次云调用完成所有数据同步`);
                    console.log(`CloudDatabaseManager: 更新的金币: ${coins}`);
                    console.log(`CloudDatabaseManager: 更新的游戏记录:`, allTopScores);
                    return true;
                } else {
                    console.error('CloudDatabaseManager: ❌ 数据更新失败 - 更新条数:', updateResult.stats.updated);
                    console.error('CloudDatabaseManager: 错误信息:', updateResult.errMsg);
                    console.error('CloudDatabaseManager: 完整更新结果:', updateResult);
                    return false;
                }
            } else {
                console.log('CloudDatabaseManager: 非微信环境，跳过云数据库操作');
                return true;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 数据更新异常', error);
            return false;
        }
    }

    /**
     * 🎯 智能优化：仅更新所有游戏记录（按需调用）
     */
    public async updateAllTopScores(allTopScores: any): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 开发模式下跳过
            if (typeof wx !== 'undefined') {
                // 🔧 修复：优先尝试获取缓存的openid，失败则调用getWxContext
                let openid = this.getCachedOpenid();
                if (!openid) {
                    console.warn('CloudDatabaseManager: 缓存openid不可用，尝试获取微信上下文');
                    const wxContext = await this.getWxContext();
                    if (wxContext && wxContext.openid) {
                        openid = wxContext.openid;
                        console.log('CloudDatabaseManager: 通过微信上下文获取openid成功');
                    } else {
                        console.error('CloudDatabaseManager: 无法获取有效的openid');
                        return false;
                    }
                }

                console.log(`CloudDatabaseManager: 使用openid进行游戏记录更新: ${openid}`);

                if (openid.startsWith('dev_openid_')) {
                    console.log('CloudDatabaseManager: 开发模式，跳过游戏记录更新');
                    return true;
                }

                // 🔧 调试：先查询当前用户数据，确认记录存在
                console.log('CloudDatabaseManager: 🔍 调试 - 先查询当前用户数据');
                const queryResult = await this._db.collection(this.COLLECTION_NAME)
                    .where({
                        _openid: openid
                    })
                    .get();

                console.log('CloudDatabaseManager: 🔍 查询结果:', {
                    errMsg: queryResult.errMsg,
                    dataLength: queryResult.data?.length || 0,
                    data: queryResult.data
                });

                if (!queryResult.data || queryResult.data.length === 0) {
                    console.error('CloudDatabaseManager: ❌ 未找到用户记录，无法更新游戏分数');
                    return false;
                }

                // 🎯 仅更新游戏记录
                console.log('CloudDatabaseManager: 开始更新游戏记录到云数据库');
                console.log('CloudDatabaseManager: 要更新的数据:', allTopScores);

                const updateResult = await this._db.collection(this.COLLECTION_NAME)
                    .where({
                        _openid: openid
                    })
                    .update({
                        data: {
                            topScores: allTopScores,
                            updatedAt: new Date()
                        }
                    });

                console.log('CloudDatabaseManager: 🔍 更新操作完整结果:', updateResult);

                // 🔧 修复误报：检查更新结果的多种成功情况
                const isSuccess = updateResult.stats.updated >= 1 ||
                                updateResult.errMsg === 'collection.update:ok' ||
                                updateResult.errMsg.includes('ok');

                if (isSuccess) {
                    console.log(`CloudDatabaseManager: ✅ 游戏记录更新成功 - 所有关卡分数已同步`);
                    console.log(`CloudDatabaseManager: 更新的数据:`, allTopScores);
                    return true;
                } else {
                    console.error('CloudDatabaseManager: ❌ 游戏记录更新失败 - 更新条数:', updateResult.stats.updated);
                    console.error('CloudDatabaseManager: 错误信息:', updateResult.errMsg);
                    console.error('CloudDatabaseManager: 完整更新结果:', updateResult);
                    return false;
                }
            } else {
                console.log('CloudDatabaseManager: 非微信环境，跳过云数据库操作');
                return true;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 游戏记录更新异常', error);
            return false;
        }
    }

    /**
     * 🔧 优化：合并更新玩家分数和金币（减少云调用次数）
     */
    public async updatePlayerScoreAndCoins(gameMode: GameMode, score: number, coins: number): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 开发模式下跳过
            const wxContext = await this.getWxContext();
            if (wxContext && wxContext.openid && wxContext.openid.startsWith('dev_openid_')) {
                console.log('CloudDatabaseManager: 开发模式，跳过分数和金币更新');
                return true;
            }

            // 获取当前用户数据
            const currentUser = await this.getCurrentUserDataInternal();
            if (!currentUser) {
                console.error('CloudDatabaseManager: 未找到当前用户数据');
                return false;
            }

            // 计算新的分数数组
            const currentScores = currentUser.topScores[gameMode] || [];
            const newScores = [...currentScores, score]
                .sort((a, b) => b - a) // 降序排列
                .slice(0, 3); // 只保留前3个

            // 🔧 合并更新：一次操作同时更新分数和金币
            const updateResult = await this._db.collection(this.COLLECTION_NAME)
                .where({
                    _openid: wxContext.openid
                })
                .update({
                    data: {
                        [`topScores.${gameMode}`]: newScores,
                        coins: coins,
                        updatedAt: new Date()
                    }
                });

            if (updateResult.stats.updated === 1) {
                const isNewRecord = newScores[0] === score;
                console.log(`CloudDatabaseManager: 成功合并更新 - 分数${gameMode}: ${score}${isNewRecord ? ' 🎉 新纪录！' : ''}, 金币: ${coins}`);
                return true;
            } else {
                console.error('CloudDatabaseManager: 合并更新失败');
                return false;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 合并更新异常', error);
            return false;
        }
    }

    /**
     * 更新玩家金币（本地数据库操作）
     */
    public async updatePlayerCoins(amount: number, operation: 'add' | 'set' = 'add'): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 开发模式下跳过
            const wxContext = await this.getWxContext();
            if (wxContext && wxContext.openid && wxContext.openid.startsWith('dev_openid_')) {
                console.log('CloudDatabaseManager: 开发模式，跳过金币更新');
                return true;
            }

            // 获取当前用户数据
            const currentUser = await this.getCurrentUserDataInternal();
            if (!currentUser) {
                console.error('CloudDatabaseManager: 未找到当前用户数据');
                return false;
            }

            let updateData: any = {
                updatedAt: new Date()
            };

            if (operation === 'add') {
                updateData.coins = this._db.command.inc(amount);
            } else if (operation === 'set') {
                updateData.coins = amount;
            } else {
                console.error('CloudDatabaseManager: 不支持的金币操作类型');
                return false;
            }

            // 🔧 修改：使用_openid更新数据库
            const updateResult = await this._db.collection(this.COLLECTION_NAME)
                .where({
                    _openid: wxContext.openid
                })
                .update({
                    data: updateData
                });

            if (updateResult.stats.updated === 1) {
                // 计算新的金币数量（用于日志）
                const newCoins = operation === 'add' ? currentUser.coins + amount : amount;
                console.log(`CloudDatabaseManager: 成功更新金币 ${operation} ${amount}，新余额: ${newCoins}`);
                return true;
            } else {
                console.error('CloudDatabaseManager: 更新金币失败');
                return false;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 更新金币异常', error);
            return false;
        }
    }

    /**
     * 获取当前玩家数据（本地数据库操作）
     */
    public async getMyPlayerData(): Promise<CloudPlayerData | null> {
        return await this.getCurrentUserDataInternal();
    }

    /**
     * 获取玩家排名（本地数据库操作）
     */
    public async getPlayerRank(gameMode: GameMode): Promise<{ rank: number; score: number } | null> {
        if (!this.checkInitialized()) {
            return null;
        }

        try {
            // 开发模式下返回模拟排名
            const wxContext = await this.getWxContext();
            if (wxContext && wxContext.openid && wxContext.openid.startsWith('dev_openid_')) {
                console.log('CloudDatabaseManager: 开发模式，返回模拟排名');
                return { rank: 1, score: 100 };
            }

            // 获取当前用户数据
            const currentUser = await this.getCurrentUserDataInternal();
            if (!currentUser) {
                console.error('CloudDatabaseManager: 未找到当前用户数据');
                return null;
            }

            // 获取当前用户在该关卡的最高分
            const userScores = currentUser.topScores[gameMode] || [];
            const userMaxScore = userScores.length > 0 ? Math.max(...userScores) : 0;

            // 查询比当前用户分数更高的玩家数量
            const result = await this._db.collection(this.COLLECTION_NAME)
                .where({
                    [`topScores.${gameMode}`]: this._db.command.elemMatch(this._db.command.gt(userMaxScore))
                })
                .count();

            if (result.errMsg === 'collection.count:ok') {
                const rank = result.total + 1; // 排名 = 比自己高的人数 + 1
                console.log(`CloudDatabaseManager: 玩家在${gameMode}的排名: ${rank}，分数: ${userMaxScore}`);
                return {
                    rank: rank,
                    score: userMaxScore
                };
            } else {
                console.error('CloudDatabaseManager: 查询排名失败', result);
                return null;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 获取排名异常', error);
            return null;
        }
    }

    /**
     * 更新玩家资料（本地数据库操作）
     */
    public async updatePlayerProfile(nickname?: string, avatarUrl?: string): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 开发模式下跳过
            const wxContext = await this.getWxContext();
            if (wxContext && wxContext.openid && wxContext.openid.startsWith('dev_openid_')) {
                console.log('CloudDatabaseManager: 开发模式，跳过资料更新');
                return true;
            }

            // 获取当前用户数据
            const currentUser = await this.getCurrentUserDataInternal();
            if (!currentUser) {
                console.error('CloudDatabaseManager: 未找到当前用户数据');
                return false;
            }

            const updateData: any = {
                updatedAt: new Date()
            };

            if (nickname) updateData.nickname = nickname;
            if (avatarUrl) updateData.avatarUrl = avatarUrl;

            if (Object.keys(updateData).length === 1) {
                console.log('CloudDatabaseManager: 没有需要更新的资料');
                return true;
            }

            // 🔧 修改：使用_openid更新数据库
            const updateResult = await this._db.collection(this.COLLECTION_NAME)
                .where({
                    _openid: wxContext.openid
                })
                .update({
                    data: updateData
                });

            if (updateResult.stats.updated === 1) {
                console.log('CloudDatabaseManager: 成功更新玩家资料');
                return true;
            } else {
                console.error('CloudDatabaseManager: 更新玩家资料失败');
                return false;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 更新玩家资料异常', error);
            return false;
        }
    }

    /**
     * 创建新玩家数据（本地数据库操作）
     */
    public async createPlayerData(nickname: string, avatarUrl: string = ''): Promise<boolean> {
        if (!this.checkInitialized()) {
            return false;
        }

        try {
            // 开发模式下跳过
            const wxContext = await this.getWxContext();
            if (wxContext && wxContext.openid && wxContext.openid.startsWith('dev_openid_')) {
                console.log('CloudDatabaseManager: 开发模式，跳过玩家数据创建');
                return true;
            }

            // 检查用户是否已存在
            const existingUser = await this.getCurrentUserDataInternal();
            if (existingUser) {
                console.log(`CloudDatabaseManager: 用户数据已存在，无需创建 - 昵称: ${existingUser.nickname}, ID: ${existingUser._id}`);
                return true;
            }

            console.log(`CloudDatabaseManager: 开始创建新用户数据 - 昵称: ${nickname}`);

            // 生成邀请码
            const inviteCode = this.generateInviteCode();

            // 创建新用户数据
            const playerData: Partial<CloudPlayerData> = {
                nickname: nickname,
                avatarUrl: avatarUrl,
                coins: 1000, // 初始金币
                inviteCode: inviteCode,
                topScores: {
                    [GameMode.NORMAL_EASY]: [],
                    [GameMode.NORMAL_STANDARD]: [],
                    [GameMode.NORMAL_HARD]: [],
                    [GameMode.CHALLENGE_WIND]: [],
                    [GameMode.CHALLENGE_FOG]: [],
                    [GameMode.CHALLENGE_SNOW]: []
                }
            };

            console.log('CloudDatabaseManager: 准备上传用户数据到云数据库...');
            const result = await this._db.collection(this.COLLECTION_NAME).add({
                data: playerData
            });

            console.log(`CloudDatabaseManager: 创建用户数据结果 - errMsg: ${result.errMsg}, _id: ${result._id}`);

            if (result.errMsg === 'collection.add:ok') {
                console.log(`CloudDatabaseManager: 成功创建玩家数据，ID: ${result._id}, 邀请码: ${inviteCode}`);
                return true;
            } else {
                console.error('CloudDatabaseManager: 创建玩家数据失败', result);
                return false;
            }
        } catch (error) {
            console.error('CloudDatabaseManager: 创建玩家数据异常', error);
            return false;
        }
    }
}
