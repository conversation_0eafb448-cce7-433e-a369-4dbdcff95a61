import { _decorator, Component, Node } from 'cc';
import { GameData, GameMode } from './GameData';
import { GameDifficulty } from './GameDifficulty';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';

const { ccclass, property } = _decorator;

/**
 * 游戏模式修复测试脚本
 * 用于验证分数记录到正确游戏模式的修复是否有效
 */
@ccclass('GameModeFixTest')
export class GameModeFixTest extends Component {

    onLoad() {
        console.log("=== 游戏模式修复测试开始 ===");
        this.runTests();
    }

    private runTests() {
        // 清除所有记录，重新开始测试
        GameData.clearAllGameRecords();
        
        console.log("\n--- 测试1: 轻松模式分数记录 ---");
        this.testEasyMode();
        
        console.log("\n--- 测试2: 困难模式分数记录 ---");
        this.testHardMode();
        
        console.log("\n--- 测试3: 挑战模式分数记录 ---");
        this.testChallengeMode();
        
        console.log("\n--- 测试4: 验证所有模式的记录 ---");
        this.verifyAllRecords();
        
        console.log("\n=== 游戏模式修复测试完成 ===");
    }

    /**
     * 测试轻松模式
     */
    private testEasyMode() {
        // 设置轻松难度
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_EASY);
        ChallengeMode.clearMode();
        
        // 确定游戏模式
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        const gameMode = GameData.determineGameMode(difficulty, challengeMode);
        
        console.log(`设置: 难度=${difficulty}, 挑战=${challengeMode}, 模式=${gameMode}(${GameData.getGameModeName(gameMode)})`);
        
        // 设置当前游戏模式
        GameData.setCurrentGameMode(gameMode);
        
        // 模拟游戏分数
        GameData.resetScore();
        GameData.addScore(15); // 轻松模式得15分
        
        // 保存分数
        GameData.saveScore(gameMode);
        
        // 验证记录
        const bestScore = GameData.getBestScore(GameMode.NORMAL_EASY);
        const topScores = GameData.getTopScores(GameMode.NORMAL_EASY);
        
        console.log(`轻松模式记录: 最高分=${bestScore}, 前三分=${JSON.stringify(topScores)}`);
        
        // 验证其他模式没有被影响
        const standardBest = GameData.getBestScore(GameMode.NORMAL_STANDARD);
        console.log(`标准模式最高分（应该是0）: ${standardBest}`);
    }

    /**
     * 测试困难模式
     */
    private testHardMode() {
        // 设置困难难度
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_HARD);
        ChallengeMode.clearMode();
        
        // 确定游戏模式
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        const gameMode = GameData.determineGameMode(difficulty, challengeMode);
        
        console.log(`设置: 难度=${difficulty}, 挑战=${challengeMode}, 模式=${gameMode}(${GameData.getGameModeName(gameMode)})`);
        
        // 设置当前游戏模式
        GameData.setCurrentGameMode(gameMode);
        
        // 模拟游戏分数
        GameData.resetScore();
        GameData.addScore(25); // 困难模式得25分
        
        // 保存分数
        GameData.saveScore(gameMode);
        
        // 验证记录
        const bestScore = GameData.getBestScore(GameMode.NORMAL_HARD);
        const topScores = GameData.getTopScores(GameMode.NORMAL_HARD);
        
        console.log(`困难模式记录: 最高分=${bestScore}, 前三分=${JSON.stringify(topScores)}`);
        
        // 验证其他模式没有被影响
        const easyBest = GameData.getBestScore(GameMode.NORMAL_EASY);
        const standardBest = GameData.getBestScore(GameMode.NORMAL_STANDARD);
        console.log(`轻松模式最高分（应该是15）: ${easyBest}`);
        console.log(`标准模式最高分（应该是0）: ${standardBest}`);
    }

    /**
     * 测试挑战模式
     */
    private testChallengeMode() {
        // 设置大风吹挑战模式
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_HARD);
        ChallengeMode.setMode(ChallengeModeType.WIND);
        
        // 确定游戏模式
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        const gameMode = GameData.determineGameMode(difficulty, challengeMode);
        
        console.log(`设置: 难度=${difficulty}, 挑战=${challengeMode}, 模式=${gameMode}(${GameData.getGameModeName(gameMode)})`);
        
        // 设置当前游戏模式
        GameData.setCurrentGameMode(gameMode);
        
        // 模拟游戏分数
        GameData.resetScore();
        GameData.addScore(35); // 大风吹模式得35分
        
        // 保存分数
        GameData.saveScore(gameMode);
        
        // 验证记录
        const bestScore = GameData.getBestScore(GameMode.CHALLENGE_WIND);
        const topScores = GameData.getTopScores(GameMode.CHALLENGE_WIND);
        
        console.log(`大风吹模式记录: 最高分=${bestScore}, 前三分=${JSON.stringify(topScores)}`);
        
        // 验证其他模式没有被影响
        const easyBest = GameData.getBestScore(GameMode.NORMAL_EASY);
        const hardBest = GameData.getBestScore(GameMode.NORMAL_HARD);
        console.log(`轻松模式最高分（应该是15）: ${easyBest}`);
        console.log(`困难模式最高分（应该是25）: ${hardBest}`);
    }

    /**
     * 验证所有模式的记录
     */
    private verifyAllRecords() {
        console.log("所有游戏模式的记录:");
        
        const modes = [
            { mode: GameMode.NORMAL_EASY, expected: 15 },
            { mode: GameMode.NORMAL_STANDARD, expected: 0 },
            { mode: GameMode.NORMAL_HARD, expected: 25 },
            { mode: GameMode.CHALLENGE_WIND, expected: 35 },
            { mode: GameMode.CHALLENGE_FOG, expected: 0 },
            { mode: GameMode.CHALLENGE_SNOW, expected: 0 }
        ];
        
        let allCorrect = true;
        
        for (const { mode, expected } of modes) {
            const actual = GameData.getBestScore(mode);
            const modeName = GameData.getGameModeName(mode);
            const isCorrect = actual === expected;
            
            console.log(`${modeName}: 期望=${expected}, 实际=${actual} ${isCorrect ? '✅' : '❌'}`);
            
            if (!isCorrect) {
                allCorrect = false;
            }
        }
        
        console.log(`\n测试结果: ${allCorrect ? '✅ 所有测试通过！' : '❌ 存在错误！'}`);
        
        // 输出完整记录用于调试
        console.log("\n完整记录:");
        GameData.printAllGameRecords();
    }

    /**
     * 模拟GameOverUI的逻辑测试
     */
    public testGameOverUILogic() {
        console.log("\n--- 测试GameOverUI逻辑 ---");
        
        // 模拟用户选择轻松模式
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_EASY);
        ChallengeMode.clearMode();
        
        // 模拟GameOverUI.show()中的逻辑
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();
        const currentMode = GameData.determineGameMode(difficulty, challengeMode);
        
        // 确保GameData中的当前模式是正确的
        GameData.setCurrentGameMode(currentMode);
        
        console.log(`GameOverUI逻辑测试: 难度=${difficulty}, 挑战=${challengeMode}, 模式=${currentMode}(${GameData.getGameModeName(currentMode)})`);
        
        // 模拟分数保存
        GameData.resetScore();
        GameData.addScore(20);
        GameData.saveScore(currentMode);
        
        // 验证保存到了正确的模式
        const savedScore = GameData.getBestScore(GameMode.NORMAL_EASY);
        console.log(`保存的分数: ${savedScore} (应该是20)`);
        
        return savedScore === 20;
    }
}
