import { _decorator, Component, Node, Button, ScrollView, Prefab, instantiate, Label, Sprite, SpriteFrame, Color, UITransform, Layout, EventTouch, Vec3, ScrollBar } from 'cc';
import { GameMode } from '../GameData';
import { WeChatFriendsData, RankItem } from '../Data/WeChatFriendsData';
import { CustomScrollBar } from './CustomScrollBar';
import { GameDataManager } from '../Data/GameDataManager';
const { ccclass, property } = _decorator;

/**
 * 全服排行榜UI管理器
 */
@ccclass('GlobalRankUI')
export class GlobalRankUI extends Component {
    
    // 关卡按钮容器
    @property(Node)
    levelButtonsContainer: Node = null;

    // 关卡按钮
    @property(Button)
    easyBtn: Button = null;

    @property(Button)
    normalBtn: Button = null;

    @property(Button)
    hardBtn: Button = null;

    @property(Button)
    windBtn: Button = null;

    @property(Button)
    fogBtn: Button = null;

    @property(Button)
    snowBtn: Button = null;

    // 排行榜滚动视图
    @property(ScrollView)
    globalRankScrollView: ScrollView = null;

    // 排行榜条目预制体
    @property(Prefab)
    globalRankItemPrefab: Prefab = null;

    // 默认头像
    @property(SpriteFrame)
    defaultAvatarFrame: SpriteFrame = null;

    // 自定义滚动条（可选，如果设置了就使用自定义滚动条）
    @property(CustomScrollBar)
    customScrollBar: CustomScrollBar = null;

    // 当前选中的关卡
    private currentGameMode: GameMode = GameMode.NORMAL_EASY;

    // 按钮颜色配置
    private readonly SELECTED_COLOR = new Color(100, 200, 100, 255);   // 选中状态：绿色
    private readonly NORMAL_COLOR = new Color(255, 255, 255, 255);     // 普通状态：白色

    onLoad() {
        console.log("GlobalRankUI: 初始化全服排行榜UI");
        this.setupButtonEvents();

        // 根据是否有自定义滚动条来决定使用哪种滚动方式
        if (this.customScrollBar) {
            console.log("GlobalRankUI: 使用自定义滚动条");
            this.setupCustomScrollBar();
        } else {
            console.log("GlobalRankUI: 未设置自定义滚动条，跳过滚动条拖拽功能");
        }
    }

    onDestroy() {
        // 清理自定义滚动条
        if (this.customScrollBar) {
            // CustomScrollBar组件会自动清理自己的事件监听
        }
    }

    start() {
        // 进入排行榜时手动检查云端数据变化
        this.checkCloudDataChanges();

        // 默认显示轻松关卡的排行榜
        this.selectLevel(GameMode.NORMAL_EASY);
    }

    /**
     * 🔧 简化：移除进入排行榜时的云端数据检查
     * 按照用户建议，只在游戏启动时同步一次
     */
    private async checkCloudDataChanges(): Promise<void> {
        console.log('GlobalRankUI: 已简化同步策略，不再在进入排行榜时检查云端数据变化');
        // 不再调用 manualSyncCloudChanges
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        // 注意：按钮事件现在通过编辑器配置，这里不需要代码绑定
    }

    /**
     * 设置自定义滚动条
     */
    private setupCustomScrollBar(): void {
        if (!this.customScrollBar || !this.globalRankScrollView) {
            console.warn("GlobalRankUI: CustomScrollBar或ScrollView未设置");
            return;
        }

        // 设置CustomScrollBar的目标ScrollView
        this.customScrollBar.targetScrollView = this.globalRankScrollView;
        console.log("GlobalRankUI: 自定义滚动条已配置");
    }

    /**
     * 选择关卡并更新排行榜
     */
    public selectLevel(event?: any, customEventData?: any): void {
        let gameMode: GameMode;

        console.log(`GlobalRankUI: 收到参数 event=${event}, customEventData=${customEventData}, type=${typeof customEventData}`);

        // 处理从编辑器按钮事件传来的参数
        if (customEventData !== undefined && customEventData !== null) {
            // 尝试多种方式解析参数
            let modeValue: number;

            if (typeof customEventData === 'string') {
                modeValue = parseInt(customEventData);
            } else if (typeof customEventData === 'number') {
                modeValue = customEventData;
            } else {
                // 尝试转换为字符串再解析
                modeValue = parseInt(String(customEventData));
            }

            if (!isNaN(modeValue) && modeValue >= 0 && modeValue <= 5) {
                gameMode = modeValue as GameMode;
            } else {
                gameMode = GameMode.NORMAL_EASY;
                console.warn(`GlobalRankUI: 无效的CustomEventData ${customEventData}，使用默认值`);
            }
        } else if (typeof event === 'number') {
            // 直接传入数字
            gameMode = event as GameMode;
        } else {
            // 默认值
            gameMode = GameMode.NORMAL_EASY;
            console.warn(`GlobalRankUI: 无效的参数，使用默认值`);
        }

        console.log(`GlobalRankUI: 选择关卡 ${gameMode} (${this.getGameModeName(gameMode)})`);

        this.currentGameMode = gameMode;
        this.updateButtonStates();
        this.updateRankingList();
    }

    /**
     * 获取游戏模式名称
     */
    private getGameModeName(mode: GameMode): string {
        const names = {
            [GameMode.NORMAL_EASY]: "轻松",
            [GameMode.NORMAL_STANDARD]: "标准",
            [GameMode.NORMAL_HARD]: "困难",
            [GameMode.CHALLENGE_WIND]: "大风吹",
            [GameMode.CHALLENGE_FOG]: "大雾起",
            [GameMode.CHALLENGE_SNOW]: "大雪飘"
        };
        return names[mode] || "未知";
    }

    /**
     * 更新按钮状态
     */
    private updateButtonStates(): void {
        const buttons = [
            { btn: this.easyBtn, mode: GameMode.NORMAL_EASY },
            { btn: this.normalBtn, mode: GameMode.NORMAL_STANDARD },
            { btn: this.hardBtn, mode: GameMode.NORMAL_HARD },
            { btn: this.windBtn, mode: GameMode.CHALLENGE_WIND },
            { btn: this.fogBtn, mode: GameMode.CHALLENGE_FOG },
            { btn: this.snowBtn, mode: GameMode.CHALLENGE_SNOW }
        ];

        buttons.forEach(({ btn, mode }) => {
            if (btn) {
                const sprite = btn.getComponent(Sprite);
                if (sprite) {
                    sprite.color = mode === this.currentGameMode ? this.SELECTED_COLOR : this.NORMAL_COLOR;
                }
            }
        });
    }

    /**
     * 更新排行榜列表
     */
    private async updateRankingList(): Promise<void> {
        if (!this.globalRankScrollView || !this.globalRankItemPrefab) {
            console.error("GlobalRankUI: ScrollView或ItemPrefab未设置");
            return;
        }

        console.log("GlobalRankUI: 开始更新全服排行榜列表");

        try {
            // 先刷新玩家分数，确保排行榜数据是最新的
            WeChatFriendsData.instance.refreshPlayerScores();

            // 清空现有列表
            const content = this.globalRankScrollView.content;
            if (content) {
                content.removeAllChildren();
            }

            // 获取全服排行榜数据（现在是异步的）
            const rankingData = await WeChatFriendsData.instance.getGlobalRanking(this.currentGameMode, 100);

            console.log(`GlobalRankUI: 获取到 ${rankingData.length} 条全服排行榜数据`);

            // 创建排行榜条目
            rankingData.forEach((rankItem) => {
                this.createRankItem(rankItem);
            });

            // 调整content高度以适应所有条目
            this.adjustContentHeight(rankingData.length);
        } catch (error) {
            console.error("GlobalRankUI: 更新全服排行榜失败", error);
        }

        // 重置滚动位置到顶部
        this.scheduleOnce(() => {
            if (this.globalRankScrollView) {
                this.globalRankScrollView.scrollToTop(0.1);
            }

            // 如果使用自定义滚动条，更新把手大小和位置
            if (this.customScrollBar) {
                this.customScrollBar.updateHandleSize();
                // 延迟一帧确保ScrollView已经更新
                this.scheduleOnce(() => {
                    if (this.customScrollBar) {
                        // 强制更新滚动条位置
                        this.customScrollBar.updateScrollBar();
                        console.log("GlobalRankUI: 自定义滚动条已更新");
                    }
                }, 0.05);
            }
        }, 0.1);
    }

    /**
     * 创建排行榜条目
     */
    private createRankItem(rankItem: RankItem): void {
        if (!this.globalRankItemPrefab || !this.globalRankScrollView) {
            return;
        }

        const itemNode = instantiate(this.globalRankItemPrefab);
        if (!itemNode) {
            console.error("GlobalRankUI: 无法实例化排行榜条目预制体");
            return;
        }

        // 设置排行榜条目数据
        this.setupRankItemData(itemNode, rankItem);

        // 添加到滚动视图内容
        this.globalRankScrollView.content.addChild(itemNode);
    }

    /**
     * 设置排行榜条目数据
     */
    private setupRankItemData(itemNode: Node, rankItem: RankItem): void {
        // 设置排名
        const rankLabel = itemNode.getChildByName("RankLabel")?.getComponent(Label);
        if (rankLabel) {
            rankLabel.string = rankItem.rank.toString();
            
            // 前三名使用特殊颜色
            if (rankItem.rank === 1) {
                rankLabel.color = new Color(255, 215, 0, 255); // 金色
            } else if (rankItem.rank === 2) {
                rankLabel.color = new Color(192, 192, 192, 255); // 银色
            } else if (rankItem.rank === 3) {
                rankLabel.color = new Color(205, 127, 50, 255); // 铜色
            } else {
                rankLabel.color = new Color(255, 255, 255, 255); // 白色
            }
        }

        // 设置头像（这里使用默认头像，实际项目中应该加载微信头像）
        const avatarSprite = itemNode.getChildByName("AvatarSprite")?.getComponent(Sprite);
        if (avatarSprite && this.defaultAvatarFrame) {
            avatarSprite.spriteFrame = this.defaultAvatarFrame;
        }

        // 设置昵称
        const nameLabel = itemNode.getChildByName("NameLabel")?.getComponent(Label);
        if (nameLabel) {
            nameLabel.string = rankItem.friend.nickname;
            
            // 如果是自己，使用特殊颜色
            if (rankItem.friend.id === "self") {
                nameLabel.color = new Color(100, 255, 100, 255); // 绿色
            } else {
                nameLabel.color = new Color(255, 255, 255, 255); // 白色
            }
        }

        // 设置分数
        const scoreLabel = itemNode.getChildByName("ScoreLabel")?.getComponent(Label);
        if (scoreLabel) {
            scoreLabel.string = rankItem.score.toString();
        }
    }

    /**
     * 刷新排行榜数据
     */
    public async refreshRanking(): Promise<void> {
        console.log("GlobalRankUI: 刷新全服排行榜数据");

        try {
            await WeChatFriendsData.instance.refreshFriendsData();
            await this.updateRankingList();
            console.log("GlobalRankUI: 全服排行榜数据刷新完成");
        } catch (error) {
            console.error("GlobalRankUI: 刷新全服排行榜数据失败", error);
        }
    }

    /**
     * 获取当前选中的游戏模式
     */
    public getCurrentGameMode(): GameMode {
        return this.currentGameMode;
    }

    /**
     * 调整ScrollView内容高度
     */
    private adjustContentHeight(itemCount: number): void {
        if (!this.globalRankScrollView || !this.globalRankScrollView.content) {
            return;
        }

        const content = this.globalRankScrollView.content;

        // 等待一帧，确保所有条目都已创建完成
        this.scheduleOnce(() => {
            this.calculateAndSetContentHeight(content, itemCount);
        }, 0);
    }

    /**
     * 计算并设置实际的content高度
     */
    private calculateAndSetContentHeight(content: Node, itemCount: number): void {
        if (content.children.length === 0) {
            console.warn("GlobalRankUI: 没有子节点，无法计算高度");
            return;
        }

        // 获取第一个条目的实际高度
        const firstItem = content.children[0];
        const firstItemTransform = firstItem.getComponent(UITransform);
        const actualItemHeight = firstItemTransform?.height || 60;

        // 检查Layout组件的间距设置
        const layout = content.getComponent(Layout);
        const actualSpacing = layout?.spacingY || 0;

        // 计算实际需要的总高度（添加少量缓冲）
        const totalHeight = itemCount * actualItemHeight + (itemCount - 1) * actualSpacing + 20; // 添加20像素缓冲

        // 获取ScrollView的高度
        const scrollViewTransform = this.globalRankScrollView.node.getComponent(UITransform);
        const scrollViewHeight = scrollViewTransform?.height || 400;

        // 设置content高度：取实际内容高度和ScrollView高度的较大值
        const finalHeight = Math.max(totalHeight, scrollViewHeight);

        // 设置content的高度
        const contentTransform = content.getComponent(UITransform);
        if (contentTransform) {
            contentTransform.height = finalHeight;
            console.log(`GlobalRankUI: 实际条目高度=${actualItemHeight}, 间距=${actualSpacing}, 总高度=${totalHeight}, 最终高度=${finalHeight}`);
        }
    }

    /**
     * 外部调用：显示指定关卡的排行榜
     */
    public showLevelRanking(mode: GameMode): void {
        this.selectLevel(mode);
    }
}
