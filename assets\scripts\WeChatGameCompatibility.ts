import { _decorator, Component } from 'cc';
const { ccclass, property } = _decorator;

// 微信小游戏API类型声明
declare global {
    interface Window {
        wx?: {
            setClipboardData: (options: { data: string; success?: () => void; fail?: (err: any) => void; }) => void;
            getStorageSync: (key: string) => any;
            setStorageSync: (key: string, data: any) => void;
            removeStorageSync: (key: string) => void;
            clearStorageSync: () => void;
            getStorageInfoSync: () => { keys: string[]; currentSize: number; limitSize: number; };
        };
    }
    const wx: {
        setClipboardData: (options: { data: string; success?: () => void; fail?: (err: any) => void; }) => void;
        getStorageSync: (key: string) => any;
        setStorageSync: (key: string, data: any) => void;
        removeStorageSync: (key: string) => void;
        clearStorageSync: () => void;
        getStorageInfoSync: () => { keys: string[]; currentSize: number; limitSize: number; };
    } | undefined;
}

/**
 * 微信小游戏兼容性检查和修复工具
 */
@ccclass('WeChatGameCompatibility')
export class WeChatGameCompatibility extends Component {

    start() {
        // 检查并修复微信小游戏环境的兼容性问题
        this.checkAndFixCompatibility();
        
        // 暴露调试方法
        this.exposeDebugMethods();
    }

    /**
     * 检查并修复兼容性问题
     */
    private checkAndFixCompatibility() {
        console.log("=== 微信小游戏兼容性检查 ===");
        
        // 1. 检查微信小游戏环境
        this.checkWeChatGameEnvironment();
        
        // 2. 检查localStorage兼容性
        this.checkLocalStorageCompatibility();
        
        // 3. 检查剪贴板API兼容性
        this.checkClipboardCompatibility();
        
        // 4. 修复已知的兼容性问题
        this.fixKnownIssues();
        
        console.log("=== 兼容性检查完成 ===");
    }

    /**
     * 检查微信小游戏环境
     */
    private checkWeChatGameEnvironment() {
        const isWeChatGame = typeof wx !== 'undefined';
        console.log(`微信小游戏环境: ${isWeChatGame}`);
        
        if (isWeChatGame) {
            console.log("检测到微信小游戏环境，启用兼容性修复");
            
            // 检查微信API可用性
            const apis = {
                setClipboardData: typeof wx.setClipboardData === 'function',
                getStorageSync: typeof wx.getStorageSync === 'function',
                setStorageSync: typeof wx.setStorageSync === 'function'
            };
            
            console.log("微信API可用性:", apis);
        } else {
            console.log("非微信小游戏环境，使用标准Web API");
        }
    }

    /**
     * 检查localStorage兼容性
     */
    private checkLocalStorageCompatibility() {
        console.log("检查localStorage兼容性...");
        
        try {
            // 测试基本读写
            const testKey = '__compatibility_test__';
            const testValue = 'test_value_123';
            
            localStorage.setItem(testKey, testValue);
            const retrievedValue = localStorage.getItem(testKey);
            localStorage.removeItem(testKey);
            
            if (retrievedValue === testValue) {
                console.log("✅ localStorage基本功能正常");
            } else {
                console.error("❌ localStorage读写异常");
                this.fixLocalStorageIssues();
            }
            
            // 测试数字存储和解析
            const numberTestKey = '__number_test__';
            const numberTestValue = 42;
            
            localStorage.setItem(numberTestKey, numberTestValue.toString());
            const retrievedNumber = localStorage.getItem(numberTestKey);
            const parsedNumber = parseInt(retrievedNumber || "");
            localStorage.removeItem(numberTestKey);
            
            if (!isNaN(parsedNumber) && parsedNumber === numberTestValue) {
                console.log("✅ localStorage数字存储正常");
            } else {
                console.error("❌ localStorage数字解析异常");
                console.error(`期望: ${numberTestValue}, 实际: ${parsedNumber}, 原始值: "${retrievedNumber}"`);
            }
            
        } catch (error) {
            console.error("❌ localStorage测试失败:", error);
            this.fixLocalStorageIssues();
        }
    }

    /**
     * 检查剪贴板API兼容性
     */
    private checkClipboardCompatibility() {
        console.log("检查剪贴板API兼容性...");
        
        const compatibility = {
            modernClipboard: !!(navigator.clipboard && window.isSecureContext),
            execCommand: typeof document.execCommand === 'function',
            wechatClipboard: typeof wx !== 'undefined' && typeof wx.setClipboardData === 'function',
            textAreaSelect: true // 默认假设支持
        };
        
        // 测试textArea.select方法
        try {
            const testArea = document.createElement('textarea');
            testArea.style.position = 'fixed';
            testArea.style.left = '-9999px';
            document.body.appendChild(testArea);
            
            if (typeof testArea.select !== 'function') {
                compatibility.textAreaSelect = false;
                console.warn("⚠️ textArea.select方法不可用");
            }
            
            document.body.removeChild(testArea);
        } catch (error) {
            compatibility.textAreaSelect = false;
            console.warn("⚠️ textArea测试失败:", error);
        }
        
        console.log("剪贴板API兼容性:", compatibility);
        
        if (!compatibility.modernClipboard && !compatibility.execCommand && !compatibility.wechatClipboard) {
            console.error("❌ 所有剪贴板API都不可用");
        }
    }

    /**
     * 修复已知的兼容性问题
     */
    private fixKnownIssues() {
        console.log("修复已知兼容性问题...");
        
        // 1. 修复parseInt可能返回NaN的问题
        this.fixParseIntIssues();
        
        // 2. 为微信小游戏环境添加polyfill
        this.addWeChatGamePolyfills();
        
        // 3. 修复textArea.select问题
        this.fixTextAreaSelectIssue();
    }

    /**
     * 修复parseInt相关问题
     */
    private fixParseIntIssues() {
        // 检查现有的localStorage数据是否有问题
        const energyValue = localStorage.getItem("PlayerEnergy");
        const recoverTime = localStorage.getItem("EnergyRecoverTime");
        
        if (energyValue !== null) {
            const parsed = parseInt(energyValue);
            if (isNaN(parsed)) {
                console.warn(`修复无效的体力值: "${energyValue}" -> 100`);
                localStorage.setItem("PlayerEnergy", "100");
            }
        }
        
        if (recoverTime !== null) {
            const parsed = parseInt(recoverTime);
            if (isNaN(parsed)) {
                console.warn(`修复无效的恢复时间: "${recoverTime}" -> 0`);
                localStorage.setItem("EnergyRecoverTime", "0");
            }
        }
    }

    /**
     * 为微信小游戏环境添加polyfill
     */
    private addWeChatGamePolyfills() {
        if (typeof wx !== 'undefined') {
            console.log("为微信小游戏环境添加polyfill...");
            
            // 如果需要，可以在这里添加更多的polyfill
            // 例如：为不支持的Web API提供替代实现
        }
    }

    /**
     * 修复textArea.select问题
     */
    private fixTextAreaSelectIssue() {
        // 为HTMLTextAreaElement添加select方法的polyfill（如果不存在）
        if (typeof HTMLTextAreaElement !== 'undefined') {
            const proto = HTMLTextAreaElement.prototype;
            if (typeof proto.select !== 'function') {
                console.log("添加textArea.select polyfill");
                proto.select = function() {
                    try {
                        if (typeof this.setSelectionRange === 'function') {
                            this.setSelectionRange(0, this.value.length);
                        } else {
                            // 如果setSelectionRange也不可用，尝试其他方法
                            this.focus();
                        }
                    } catch (error) {
                        console.warn("textArea.select polyfill执行失败:", error);
                    }
                };
            }
        }
    }

    /**
     * 修复localStorage问题
     */
    private fixLocalStorageIssues() {
        console.log("尝试修复localStorage问题...");
        
        // 如果在微信小游戏环境中，尝试使用微信的存储API
        if (typeof wx !== 'undefined' && wx.getStorageSync && wx.setStorageSync) {
            console.log("使用微信存储API作为localStorage的替代");
            
            // 创建localStorage的替代实现
            const originalLocalStorage = window.localStorage;
            
            window.localStorage = {
                getItem: (key: string) => {
                    try {
                        return wx.getStorageSync(key) || null;
                    } catch (error) {
                        console.error("微信getStorageSync失败:", error);
                        return originalLocalStorage.getItem(key);
                    }
                },
                setItem: (key: string, value: string) => {
                    try {
                        wx.setStorageSync(key, value);
                    } catch (error) {
                        console.error("微信setStorageSync失败:", error);
                        originalLocalStorage.setItem(key, value);
                    }
                },
                removeItem: (key: string) => {
                    try {
                        wx.removeStorageSync(key);
                    } catch (error) {
                        console.error("微信removeStorageSync失败:", error);
                        originalLocalStorage.removeItem(key);
                    }
                },
                clear: () => {
                    try {
                        wx.clearStorageSync();
                    } catch (error) {
                        console.error("微信clearStorageSync失败:", error);
                        originalLocalStorage.clear();
                    }
                },
                get length() {
                    try {
                        const info = wx.getStorageInfoSync();
                        return info.keys.length;
                    } catch (error) {
                        return originalLocalStorage.length;
                    }
                },
                key: (index: number) => {
                    try {
                        const info = wx.getStorageInfoSync();
                        return info.keys[index] || null;
                    } catch (error) {
                        return originalLocalStorage.key(index);
                    }
                }
            } as Storage;
        }
    }

    /**
     * 暴露调试方法
     */
    private exposeDebugMethods() {
        if (typeof window !== 'undefined') {
            (window as any).wechatCompatibility = {
                checkCompatibility: () => this.checkAndFixCompatibility(),
                fixLocalStorage: () => this.fixLocalStorageIssues(),
                testClipboard: () => this.checkClipboardCompatibility()
            };
            
            console.log("微信兼容性调试方法已暴露到 window.wechatCompatibility");
        }
    }
}
