import { _decorator, Component, Node, UITransform, Widget, Button } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 排行榜透明遮罩组件
 * 简单的遮罩组件，仅用于阻止下层UI交互
 */
@ccclass('LeaderboardMask')
export class LeaderboardMask extends Component {

    onLoad() {
        // 确保遮罩覆盖整个屏幕
        this.setupMaskSize();

        // 确保有Button组件来阻止交互
        this.ensureButton();

        console.log("LeaderboardMask: 遮罩初始化完成");
    }

    /**
     * 确保有Button组件来阻止下层交互
     */
    private ensureButton() {
        let button = this.getComponent(Button);
        if (!button) {
            console.log("LeaderboardMask: 添加Button组件以阻止交互");
            button = this.addComponent(Button);
        }

        // 设置Button属性
        if (button) {
            button.interactable = true;
            button.transition = Button.Transition.NONE; // 无视觉反馈
            console.log("LeaderboardMask: Button组件配置完成");
        }
    }

    /**
     * 设置遮罩尺寸以覆盖整个屏幕
     */
    private setupMaskSize() {
        const uiTransform = this.getComponent(UITransform);
        if (uiTransform) {
            // 设置为全屏尺寸，确保覆盖所有可能的屏幕尺寸
            uiTransform.setContentSize(2560, 1440); // 使用更大的尺寸确保覆盖
        }

        // 如果有Widget组件，设置为全屏（可选）
        const widget = this.getComponent(Widget);
        if (widget) {
            widget.isAlignLeft = true;
            widget.isAlignRight = true;
            widget.isAlignTop = true;
            widget.isAlignBottom = true;
            widget.left = 0;
            widget.right = 0;
            widget.top = 0;
            widget.bottom = 0;
            console.log("LeaderboardMask: Widget组件已配置为全屏");
        } else {
            console.log("LeaderboardMask: 未找到Widget组件，使用固定尺寸");
        }
    }

    start() {
        // 遮罩初始化完成，仅用于阻止交互
        console.log("LeaderboardMask: 遮罩启动完成");
    }

    /**
     * 显示遮罩
     */
    public show() {
        this.node.active = true;
    }

    /**
     * 隐藏遮罩
     */
    public hide() {
        this.node.active = false;
    }
}
