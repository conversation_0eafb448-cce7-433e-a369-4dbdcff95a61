import { _decorator, Component, Node, director } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 广告空闲提示管理器
 * 用于显示"广告位空闲，敬请期待！"的UI提示
 * 参考项目中现有的金币不足提示实现模式
 */
@ccclass('AdIdleTipManager')
export class AdIdleTipManager extends Component {

    // 单例实例
    private static _instance: AdIdleTipManager = null;

    // 广告空闲提示节点
    @property(Node)
    adIdleTipSprite: Node = null;

    onLoad() {
        // 单例模式
        if (AdIdleTipManager._instance) {
            this.node.destroy();
            return;
        }

        AdIdleTipManager._instance = this;

        // 初始化提示状态
        this.initializeTipSprite();

        console.log("AdIdleTipManager 初始化完成");
    }

    onDestroy() {
        // 清理单例引用
        if (AdIdleTipManager._instance === this) {
            AdIdleTipManager._instance = null;
        }

        console.log("AdIdleTipManager: 组件已销毁");
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): AdIdleTipManager | null {
        if (!AdIdleTipManager._instance) {
            // 如果实例不存在，尝试在当前场景中查找
            AdIdleTipManager.findInstance();
        }
        return AdIdleTipManager._instance;
    }

    /**
     * 在当前场景中查找AdIdleTipManager实例
     */
    private static findInstance(): void {
        const scene = director.getScene();
        if (scene) {
            const existingManager = scene.getComponentInChildren(AdIdleTipManager);
            if (existingManager) {
                AdIdleTipManager._instance = existingManager;
                console.log("AdIdleTipManager: 找到现有实例");
            }
        }
    }

    /**
     * 初始化提示精灵状态
     */
    private initializeTipSprite(): void {
        if (this.adIdleTipSprite) {
            this.adIdleTipSprite.active = false;
        }
    }

    /**
     * 显示广告空闲提示
     * 参考BackgroundShopManager.showCoinLackSprite的实现
     */
    public showAdIdleTip(): void {
        console.log("AdIdleTipManager: 尝试显示广告空闲提示");
        console.log(`AdIdleTipManager: adIdleTipSprite节点状态: ${this.adIdleTipSprite ? '已设置' : '未设置'}`);

        if (this.adIdleTipSprite) {
            console.log(`AdIdleTipManager: adIdleTipSprite节点有效性: ${this.adIdleTipSprite.isValid}`);
            console.log(`AdIdleTipManager: adIdleTipSprite当前激活状态: ${this.adIdleTipSprite.active}`);

            this.adIdleTipSprite.active = true;
            console.log("AdIdleTipManager: 广告空闲提示已显示");

            // 1秒后自动隐藏
            this.scheduleOnce(() => {
                if (this.adIdleTipSprite && this.adIdleTipSprite.isValid) {
                    this.adIdleTipSprite.active = false;
                    console.log("AdIdleTipManager: 广告空闲提示已隐藏");
                }
            }, 1.0);
        } else {
            console.error("AdIdleTipManager: 广告空闲提示节点未设置！尝试查找替代方案");
            this.tryFindAdIdleTipSprite();
        }
    }

    /**
     * 手动隐藏广告空闲提示
     */
    public hideAdIdleTip(): void {
        if (this.adIdleTipSprite) {
            this.adIdleTipSprite.active = false;
            console.log("手动隐藏广告空闲提示");
        }
    }

    /**
     * 检查提示是否正在显示
     */
    public isShowing(): boolean {
        return this.adIdleTipSprite && this.adIdleTipSprite.active;
    }



    /**
     * 创建AdIdleTipManager实例（如果场景中没有）
     */
    public static createInstance(): AdIdleTipManager | null {
        if (AdIdleTipManager._instance) {
            return AdIdleTipManager._instance;
        }

        const scene = director.getScene();
        if (!scene) {
            console.error("AdIdleTipManager: 无法获取当前场景");
            return null;
        }

        // 创建一个新的AdIdleTipManager节点
        const tipManagerNode = new Node('AdIdleTipManager');
        const tipManager = tipManagerNode.addComponent(AdIdleTipManager);

        // 将节点添加到当前场景
        scene.addChild(tipManagerNode);
        console.log("AdIdleTipManager: 创建新实例并添加到场景");

        return tipManager;
    }

    /**
     * 静态方法：显示广告空闲提示
     * 方便其他组件调用
     */
    public static showTip(): void {
        let instance = AdIdleTipManager.getInstance();
        if (!instance) {
            instance = AdIdleTipManager.createInstance();
        }

        if (instance) {
            instance.showAdIdleTip();
        } else {
            console.error("AdIdleTipManager: 无法获取或创建实例");
        }
    }

    /**
     * 静态方法：隐藏广告空闲提示
     */
    public static hideTip(): void {
        const instance = AdIdleTipManager.getInstance();
        if (instance) {
            instance.hideAdIdleTip();
        }
    }

    /**
     * 尝试查找广告空闲提示节点（当节点引用丢失时使用）
     */
    private tryFindAdIdleTipSprite(): void {
        console.log("AdIdleTipManager: 尝试查找广告空闲提示节点");

        // 首先尝试在当前节点的子节点中查找
        if (this.node && this.node.children) {
            for (const child of this.node.children) {
                if (child.name.includes('AdIdleTip') || child.name.includes('adIdleTip')) {
                    console.log(`AdIdleTipManager: 在子节点中找到可能的广告提示节点: ${child.name}`);
                    this.adIdleTipSprite = child;
                    this.showAdIdleTip();
                    return;
                }
            }
        }

        // 如果在子节点中没找到，尝试在场景中查找
        const scene = director.getScene();
        if (scene) {
            const foundNode = this.findNodeByNameRecursive(scene, 'AdIdleTipSprite');
            if (foundNode) {
                console.log("AdIdleTipManager: 在场景中找到广告提示节点");
                this.adIdleTipSprite = foundNode;
                this.showAdIdleTip();
                return;
            }
        }

        // 如果都没找到，创建一个临时的提示
        console.warn("AdIdleTipManager: 无法找到广告提示节点，显示控制台提示");
        this.showConsoleAdIdleTip();
    }

    /**
     * 递归查找节点
     */
    private findNodeByNameRecursive(node: Node, targetName: string): Node | null {
        if (node.name === targetName) {
            return node;
        }

        for (const child of node.children) {
            const found = this.findNodeByNameRecursive(child, targetName);
            if (found) {
                return found;
            }
        }

        return null;
    }

    /**
     * 显示控制台广告空闲提示（备用方案）
     */
    private showConsoleAdIdleTip(): void {
        console.log("=== 广告位空闲，敬请期待！===");
        console.log("AdIdleTipManager: 由于UI节点未找到，使用控制台显示提示");

        // 可以在这里添加其他的提示方式，比如弹窗等
        if (typeof alert !== 'undefined') {
            // 在浏览器环境中显示弹窗
            setTimeout(() => {
                alert('广告位空闲，敬请期待！');
            }, 100);
        }
    }
}
