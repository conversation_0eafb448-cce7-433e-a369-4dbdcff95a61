// 云函数：用户登录
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

/**
 * 云函数入口函数
 * 获取用户openid
 */
exports.main = async (event, context) => {
  console.log('用户登录请求')
  
  try {
    // 获取微信调用上下文
    const wxContext = cloud.getWXContext()
    
    console.log('获取到用户openid:', wxContext.OPENID)
    
    return {
      success: true,
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID,
    }
    
  } catch (error) {
    console.error('登录时出错:', error)
    
    return {
      success: false,
      error: error.message || '登录失败',
      openid: null
    }
  }
}
