// 云函数：定时更新排行榜缓存
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 定时更新排行榜缓存云函数
 * 每5分钟自动执行，计算所有关卡的前100名并存储到缓存集合
 */
exports.main = async (event, context) => {
  console.log('开始定时更新排行榜缓存')
  
  try {
    const allModes = [0, 1, 2, 3, 4, 5] // 6个关卡
    const updateResults = []
    
    // 并行计算所有关卡的排行榜
    const promises = allModes.map(async (gameMode) => {
      return await updateSingleModeLeaderboard(gameMode)
    })
    
    const results = await Promise.all(promises)
    
    // 统计结果
    let successCount = 0
    let totalPlayers = 0
    
    results.forEach((result, index) => {
      if (result.success) {
        successCount++
        totalPlayers += result.playerCount
        updateResults.push({
          gameMode: allModes[index],
          playerCount: result.playerCount,
          success: true
        })
      } else {
        updateResults.push({
          gameMode: allModes[index],
          error: result.error,
          success: false
        })
      }
    })
    
    console.log(`排行榜缓存更新完成: ${successCount}/${allModes.length}个关卡成功，总计${totalPlayers}条数据`)
    
    return {
      success: true,
      message: `成功更新${successCount}个关卡的排行榜缓存`,
      results: updateResults,
      totalPlayers: totalPlayers,
      updateTime: new Date().toISOString()
    }
    
  } catch (error) {
    console.error('定时更新排行榜缓存失败:', error)
    return {
      success: false,
      error: error.message || '更新排行榜缓存失败'
    }
  }
}

/**
 * 更新单个关卡的排行榜缓存
 */
async function updateSingleModeLeaderboard(gameMode) {
  try {
    console.log(`开始计算关卡${gameMode}的排行榜`)
    
    // 聚合查询获取排行榜数据
    const result = await db.collection('players').aggregate()
      .addFields({
        // 计算该关卡的最高分
        maxScore: {
          $max: {
            $ifNull: [`$topScores.${gameMode}`, [0]]
          }
        }
      })
      .match({
        maxScore: _.gt(0) // 只包含有分数的玩家
      })
      .sort({
        maxScore: -1, // 按分数降序
        coins: -1,    // 分数相同时按金币降序
        _id: 1        // 最后按ID升序确保稳定排序
      })
      .limit(100) // 只取前100名
      .project({
        nickname: 1,
        avatarUrl: 1,
        maxScore: 1
      })
      .end()
    
    if (result.errMsg !== 'collection.aggregate:ok') {
      throw new Error(`聚合查询失败: ${result.errMsg}`)
    }
    
    // 添加排名信息，只保留排行榜显示必需的数据
    const players = result.list.map((player, index) => ({
      rank: index + 1,
      nickname: player.nickname,
      avatarUrl: player.avatarUrl,
      maxScore: player.maxScore
    }))
    
    console.log(`关卡${gameMode}计算完成，共${players.length}名玩家`)
    
    // 更新或插入缓存数据，只保留必要字段
    const cacheData = {
      gameMode: gameMode,
      players: players,
      updateTime: new Date()
    }
    
    // 更新现有记录（6条记录已预先创建，只需更新内容）
    const updateResult = await db.collection('leaderboard_cache')
      .where({
        gameMode: gameMode
      })
      .update({
        data: cacheData
      })

    if (updateResult.stats.updated > 0) {
      console.log(`关卡${gameMode}缓存数据已更新`)
    } else {
      console.warn(`关卡${gameMode}缓存记录不存在，请检查数据库`)
    }

    // 注释掉创建新记录的逻辑（6条记录已预先创建）
    // if (updateResult.stats.updated === 0) {
    //   await db.collection('leaderboard_cache').add({
    //     data: cacheData
    //   })
    //   console.log(`关卡${gameMode}缓存数据已插入`)
    // }
    
    return {
      success: true,
      playerCount: players.length
    }
    
  } catch (error) {
    console.error(`更新关卡${gameMode}排行榜缓存失败:`, error)
    return {
      success: false,
      error: error.message
    }
  }
}
