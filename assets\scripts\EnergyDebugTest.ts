import { _decorator, Component, Node } from 'cc';
import { EnergyManager } from './EnergyManager';
import { GameManager } from './GameManager';
const { ccclass, property } = _decorator;

/**
 * 体力调试测试脚本
 * 专门用于调试体力消耗问题
 */
@ccclass('EnergyDebugTest')
export class EnergyDebugTest extends Component {

    start() {
        console.log("=== 体力调试测试开始 ===");
        
        // 延迟执行，确保其他组件初始化完成
        this.scheduleOnce(() => {
            this.runEnergyDebugTests();
        }, 2.0);
    }

    /**
     * 运行体力调试测试
     */
    private runEnergyDebugTests() {
        console.log("\n--- 体力系统状态检查 ---");
        
        // 检查EnergyManager实例
        const energyManager = EnergyManager.getInstance();
        console.log(`EnergyManager实例: ${energyManager ? '✅ 已获取' : '❌ 未获取'}`);
        
        if (!energyManager) {
            console.error("❌ EnergyManager实例获取失败，无法进行测试");
            return;
        }

        // 显示当前体力状态
        const currentEnergy = energyManager.getCurrentEnergy();
        const maxEnergy = energyManager.getMaxEnergy();
        console.log(`当前体力: ${currentEnergy}/${maxEnergy}`);

        // 检查GameManager实例
        const gameManager = GameManager.inst();
        console.log(`GameManager实例: ${gameManager ? '✅ 已获取' : '❌ 未获取'}`);

        if (!gameManager) {
            console.error("❌ GameManager实例获取失败，无法进行测试");
            return;
        }

        // 检查GameManager的体力消耗标志
        const energyConsumed = (gameManager as any)._energyConsumed;
        console.log(`GameManager体力消耗标志: ${energyConsumed}`);

        // 手动测试体力消耗
        this.testManualEnergyConsumption();

        // 模拟游戏开始流程
        this.testGameStartFlow();
    }

    /**
     * 手动测试体力消耗
     */
    private testManualEnergyConsumption() {
        console.log("\n--- 手动体力消耗测试 ---");
        
        const energyManager = EnergyManager.getInstance();
        if (!energyManager) return;

        const beforeEnergy = energyManager.getCurrentEnergy();
        console.log(`测试前体力: ${beforeEnergy}`);

        if (beforeEnergy >= 4) {
            const success = energyManager.consumeEnergy();
            const afterEnergy = energyManager.getCurrentEnergy();
            
            console.log(`手动消耗结果: ${success ? '成功' : '失败'}`);
            console.log(`测试后体力: ${afterEnergy}`);
            
            if (success && afterEnergy === beforeEnergy - 4) {
                console.log("✅ 手动体力消耗测试通过");
            } else {
                console.error("❌ 手动体力消耗测试失败");
            }
        } else {
            console.log("⚠️ 体力不足，无法进行手动消耗测试");
        }
    }

    /**
     * 模拟游戏开始流程测试
     */
    private testGameStartFlow() {
        console.log("\n--- 游戏开始流程测试 ---");
        
        const gameManager = GameManager.inst();
        if (!gameManager) return;

        // 重置体力消耗标志（模拟transitionToReadyState）
        console.log("1. 模拟进入Ready状态，重置体力消耗标志");
        (gameManager as any)._energyConsumed = false;
        console.log(`体力消耗标志重置为: ${(gameManager as any)._energyConsumed}`);

        // 获取当前体力
        const energyManager = EnergyManager.getInstance();
        if (!energyManager) return;

        const beforeGameEnergy = energyManager.getCurrentEnergy();
        console.log(`游戏开始前体力: ${beforeGameEnergy}`);

        // 模拟调用transitionToGamingState
        console.log("2. 模拟调用transitionToGamingState");
        
        // 这里不直接调用transitionToGamingState，而是模拟其体力消耗逻辑
        this.simulateEnergyConsumptionLogic();
    }

    /**
     * 模拟体力消耗逻辑
     */
    private simulateEnergyConsumptionLogic() {
        console.log("\n--- 模拟体力消耗逻辑 ---");
        
        const gameManager = GameManager.inst();
        const energyManager = EnergyManager.getInstance();
        
        if (!gameManager || !energyManager) return;

        // 获取当前状态
        const energyConsumed = (gameManager as any)._energyConsumed;
        console.log(`当前体力消耗标志: ${energyConsumed}`);

        // 模拟条件检查
        const shouldConsumeEnergy = !energyConsumed;
        console.log(`是否应该消耗体力: ${shouldConsumeEnergy}`);

        if (shouldConsumeEnergy) {
            const beforeEnergy = energyManager.getCurrentEnergy();
            console.log(`消耗前体力: ${beforeEnergy}`);

            if (beforeEnergy >= 4) {
                const success = energyManager.consumeEnergy();
                const afterEnergy = energyManager.getCurrentEnergy();
                
                console.log(`消耗结果: ${success ? '成功' : '失败'}`);
                console.log(`消耗后体力: ${afterEnergy}`);

                if (success) {
                    // 设置体力消耗标志
                    (gameManager as any)._energyConsumed = true;
                    console.log(`体力消耗标志已设置: ${(gameManager as any)._energyConsumed}`);
                    console.log("✅ 模拟体力消耗成功");
                } else {
                    console.error("❌ 模拟体力消耗失败");
                }
            } else {
                console.log("⚠️ 体力不足，无法消耗");
            }
        } else {
            console.log("⚠️ 体力消耗标志已设置，跳过消耗");
        }
    }

    /**
     * 静态方法：重置体力到满值（用于测试）
     */
    public static resetEnergyToFull() {
        console.log("=== 重置体力到满值 ===");
        
        const energyManager = EnergyManager.getInstance();
        if (!energyManager) {
            console.error("❌ 未找到EnergyManager实例");
            return;
        }

        // 通过反射设置体力值（仅用于测试）
        (energyManager as any)._currentEnergy = 100;
        (energyManager as any).saveEnergy();
        (energyManager as any)._nextRecoverTime = 0;
        (energyManager as any).saveRecoverTime();
        (energyManager as any).notifyEnergyChange();

        console.log("✅ 体力已重置到满值");
        console.log(`当前体力: ${energyManager.getCurrentEnergy()}/${energyManager.getMaxEnergy()}`);
    }

    /**
     * 静态方法：重置GameManager体力消耗标志
     */
    public static resetEnergyConsumedFlag() {
        console.log("=== 重置体力消耗标志 ===");
        
        const gameManager = GameManager.inst();
        if (!gameManager) {
            console.error("❌ 未找到GameManager实例");
            return;
        }

        (gameManager as any)._energyConsumed = false;
        console.log("✅ 体力消耗标志已重置");
        console.log(`当前标志状态: ${(gameManager as any)._energyConsumed}`);
    }

    /**
     * 静态方法：显示详细状态
     */
    public static showDetailedStatus() {
        console.log("=== 详细状态信息 ===");
        
        const energyManager = EnergyManager.getInstance();
        const gameManager = GameManager.inst();

        if (energyManager) {
            console.log(`体力: ${energyManager.getCurrentEnergy()}/${energyManager.getMaxEnergy()}`);
            console.log(`下次恢复时间: ${energyManager.getTimeUntilNextRecover()}ms`);
        } else {
            console.log("❌ EnergyManager实例未找到");
        }

        if (gameManager) {
            console.log(`体力消耗标志: ${(gameManager as any)._energyConsumed}`);
            console.log(`当前游戏状态: ${(gameManager as any).curGS}`);
        } else {
            console.log("❌ GameManager实例未找到");
        }
    }
}
