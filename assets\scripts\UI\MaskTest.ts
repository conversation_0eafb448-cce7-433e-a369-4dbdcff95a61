import { _decorator, Component, Node, Label } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 遮罩测试脚本
 * 用于测试遮罩是否正确阻止了下层UI的交互
 */
@ccclass('MaskTest')
export class MaskTest extends Component {
    @property(Label)
    testLabel: Label = null;

    private clickCount: number = 0;

    start() {
        if (this.testLabel) {
            this.testLabel.string = "点击计数: 0";
        }
    }

    /**
     * 测试按钮点击事件
     * 如果遮罩正常工作，这个方法在遮罩显示时不应该被调用
     */
    onTestButtonClick() {
        this.clickCount++;
        console.log(`MaskTest: 按钮被点击，计数: ${this.clickCount}`);
        
        if (this.testLabel) {
            this.testLabel.string = `点击计数: ${this.clickCount}`;
        }
    }

    /**
     * 重置计数
     */
    resetCount() {
        this.clickCount = 0;
        if (this.testLabel) {
            this.testLabel.string = "点击计数: 0";
        }
        console.log("MaskTest: 计数已重置");
    }
}
