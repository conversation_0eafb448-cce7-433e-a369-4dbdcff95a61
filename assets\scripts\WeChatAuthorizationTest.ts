import { _decorator, Component, Node, Label, Button } from 'cc';
import { WeChatLoginManager } from './WeChatLoginManager';
import { WeChatFriendsData } from './Data/WeChatFriendsData';
const { ccclass, property } = _decorator;

/**
 * 微信授权测试组件
 * 用于测试和调试微信授权流程
 */
@ccclass('WeChatAuthorizationTest')
export class WeChatAuthorizationTest extends Component {
    @property(Label)
    statusLabel: Label = null;

    @property(Button)
    checkAuthButton: Button = null;

    @property(Button)
    requestAuthButton: Button = null;

    @property(Button)
    testFriendsButton: Button = null;

    @property(Button)
    clearCacheButton: Button = null;

    onLoad() {
        // 绑定按钮事件
        if (this.checkAuthButton) {
            this.checkAuthButton.node.on('click', this.checkAuthorizationStatus, this);
        }
        if (this.requestAuthButton) {
            this.requestAuthButton.node.on('click', this.requestAuthorization, this);
        }
        if (this.testFriendsButton) {
            this.testFriendsButton.node.on('click', this.testFriendsData, this);
        }
        if (this.clearCacheButton) {
            this.clearCacheButton.node.on('click', this.clearAuthCache, this);
        }

        this.updateStatus("微信授权测试工具已加载");
    }

    /**
     * 更新状态显示
     */
    private updateStatus(message: string): void {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log("WeChatAuthTest:", message);
    }

    /**
     * 检查当前授权状态
     */
    private async checkAuthorizationStatus(): Promise<void> {
        this.updateStatus("正在检查授权状态...");

        try {
            if (typeof wx === 'undefined') {
                this.updateStatus("❌ 非微信环境");
                return;
            }

            // 检查授权设置
            (wx as any).getSetting({
                success: (res: any) => {
                    console.log("完整授权设置:", res.authSetting);
                    
                    const userInfo = res.authSetting['scope.userInfo'];
                    const friendInfo = res.authSetting['scope.WxFriendInteraction'];
                    const werun = res.authSetting['scope.werun'];
                    
                    let status = "授权状态:\n";
                    status += `用户信息: ${userInfo === true ? '✅已授权' : userInfo === false ? '❌已拒绝' : '⚪未设置'}\n`;
                    status += `好友信息: ${friendInfo === true ? '✅已授权' : friendInfo === false ? '❌已拒绝' : '⚪未设置'}\n`;
                    status += `微信运动: ${werun === true ? '✅已授权' : werun === false ? '❌已拒绝' : '⚪未设置'}`;
                    
                    this.updateStatus(status);
                },
                fail: (error: any) => {
                    this.updateStatus("❌ 获取授权设置失败: " + error.errMsg);
                }
            });

        } catch (error) {
            this.updateStatus("❌ 检查授权状态异常: " + error.message);
        }
    }

    /**
     * 请求授权
     */
    private async requestAuthorization(): Promise<void> {
        this.updateStatus("正在请求授权...");

        try {
            const loginManager = WeChatLoginManager.instance;
            const success = await loginManager.authorizeLogin();
            
            if (success) {
                this.updateStatus("✅ 授权成功");
                // 重新检查授权状态
                setTimeout(() => {
                    this.checkAuthorizationStatus();
                }, 1000);
            } else {
                this.updateStatus("❌ 授权失败");
            }

        } catch (error) {
            this.updateStatus("❌ 请求授权异常: " + error.message);
        }
    }

    /**
     * 测试好友数据获取
     */
    private async testFriendsData(): Promise<void> {
        this.updateStatus("正在测试好友数据获取...");

        try {
            const friendsData = WeChatFriendsData.instance;
            const friends = friendsData.getAllFriends();
            
            let status = `好友数据测试结果:\n`;
            status += `总好友数: ${friends.length}\n`;
            
            if (friends.length > 0) {
                status += `好友列表:\n`;
                friends.slice(0, 5).forEach((friend, index) => {
                    status += `${index + 1}. ${friend.nickname}\n`;
                });
                if (friends.length > 5) {
                    status += `... 还有${friends.length - 5}个好友`;
                }
            } else {
                status += "❌ 没有获取到好友数据\n";
                status += "可能原因:\n";
                status += "1. 没有好友信息授权\n";
                status += "2. 好友没有玩过游戏\n";
                status += "3. 在开发环境中";
            }
            
            this.updateStatus(status);

        } catch (error) {
            this.updateStatus("❌ 测试好友数据异常: " + error.message);
        }
    }

    /**
     * 清除授权缓存
     */
    private clearAuthCache(): void {
        this.updateStatus("正在清除授权缓存...");

        try {
            if (typeof wx !== 'undefined') {
                // 清除本地存储的用户信息
                (wx as any).removeStorageSync('wechat_user_info');
                (wx as any).removeStorageSync('cached_openid');
                
                this.updateStatus("✅ 授权缓存已清除\n重新进入游戏将重新授权");
            } else {
                // 非微信环境，清除localStorage
                localStorage.removeItem('wechat_user_info');
                localStorage.removeItem('cached_openid');
                
                this.updateStatus("✅ 本地缓存已清除");
            }

        } catch (error) {
            this.updateStatus("❌ 清除缓存异常: " + error.message);
        }
    }

    /**
     * 手动触发好友信息授权
     */
    public async requestFriendAuthorization(): Promise<void> {
        this.updateStatus("正在请求好友信息授权...");

        try {
            if (typeof wx === 'undefined') {
                this.updateStatus("❌ 非微信环境");
                return;
            }

            (wx as any).authorize({
                scope: 'scope.WxFriendInteraction',
                success: () => {
                    this.updateStatus("✅ 好友信息授权成功");
                    // 重新检查授权状态
                    setTimeout(() => {
                        this.checkAuthorizationStatus();
                    }, 1000);
                },
                fail: (error: any) => {
                    this.updateStatus("❌ 好友信息授权失败: " + error.errMsg);
                    
                    if (error.errMsg.includes('deny')) {
                        this.updateStatus("用户拒绝了授权\n可以在设置中手动开启");
                    }
                }
            });

        } catch (error) {
            this.updateStatus("❌ 请求好友授权异常: " + error.message);
        }
    }
}
