import { _decorator, Component, Node, Button, Label, Sprite, Enum } from 'cc';
import { BirdType, GameData } from '../GameData';
const { ccclass, property } = _decorator;

@ccclass('BirdCard')
export class BirdCard extends Component {
    // 小鸟类型
    @property({ type: Enum(BirdType) })
    birdType: BirdType = BirdType.NORMAL;

    // 购买按钮
    @property(Button)
    purchaseButton: Button = null;

    // 价格标签
    @property(Label)
    priceLabel: Label = null;

    // 金币图标
    @property(Node)
    coinIcon: Node = null;

    // 金币不足提示
    @property(Node)
    coinLackSprite: Node = null;

    start() {
        this.initializeCard();
        this.updateDisplay();
    }

    private initializeCard() {
        // 设置价格
        if (this.priceLabel) {
            this.priceLabel.string = GameData.getBirdPrice(this.birdType).toString();
        }

        // 为购买按钮添加点击事件
        if (this.purchaseButton) {
            this.purchaseButton.node.on(Button.EventType.CLICK, this.onPurchaseClick, this);
        }

        // 初始化时隐藏金币不足提示
        if (this.coinLackSprite) {
            this.coinLackSprite.active = false;
        }

        console.log(`BirdCard初始化: ${GameData.getBirdName(this.birdType)}, 价格: ${GameData.getBirdPrice(this.birdType)}`);
    }

    /**
     * 购买按钮点击事件
     */
    private onPurchaseClick(): void {
        console.log(`点击购买${GameData.getBirdName(this.birdType)}`);

        // 普通小鸟默认拥有，不需要购买按钮
        if (this.birdType === BirdType.NORMAL) {
            console.log("普通小鸟默认拥有，无需购买");
            return;
        }

        // 检查是否已经拥有
        if (GameData.isBirdPurchased(this.birdType)) {
            console.log(`${GameData.getBirdName(this.birdType)} 已经拥有`);
            return;
        }

        // 检查金币是否足够
        const totalCoins = GameData.getTotalCoins();
        const price = GameData.getBirdPrice(this.birdType);

        if (totalCoins < price) {
            console.log(`金币不足，需要 ${price} 金币，当前只有 ${totalCoins} 金币`);
            this.showCoinLackSprite();
            return;
        }

        // 执行购买
        const success = GameData.purchaseBird(this.birdType);
        if (success) {
            this.updateDisplay();
            console.log(`购买成功！${GameData.getBirdName(this.birdType)}`);
            
            // 通知其他组件小鸟已解锁（如果需要的话）
            this.node.emit('bird-purchased', this.birdType);
        } else {
            console.log("购买失败");
            this.showCoinLackSprite();
        }
    }

    /**
     * 显示金币不足提示
     */
    private showCoinLackSprite(): void {
        if (this.coinLackSprite) {
            this.coinLackSprite.active = true;
            // 2秒后隐藏提示
            this.scheduleOnce(() => {
                if (this.coinLackSprite) {
                    this.coinLackSprite.active = false;
                }
            }, 2.0);
        }
    }

    /**
     * 更新显示
     */
    public updateDisplay(): void {
        const isPurchased = GameData.isBirdPurchased(this.birdType);

        // 普通小鸟默认拥有，不显示购买相关UI
        if (this.birdType === BirdType.NORMAL) {
            if (this.purchaseButton) {
                this.purchaseButton.node.active = false;
            }
            if (this.priceLabel) {
                this.priceLabel.node.active = false;
            }
            if (this.coinIcon) {
                this.coinIcon.active = false;
            }
            return;
        }

        // 购买按钮：未购买时显示，始终保持可交互
        if (this.purchaseButton) {
            this.purchaseButton.node.active = !isPurchased;

            if (!isPurchased) {
                // 始终保持按钮可交互，金币不足时在点击事件中处理
                this.purchaseButton.interactable = true;
                console.log(`${GameData.getBirdName(this.birdType)} 购买按钮设置为可见且可交互`);
            } else {
                console.log(`${GameData.getBirdName(this.birdType)} 购买按钮设置为隐藏（已购买）`);
            }
        }

        // 价格标签和金币图标：未购买时显示
        if (this.priceLabel) {
            this.priceLabel.node.active = !isPurchased;
        }
        if (this.coinIcon) {
            this.coinIcon.active = !isPurchased;
        }

        console.log(`${GameData.getBirdName(this.birdType)} 显示更新完成，已购买: ${isPurchased}`);
    }

    onDestroy() {
        // 移除事件监听
        if (this.purchaseButton && this.purchaseButton.node && this.purchaseButton.node.isValid) {
            this.purchaseButton.node.off(Button.EventType.CLICK, this.onPurchaseClick, this);
        }
    }
}
