import { _decorator, Component, Node } from 'cc';
import { GameData, BirdType, GameMode } from './GameData';
import { ItemManager, ItemType } from './ItemManager';
const { ccclass, property } = _decorator;

/**
 * 倍率系统测试类
 * 用于测试关卡基础倍率、金色小鸟倍率、幸运骰子倍率和双倍金币卡倍率的组合
 */
@ccclass('MultiplierTest')
export class MultiplierTest extends Component {

    start() {
        console.log("=== 倍率系统测试开始 ===");
        this.runAllTests();
    }

    private runAllTests() {
        this.testLevelBaseMultipliers();
        this.testGoldBirdMultiplier();
        this.testCombinedMultipliers();
        this.testMultiplierDisplay();
    }

    /**
     * 测试关卡基础倍率
     */
    private testLevelBaseMultipliers() {
        console.log("\n--- 测试1: 关卡基础倍率测试 ---");
        
        const testCases = [
            { mode: GameMode.NORMAL_EASY, expected: 1.0, name: "轻松难度" },
            { mode: GameMode.NORMAL_STANDARD, expected: 1.2, name: "标准难度" },
            { mode: GameMode.NORMAL_HARD, expected: 1.5, name: "困难难度" },
            { mode: GameMode.CHALLENGE_WIND, expected: 2.0, name: "大风吹" },
            { mode: GameMode.CHALLENGE_FOG, expected: 2.0, name: "大雾起" },
            { mode: GameMode.CHALLENGE_SNOW, expected: 2.0, name: "大雪飘" }
        ];
        
        let allPassed = true;
        
        for (const testCase of testCases) {
            // 设置当前游戏模式
            GameData.setCurrentGameMode(testCase.mode);
            const actualMultiplier = GameData.getLevelBaseMultiplier();
            
            console.log(`${testCase.name}: 期望${testCase.expected}, 实际${actualMultiplier}`);
            
            if (actualMultiplier !== testCase.expected) {
                console.error(`❌ ${testCase.name}倍率测试失败`);
                allPassed = false;
            }
        }
        
        if (allPassed) {
            console.log("✅ 关卡基础倍率测试通过");
        } else {
            console.error("❌ 关卡基础倍率测试失败");
        }
    }

    /**
     * 测试金色小鸟倍率
     */
    private testGoldBirdMultiplier() {
        console.log("\n--- 测试2: 金色小鸟倍率测试 ---");
        
        // 测试普通小鸟
        GameData.setSelectedBirdType(BirdType.NORMAL);
        const normalMultiplier = GameData.getGoldBirdMultiplier();
        console.log(`普通小鸟倍率: ${normalMultiplier}`);
        
        // 测试金色小鸟
        GameData.setSelectedBirdType(BirdType.GOLD);
        const goldMultiplier = GameData.getGoldBirdMultiplier();
        console.log(`金色小鸟倍率: ${goldMultiplier}`);
        
        // 测试其他小鸟
        GameData.setSelectedBirdType(BirdType.PENGUIN);
        const penguinMultiplier = GameData.getGoldBirdMultiplier();
        console.log(`企鹅小鸟倍率: ${penguinMultiplier}`);
        
        if (normalMultiplier === 1.0 && goldMultiplier === 1.5 && penguinMultiplier === 1.0) {
            console.log("✅ 金色小鸟倍率测试通过");
        } else {
            console.error("❌ 金色小鸟倍率测试失败");
        }
    }

    /**
     * 测试组合倍率
     */
    private testCombinedMultipliers() {
        console.log("\n--- 测试3: 组合倍率测试 ---");
        
        // 重置状态
        GameData.resetSessionCoins();
        GameData.clearLuckyDiceMultiplier();
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        ItemManager.deactivateItemEffect(ItemType.LUCKY_DICE);
        
        // 设置道具数量
        ItemManager.setItemCount(ItemType.DOUBLE_COIN, 1);
        ItemManager.setItemCount(ItemType.LUCKY_DICE, 1);
        
        // 测试案例1: 金色小鸟 + 幸运骰子(1倍) + 标准关卡
        console.log("\n测试案例1: 金色小鸟 + 幸运骰子(1倍) + 标准关卡");
        GameData.setCurrentGameMode(GameMode.NORMAL_STANDARD);
        GameData.setSelectedBirdType(BirdType.GOLD);
        ItemManager.activateItemEffect(ItemType.LUCKY_DICE);
        GameData.setLuckyDiceMultiplier(1);
        
        const info1 = GameData.getMultiplierInfo();
        console.log(`关卡基础:${info1.levelBase}, 骰子:${info1.luckyDice}, 金鸟:${info1.goldBird}, 双倍:${info1.doubleCoin}, 总计:${info1.total}`);
        
        const expected1 = 1.2 * 1 * 1.5 * 1; // 1.8
        if (Math.abs(info1.total - expected1) < 0.001) {
            console.log("✅ 案例1通过");
        } else {
            console.error(`❌ 案例1失败: 期望${expected1}, 实际${info1.total}`);
        }
        
        // 测试案例2: 幸运骰子(2倍) + 困难关卡
        console.log("\n测试案例2: 幸运骰子(2倍) + 困难关卡");
        GameData.setCurrentGameMode(GameMode.NORMAL_HARD);
        GameData.setSelectedBirdType(BirdType.NORMAL);
        GameData.setLuckyDiceMultiplier(2);
        
        const info2 = GameData.getMultiplierInfo();
        console.log(`关卡基础:${info2.levelBase}, 骰子:${info2.luckyDice}, 金鸟:${info2.goldBird}, 双倍:${info2.doubleCoin}, 总计:${info2.total}`);
        
        const expected2 = 1.5 * 2 * 1 * 1; // 3.0
        if (Math.abs(info2.total - expected2) < 0.001) {
            console.log("✅ 案例2通过");
        } else {
            console.error(`❌ 案例2失败: 期望${expected2}, 实际${info2.total}`);
        }
        
        // 测试案例3: 全部激活 + 挑战模式
        console.log("\n测试案例3: 全部激活 + 挑战模式");
        GameData.setCurrentGameMode(GameMode.CHALLENGE_WIND);
        GameData.setSelectedBirdType(BirdType.GOLD);
        ItemManager.activateItemEffect(ItemType.DOUBLE_COIN);
        GameData.setLuckyDiceMultiplier(3);
        
        const info3 = GameData.getMultiplierInfo();
        console.log(`关卡基础:${info3.levelBase}, 骰子:${info3.luckyDice}, 金鸟:${info3.goldBird}, 双倍:${info3.doubleCoin}, 总计:${info3.total}`);
        
        const expected3 = 2.0 * 3 * 1.5 * 2; // 18.0
        if (Math.abs(info3.total - expected3) < 0.001) {
            console.log("✅ 案例3通过");
        } else {
            console.error(`❌ 案例3失败: 期望${expected3}, 实际${info3.total}`);
        }
    }

    /**
     * 测试倍率显示格式
     */
    private testMultiplierDisplay() {
        console.log("\n--- 测试4: 倍率显示格式测试 ---");
        
        // 这里只能测试倍率信息的获取，实际的显示格式需要在GameOverUI中测试
        
        // 测试案例: 金色小鸟 + 幸运骰子(1倍) + 标准关卡
        GameData.setCurrentGameMode(GameMode.NORMAL_STANDARD);
        GameData.setSelectedBirdType(BirdType.GOLD);
        ItemManager.activateItemEffect(ItemType.LUCKY_DICE);
        GameData.setLuckyDiceMultiplier(1);
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        
        const info = GameData.getMultiplierInfo();
        console.log("显示格式测试数据:");
        console.log(`关卡基础倍率: ${info.levelBase}`);
        console.log(`幸运骰子倍率: ${info.luckyDice}`);
        console.log(`金色小鸟倍率: ${info.goldBird}`);
        console.log(`双倍金币卡倍率: ${info.doubleCoin}`);
        console.log(`总倍率: ${info.total}`);
        console.log("期望显示格式: X1.2X1X1.5=1.8!");
        
        // 模拟金币收集
        GameData.resetSessionCoins();
        GameData.addCoin(5);
        const sessionCoins = GameData.getSessionCoins();
        const finalCoins = GameData.getFinalSessionCoins();
        
        console.log(`原始金币: ${sessionCoins}, 最终金币: ${finalCoins}`);
        
        if (sessionCoins === 5 && Math.abs(finalCoins - 9) < 0.001) { // 5 * 1.8 = 9
            console.log("✅ 金币计算测试通过");
        } else {
            console.error(`❌ 金币计算测试失败: 期望9, 实际${finalCoins}`);
        }
        
        console.log("=== 倍率系统测试结束 ===");
    }
}
