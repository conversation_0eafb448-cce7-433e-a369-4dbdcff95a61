import { _decorator, Component, Label } from 'cc';
import { GameData, BirdType } from '../GameData';
import { WoodpeckerSkillManager } from '../WoodpeckerSkillManager';
import { GameManager } from '../GameManager';
const { ccclass, property } = _decorator;

@ccclass('WoodpeckerCooldownLabel')
export class WoodpeckerCooldownLabel extends Component {

    @property(Label)
    label: Label = null;

    // 冷却状态
    private isActive: boolean = false;
    private remainingTime: number = 0;

    onLoad() {
        console.log("=== WoodpeckerCooldownLabel组件onLoad ===");
        console.log("节点名称:", this.node.name);
        console.log("节点激活状态:", this.node.active);
        console.log("父节点:", this.node.parent ? this.node.parent.name : "无");
        console.log("父节点激活状态:", this.node.parent ? this.node.parent.active : "无");
    }

    start() {
        console.log("=== WoodpeckerCooldownLabel组件start开始 ===");

        // 获取Label组件
        if (!this.label) {
            this.label = this.getComponent(Label);
            console.log("自动获取Label组件:", !!this.label);
        } else {
            console.log("使用配置的Label组件:", !!this.label);
        }

        // 初始时隐藏
        this.node.active = false;

        console.log("=== WoodpeckerCooldownLabel组件已启动 ===");
    }

    onEnable() {
        console.log("WoodpeckerCooldownLabel组件onEnable - 节点被激活");
    }

    onDisable() {
        console.log("WoodpeckerCooldownLabel组件onDisable - 节点被禁用");
    }

    /**
     * 开始冷却倒计时
     * @param cooldownTime 冷却时间（秒）
     */
    public startCooldown(cooldownTime: number): void {
        console.log(`WoodpeckerCooldownLabel开始冷却倒计时: ${cooldownTime}秒`);

        this.remainingTime = cooldownTime;
        this.isActive = true;

        // 显示节点
        this.node.active = true;

        // 立即更新一次显示
        this.updateDisplay();
    }

    /**
     * 停止冷却倒计时
     */
    public stopCooldown(): void {
        console.log("WoodpeckerCooldownLabel停止冷却倒计时");

        this.isActive = false;
        this.remainingTime = 0;

        // 隐藏节点
        this.node.active = false;
    }

    /**
     * 更新显示
     */
    private updateDisplay(): void {
        if (!this.label) {
            return;
        }

        if (this.remainingTime > 0) {
            // 显示剩余时间，保留1位小数
            this.label.string = `${Math.ceil(this.remainingTime)}s`;
        } else {
            this.label.string = "0s";
        }
    }

    update(deltaTime: number): void {
        if (!this.isActive || this.remainingTime <= 0) {
            return;
        }

        // 检查游戏是否暂停，暂停时不更新倒计时
        const gameManager = GameManager.inst();
        if (gameManager && gameManager.isPaused()) {
            return; // 暂停时不更新倒计时
        }

        // 减少剩余时间
        this.remainingTime -= deltaTime;

        // 检查是否冷却结束
        if (this.remainingTime <= 0) {
            this.remainingTime = 0;
            this.stopCooldown();
            console.log("WoodpeckerCooldownLabel冷却倒计时结束");
        } else {
            // 更新显示
            this.updateDisplay();
        }
    }


}
