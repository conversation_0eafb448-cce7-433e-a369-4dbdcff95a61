// 云函数：更新邀请者奖励
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 使用当前云环境
})

// 获取数据库引用
const db = cloud.database()
const _ = db.command

/**
 * 云函数入口函数
 * 用于给邀请者发放奖励
 */
exports.main = async (event, context) => {
  const { inviteCode, reward } = event
  
  console.log('收到邀请码奖励请求:', { inviteCode, reward })
  
  try {
    // 验证参数
    if (!inviteCode || typeof reward !== 'number' || reward <= 0) {
      return {
        success: false,
        error: '参数无效',
        data: null
      }
    }
    
    // 查找拥有该邀请码的玩家
    const queryResult = await db.collection('players')
      .where({
        inviteCode: inviteCode
      })
      .get()
    
    if (queryResult.data.length === 0) {
      return {
        success: false,
        error: '未找到对应的邀请者',
        data: null
      }
    }
    
    const inviterPlayer = queryResult.data[0]
    console.log('找到邀请者:', inviterPlayer.nickname, '当前金币:', inviterPlayer.coins)
    
    // 更新邀请者的金币
    const updateResult = await db.collection('players')
      .doc(inviterPlayer._id)
      .update({
        data: {
          coins: _.inc(reward), // 增加金币
          updatedAt: new Date()
        }
      })
    
    if (updateResult.stats.updated === 1) {
      console.log(`成功给邀请者 ${inviterPlayer.nickname} 增加 ${reward} 金币`)
      
      return {
        success: true,
        error: null,
        data: {
          inviterNickname: inviterPlayer.nickname,
          oldCoins: inviterPlayer.coins,
          newCoins: inviterPlayer.coins + reward,
          reward: reward
        }
      }
    } else {
      return {
        success: false,
        error: '更新邀请者金币失败',
        data: null
      }
    }
    
  } catch (error) {
    console.error('处理邀请码奖励时出错:', error)
    
    return {
      success: false,
      error: error.message || '服务器内部错误',
      data: null
    }
  }
}
