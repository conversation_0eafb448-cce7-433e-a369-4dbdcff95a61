import { _decorator, Component, Label } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('TestLabel')
export class TestLabel extends Component {

    @property(Label)
    label: Label = null;

    onLoad() {
        console.log("=== TestLabel组件onLoad ===");
        console.log("节点名称:", this.node.name);
    }

    start() {
        console.log("=== TestLabel组件start ===");
        
        if (!this.label) {
            this.label = this.getComponent(Label);
        }
        
        if (this.label) {
            this.label.string = "测试成功！";
            console.log("TestLabel设置文本成功");
        }
        
        this.node.active = true;
        console.log("=== TestLabel组件启动完成 ===");
    }
}
