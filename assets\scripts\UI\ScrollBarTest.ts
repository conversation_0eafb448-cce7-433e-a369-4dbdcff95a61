import { _decorator, Component, Node, ScrollView, Label } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 滚动条测试组件
 * 用于测试滚动条拖拽功能是否正常工作
 */
@ccclass('ScrollBarTest')
export class ScrollBarTest extends Component {
    
    @property(ScrollView)
    testScrollView: ScrollView = null;
    
    @property(Label)
    debugLabel: Label = null;

    private scrollProgress: number = 0;

    onLoad() {
        if (this.testScrollView) {
            // 监听滚动事件
            this.testScrollView.node.on('scroll-to-top', this.onScrollToTop, this);
            this.testScrollView.node.on('scroll-to-bottom', this.onScrollToBottom, this);
            this.testScrollView.node.on('scrolling', this.onScrolling, this);
        }
    }

    start() {
        this.updateDebugInfo();
    }

    onDestroy() {
        if (this.testScrollView) {
            this.testScrollView.node.off('scroll-to-top', this.onScrollToTop, this);
            this.testScrollView.node.off('scroll-to-bottom', this.onScrollToBottom, this);
            this.testScrollView.node.off('scrolling', this.onScrolling, this);
        }
    }

    /**
     * 滚动到顶部事件
     */
    private onScrollToTop(): void {
        console.log("ScrollBarTest: 滚动到顶部");
        this.scrollProgress = 0;
        this.updateDebugInfo();
    }

    /**
     * 滚动到底部事件
     */
    private onScrollToBottom(): void {
        console.log("ScrollBarTest: 滚动到底部");
        this.scrollProgress = 1;
        this.updateDebugInfo();
    }

    /**
     * 滚动中事件
     */
    private onScrolling(): void {
        if (this.testScrollView) {
            this.scrollProgress = this.testScrollView.getScrollOffset().y / this.testScrollView.getMaxScrollOffset().y;
            this.updateDebugInfo();
        }
    }

    /**
     * 更新调试信息
     */
    private updateDebugInfo(): void {
        if (this.debugLabel) {
            const progressPercent = (this.scrollProgress * 100).toFixed(1);
            this.debugLabel.string = `滚动进度: ${progressPercent}%`;
        }
    }

    /**
     * 测试按钮：滚动到顶部
     */
    public testScrollToTop(): void {
        if (this.testScrollView) {
            this.testScrollView.scrollToTop(0.3);
            console.log("ScrollBarTest: 执行滚动到顶部");
        }
    }

    /**
     * 测试按钮：滚动到底部
     */
    public testScrollToBottom(): void {
        if (this.testScrollView) {
            this.testScrollView.scrollToBottom(0.3);
            console.log("ScrollBarTest: 执行滚动到底部");
        }
    }

    /**
     * 测试按钮：滚动到中间
     */
    public testScrollToMiddle(): void {
        if (this.testScrollView) {
            this.testScrollView.scrollToPercentVertical(0.5, 0.3);
            console.log("ScrollBarTest: 执行滚动到中间");
        }
    }

    /**
     * 测试按钮：随机滚动
     */
    public testRandomScroll(): void {
        if (this.testScrollView) {
            const randomProgress = Math.random();
            this.testScrollView.scrollToPercentVertical(randomProgress, 0.3);
            console.log(`ScrollBarTest: 执行随机滚动到 ${(randomProgress * 100).toFixed(1)}%`);
        }
    }
}
