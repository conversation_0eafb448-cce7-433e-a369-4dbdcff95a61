import { _decorator, Component, Button, Label } from 'cc';
import { AdManager, AdType, AdCallback } from './AdManager';
import { AdIdleTipManager } from './AdIdleTipManager';
const { ccclass, property } = _decorator;

/**
 * 广告管理器调试组件
 * 用于测试和调试广告功能
 */
@ccclass('AdManagerDebugger')
export class AdManagerDebugger extends Component {

    @property(Button)
    testEnergyAdButton: Button = null;

    @property(Button)
    testCoinCardAdButton: Button = null;

    @property(Button)
    testReviveAdButton: Button = null;

    @property(Button)
    forceTestModeButton: Button = null;

    @property(Button)
    testAdIdleTipButton: Button = null;

    @property(Label)
    statusLabel: Label = null;

    start() {
        this.bindButtonEvents();
        this.updateStatus();
    }

    private bindButtonEvents(): void {
        if (this.testEnergyAdButton) {
            this.testEnergyAdButton.node.on(Button.EventType.CLICK, this.onTestEnergyAdClick, this);
        }

        if (this.testCoinCardAdButton) {
            this.testCoinCardAdButton.node.on(Button.EventType.CLICK, this.onTestCoinCardAdClick, this);
        }

        if (this.testReviveAdButton) {
            this.testReviveAdButton.node.on(Button.EventType.CLICK, this.onTestReviveAdClick, this);
        }

        if (this.forceTestModeButton) {
            this.forceTestModeButton.node.on(Button.EventType.CLICK, this.onForceTestModeClick, this);
        }

        if (this.testAdIdleTipButton) {
            this.testAdIdleTipButton.node.on(Button.EventType.CLICK, this.onTestAdIdleTipClick, this);
        }
    }

    private onTestEnergyAdClick(): void {
        console.log("AdManagerDebugger: 测试体力广告");
        this.testAd(AdType.ENERGY);
    }

    private onTestCoinCardAdClick(): void {
        console.log("AdManagerDebugger: 测试金币卡广告");
        this.testAd(AdType.COIN_CARD);
    }

    private onTestReviveAdClick(): void {
        console.log("AdManagerDebugger: 测试复活广告");
        this.testAd(AdType.REVIVE);
    }

    private onForceTestModeClick(): void {
        console.log("AdManagerDebugger: 强制启用测试模式");
        const adManager = AdManager.getInstance();
        if (adManager) {
            adManager.forceTestMode();
            this.updateStatus();
        } else {
            console.error("AdManagerDebugger: 无法获取AdManager实例");
        }
    }

    private onTestAdIdleTipClick(): void {
        console.log("AdManagerDebugger: 测试广告空闲提示");
        AdIdleTipManager.showTip();
    }

    private testAd(adType: AdType): void {
        const adManager = AdManager.getInstance();
        if (!adManager) {
            console.error("AdManagerDebugger: 无法获取AdManager实例");
            return;
        }

        const callback: AdCallback = {
            onSuccess: () => {
                console.log(`AdManagerDebugger: ${adType}广告观看成功`);
                this.updateStatus(`${adType}广告观看成功`);
            },
            onFailed: (error) => {
                console.log(`AdManagerDebugger: ${adType}广告观看失败:`, error);
                this.updateStatus(`${adType}广告观看失败: ${error}`);
            },
            onClosed: () => {
                console.log(`AdManagerDebugger: ${adType}广告关闭`);
            }
        };

        adManager.showRewardedAd(adType, callback);
    }

    private updateStatus(message?: string): void {
        if (!this.statusLabel) return;

        const adManager = AdManager.getInstance();
        if (adManager) {
            const isTestMode = adManager.isTestMode();
            const baseStatus = `广告管理器状态: ${isTestMode ? '测试模式' : '广告模式'}`;
            this.statusLabel.string = message ? `${baseStatus}\n最后操作: ${message}` : baseStatus;
        } else {
            this.statusLabel.string = "广告管理器未初始化";
        }
    }

    onDestroy() {
        // 清理事件监听
        if (this.testEnergyAdButton && this.testEnergyAdButton.isValid) {
            this.testEnergyAdButton.node.off(Button.EventType.CLICK, this.onTestEnergyAdClick, this);
        }

        if (this.testCoinCardAdButton && this.testCoinCardAdButton.isValid) {
            this.testCoinCardAdButton.node.off(Button.EventType.CLICK, this.onTestCoinCardAdClick, this);
        }

        if (this.testReviveAdButton && this.testReviveAdButton.isValid) {
            this.testReviveAdButton.node.off(Button.EventType.CLICK, this.onTestReviveAdClick, this);
        }

        if (this.forceTestModeButton && this.forceTestModeButton.isValid) {
            this.forceTestModeButton.node.off(Button.EventType.CLICK, this.onForceTestModeClick, this);
        }

        if (this.testAdIdleTipButton && this.testAdIdleTipButton.isValid) {
            this.testAdIdleTipButton.node.off(Button.EventType.CLICK, this.onTestAdIdleTipClick, this);
        }
    }
}
