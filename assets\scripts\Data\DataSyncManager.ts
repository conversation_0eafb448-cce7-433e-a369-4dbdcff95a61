import { _decorator } from 'cc';
import { CloudDatabaseManager } from './CloudDatabaseManager';
import { GameMode, GameData } from '../GameData';
const { ccclass } = _decorator;

/**
 * 数据同步管理器
 * 控制数据库操作频率，避免过于频繁的数据库写入
 */
@ccclass('DataSyncManager')
export class DataSyncManager {
    private static _instance: DataSyncManager = null;

    // 同步策略配置
    private readonly SCORE_SYNC_DELAY = 2000; // 分数同步延迟2秒（本地操作，可以更快）
    private readonly COINS_SYNC_DELAY = 500;  // 金币同步延迟0.5秒
    private readonly MAX_RETRY_COUNT = 3;      // 最大重试次数
    
    // 待同步的数据队列
    private pendingScoreUpdates: Map<GameMode, { score: number; timestamp: number }> = new Map();
    private pendingCoinsUpdate: { amount: number; operation: 'add' | 'set'; timestamp: number } | null = null;
    
    // 同步定时器
    private scoreTimers: Map<GameMode, any> = new Map();
    private coinsTimer: any = null;
    
    // 重试计数
    private retryCount: Map<string, number> = new Map();
    
    public static get instance(): DataSyncManager {
        if (!this._instance) {
            this._instance = new DataSyncManager();
        }
        return this._instance;
    }

    /**
     * 请求同步分数（延迟批量处理）
     */
    public requestScoreSync(gameMode: GameMode, score: number): void {
        console.log(`DataSyncManager: 请求同步分数 ${gameMode}: ${score}`);
        
        // 更新待同步数据
        const currentPending = this.pendingScoreUpdates.get(gameMode);
        if (!currentPending || score > currentPending.score) {
            this.pendingScoreUpdates.set(gameMode, {
                score: score,
                timestamp: Date.now()
            });
        }
        
        // 清除现有定时器
        if (this.scoreTimers.has(gameMode)) {
            clearTimeout(this.scoreTimers.get(gameMode));
        }
        
        // 设置新的延迟同步定时器
        const timer = setTimeout(() => {
            this.syncScore(gameMode);
        }, this.SCORE_SYNC_DELAY);
        
        this.scoreTimers.set(gameMode, timer);
    }

    /**
     * 请求同步金币（延迟批量处理）
     */
    public requestCoinsSync(amount: number, operation: 'add' | 'set' = 'add'): void {
        console.log(`DataSyncManager: 请求同步金币 ${operation}: ${amount}`);
        
        // 更新待同步数据
        if (operation === 'add' && this.pendingCoinsUpdate && this.pendingCoinsUpdate.operation === 'add') {
            // 合并多次加法操作
            this.pendingCoinsUpdate.amount += amount;
            this.pendingCoinsUpdate.timestamp = Date.now();
        } else {
            this.pendingCoinsUpdate = {
                amount: amount,
                operation: operation,
                timestamp: Date.now()
            };
        }
        
        // 清除现有定时器
        if (this.coinsTimer) {
            clearTimeout(this.coinsTimer);
        }
        
        // 设置新的延迟同步定时器
        this.coinsTimer = setTimeout(() => {
            this.syncCoins();
        }, this.COINS_SYNC_DELAY);
    }

    /**
     * 立即同步分数（用于游戏结束等重要时刻）
     */
    public async syncScoreImmediately(gameMode: GameMode, score: number): Promise<boolean> {
        console.log(`DataSyncManager: 立即同步分数 ${gameMode}: ${score}`);
        
        // 清除延迟同步
        if (this.scoreTimers.has(gameMode)) {
            clearTimeout(this.scoreTimers.get(gameMode));
            this.scoreTimers.delete(gameMode);
        }
        this.pendingScoreUpdates.delete(gameMode);
        
        return await this.performScoreSync(gameMode, score);
    }

    /**
     * 立即同步金币
     */
    public async syncCoinsImmediately(amount: number, operation: 'add' | 'set' = 'add'): Promise<boolean> {
        console.log(`DataSyncManager: 立即同步金币 ${operation}: ${amount}`);
        
        // 清除延迟同步
        if (this.coinsTimer) {
            clearTimeout(this.coinsTimer);
            this.coinsTimer = null;
        }
        this.pendingCoinsUpdate = null;
        
        return await this.performCoinsSync(amount, operation);
    }

    /**
     * 执行分数同步
     */
    private async syncScore(gameMode: GameMode): Promise<void> {
        const pending = this.pendingScoreUpdates.get(gameMode);
        if (!pending) return;
        
        this.pendingScoreUpdates.delete(gameMode);
        this.scoreTimers.delete(gameMode);
        
        const success = await this.performScoreSync(gameMode, pending.score);
        if (!success) {
            // 重试机制
            const retryKey = `score_${gameMode}`;
            const currentRetries = this.retryCount.get(retryKey) || 0;
            
            if (currentRetries < this.MAX_RETRY_COUNT) {
                this.retryCount.set(retryKey, currentRetries + 1);
                console.log(`DataSyncManager: 分数同步失败，${5}秒后重试 (${currentRetries + 1}/${this.MAX_RETRY_COUNT})`);
                
                setTimeout(() => {
                    this.requestScoreSync(gameMode, pending.score);
                }, 5000);
            } else {
                console.error(`DataSyncManager: 分数同步失败，已达到最大重试次数`);
                this.retryCount.delete(retryKey);
            }
        } else {
            // 成功后清除重试计数
            this.retryCount.delete(`score_${gameMode}`);
        }
    }

    /**
     * 执行金币同步
     */
    private async syncCoins(): Promise<void> {
        if (!this.pendingCoinsUpdate) return;
        
        const pending = this.pendingCoinsUpdate;
        this.pendingCoinsUpdate = null;
        this.coinsTimer = null;
        
        const success = await this.performCoinsSync(pending.amount, pending.operation);
        if (!success) {
            // 重试机制
            const retryKey = 'coins';
            const currentRetries = this.retryCount.get(retryKey) || 0;
            
            if (currentRetries < this.MAX_RETRY_COUNT) {
                this.retryCount.set(retryKey, currentRetries + 1);
                console.log(`DataSyncManager: 金币同步失败，${5}秒后重试 (${currentRetries + 1}/${this.MAX_RETRY_COUNT})`);
                
                setTimeout(() => {
                    this.requestCoinsSync(pending.amount, pending.operation);
                }, 5000);
            } else {
                console.error(`DataSyncManager: 金币同步失败，已达到最大重试次数`);
                this.retryCount.delete(retryKey);
            }
        } else {
            // 成功后清除重试计数
            this.retryCount.delete('coins');
        }
    }

    /**
     * 实际执行分数同步
     */
    private async performScoreSync(gameMode: GameMode, score: number): Promise<boolean> {
        try {
            const cloudDB = CloudDatabaseManager.instance;
            return await cloudDB.updatePlayerScore(gameMode, score);
        } catch (error) {
            console.error('DataSyncManager: 分数同步异常', error);
            return false;
        }
    }

    /**
     * 实际执行金币同步
     */
    private async performCoinsSync(amount: number, operation: 'add' | 'set'): Promise<boolean> {
        try {
            const cloudDB = CloudDatabaseManager.instance;
            return await cloudDB.updatePlayerCoins(amount, operation);
        } catch (error) {
            console.error('DataSyncManager: 金币同步异常', error);
            return false;
        }
    }

    /**
     * 强制同步所有待处理的数据
     */
    public async forceSync(): Promise<void> {
        console.log('DataSyncManager: 强制同步所有待处理数据');
        
        // 同步所有待处理的分数
        const scorePromises: Promise<boolean>[] = [];
        for (const [gameMode, pending] of this.pendingScoreUpdates) {
            scorePromises.push(this.syncScoreImmediately(gameMode, pending.score));
        }
        
        // 同步待处理的金币
        if (this.pendingCoinsUpdate) {
            const coinsPromise = this.syncCoinsImmediately(
                this.pendingCoinsUpdate.amount, 
                this.pendingCoinsUpdate.operation
            );
            scorePromises.push(coinsPromise);
        }
        
        // 等待所有同步完成
        await Promise.all(scorePromises);
        console.log('DataSyncManager: 强制同步完成');
    }

    /**
     * 获取同步状态
     */
    public getSyncStatus(): {
        pendingScores: number;
        pendingCoins: boolean;
        retryCount: number;
    } {
        return {
            pendingScores: this.pendingScoreUpdates.size,
            pendingCoins: this.pendingCoinsUpdate !== null,
            retryCount: Array.from(this.retryCount.values()).reduce((sum, count) => sum + count, 0)
        };
    }

    /**
     * 清理资源
     */
    public cleanup(): void {
        // 清除所有定时器
        for (const timer of this.scoreTimers.values()) {
            clearTimeout(timer);
        }
        this.scoreTimers.clear();
        
        if (this.coinsTimer) {
            clearTimeout(this.coinsTimer);
            this.coinsTimer = null;
        }
        
        // 清除待同步数据
        this.pendingScoreUpdates.clear();
        this.pendingCoinsUpdate = null;
        this.retryCount.clear();
    }
}
