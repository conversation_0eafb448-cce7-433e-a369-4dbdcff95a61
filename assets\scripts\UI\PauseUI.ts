import { _decorator, Component, Node, Button } from 'cc';
import { GameManager } from '../GameManager';
const { ccclass, property } = _decorator;

@ccclass('PauseUI')
export class PauseUI extends Component {
    
    @property(Button)
    pauseButton: Button = null;
    
    @property(Button)
    resumeButton: Button = null;

    protected onLoad(): void {
        // 绑定按钮点击事件
        if (this.pauseButton) {
            this.pauseButton.node.on(Button.EventType.CLICK, this.onPauseButtonClick, this);
        }
        
        if (this.resumeButton) {
            this.resumeButton.node.on(Button.EventType.CLICK, this.onResumeButtonClick, this);
        }
        
        // 初始状态：显示暂停按钮，隐藏继续按钮
        this.showPauseButton();
    }

    protected onDestroy(): void {
        // 清理事件监听
        if (this.pauseButton && this.pauseButton.node && this.pauseButton.node.isValid) {
            this.pauseButton.node.off(Button.EventType.CLICK, this.onPauseButtonClick, this);
        }

        if (this.resumeButton && this.resumeButton.node && this.resumeButton.node.isValid) {
            this.resumeButton.node.off(Button.EventType.CLICK, this.onResumeButtonClick, this);
        }
    }

    /**
     * 暂停按钮点击事件
     */
    private onPauseButtonClick(): void {
        console.log("PauseUI: 点击暂停按钮");
        
        const gameManager = GameManager.inst();
        if (gameManager) {
            gameManager.pauseGame();
            this.showResumeButton();
        }
    }

    /**
     * 继续按钮点击事件
     */
    private onResumeButtonClick(): void {
        console.log("PauseUI: 点击继续按钮");
        
        const gameManager = GameManager.inst();
        if (gameManager) {
            gameManager.resumeGame();
            this.showPauseButton();
        }
    }

    /**
     * 显示暂停按钮，隐藏继续按钮
     */
    public showPauseButton(): void {
        if (this.pauseButton) {
            this.pauseButton.node.active = true;
        }
        if (this.resumeButton) {
            this.resumeButton.node.active = false;
        }
    }

    /**
     * 显示继续按钮，隐藏暂停按钮
     */
    public showResumeButton(): void {
        if (this.pauseButton) {
            this.pauseButton.node.active = false;
        }
        if (this.resumeButton) {
            this.resumeButton.node.active = true;
        }
    }

    /**
     * 重置UI状态（游戏重新开始时调用）
     */
    public resetUI(): void {
        this.showPauseButton();
    }
}
