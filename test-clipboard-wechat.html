<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信环境剪贴板测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .wechat-sim {
            background: #e8f5e8;
            border: 2px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 微信环境剪贴板测试</h1>
        
        <div class="test-section wechat-sim">
            <h3>📱 微信环境模拟</h3>
            <p>这个页面模拟微信小游戏环境，测试我们的复制功能修复</p>
            <button onclick="toggleWeChatAPI()">切换微信API状态</button>
            <button onclick="simulateAPIFailure()">模拟API失败</button>
            <div id="wechat-status" class="result info"></div>
        </div>

        <div class="test-section">
            <h3>🔧 复制测试</h3>
            <input type="text" id="test-text" value="wa6u15" placeholder="输入要复制的文本">
            <br>
            <button onclick="testCopySequence()">测试完整复制流程</button>
            <button onclick="testFailureRecovery()">测试失败恢复</button>
            <div id="copy-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果</h3>
            <div id="test-summary" class="result info"></div>
        </div>

        <div class="test-section">
            <h3>📝 详细日志</h3>
            <button onclick="clearLogs()">清空日志</button>
            <div id="log-result" class="result info" style="max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        // 测试状态
        let testResults = {
            wechatAPI: null,
            modernAPI: null,
            legacyAPI: null,
            fallbackShown: false
        };

        // 日志收集
        const logs = [];
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.join(' ');
            logs.push(`${timestamp}: ${message}`);
            updateLogDisplay();
            originalConsoleLog.apply(console, arguments);
        };

        // 微信API模拟
        let wechatAPIEnabled = true;
        let shouldAPIFail = false;

        window.wx = {
            setClipboardData: function(options) {
                console.log('微信API调用:', options);
                
                if (!wechatAPIEnabled) {
                    console.log('微信API被禁用');
                    return;
                }
                
                setTimeout(() => {
                    if (shouldAPIFail) {
                        console.log('微信API模拟失败');
                        testResults.wechatAPI = false;
                        if (options.fail) {
                            options.fail({
                                errMsg: 'setClipboardData:fail',
                                error: 'mock_failure'
                            });
                        }
                    } else {
                        console.log('微信API模拟成功');
                        testResults.wechatAPI = true;
                        if (options.success) {
                            options.success();
                        }
                    }
                    updateTestSummary();
                }, 100);
            },
            showToast: function(options) {
                console.log('微信Toast:', options.title);
                // 在页面上显示toast模拟
                showMockToast(options.title);
            },
            showModal: function(options) {
                console.log('微信Modal:', options.content);
                testResults.fallbackShown = true;
                updateTestSummary();
                // 在页面上显示modal模拟
                showMockModal(options.title || '提示', options.content);
                if (options.success) {
                    setTimeout(() => options.success({confirm: true, cancel: false}), 500);
                }
            }
        };

        function toggleWeChatAPI() {
            wechatAPIEnabled = !wechatAPIEnabled;
            updateWeChatStatus();
            console.log('微信API状态:', wechatAPIEnabled ? '启用' : '禁用');
        }

        function simulateAPIFailure() {
            shouldAPIFail = !shouldAPIFail;
            updateWeChatStatus();
            console.log('API失败模拟:', shouldAPIFail ? '启用' : '禁用');
        }

        function updateWeChatStatus() {
            const status = document.getElementById('wechat-status');
            status.textContent = `微信API: ${wechatAPIEnabled ? '启用' : '禁用'}\n失败模拟: ${shouldAPIFail ? '启用' : '禁用'}`;
        }

        function testCopySequence() {
            const text = document.getElementById('test-text').value;
            const result = document.getElementById('copy-result');
            
            // 重置测试结果
            testResults = {
                wechatAPI: null,
                modernAPI: null,
                legacyAPI: null,
                fallbackShown: false
            };
            
            result.textContent = '开始完整复制流程测试...';
            result.className = 'result info';
            
            console.log('=== 开始复制测试 ===');
            console.log('测试文本:', text);
            
            // 模拟我们的WeChatClipboard.copyText逻辑
            simulateWeChatClipboardCopy(text, result);
        }

        function testFailureRecovery() {
            // 强制API失败来测试恢复机制
            const originalShouldFail = shouldAPIFail;
            shouldAPIFail = true;
            updateWeChatStatus();
            
            setTimeout(() => {
                testCopySequence();
                // 2秒后恢复原状态
                setTimeout(() => {
                    shouldAPIFail = originalShouldFail;
                    updateWeChatStatus();
                }, 2000);
            }, 100);
        }

        function simulateWeChatClipboardCopy(text, resultElement) {
            console.log('WeChatClipboard: 尝试复制文本:', text);
            
            // 方法1: 微信小游戏API
            if (typeof wx !== 'undefined' && wx.setClipboardData && wechatAPIEnabled) {
                console.log('WeChatClipboard: 使用微信API');
                wx.setClipboardData({
                    data: text,
                    success: () => {
                        console.log('WeChatClipboard: 微信API复制成功');
                        resultElement.textContent = '✅ 微信API复制成功';
                        resultElement.className = 'result success';
                        updateTestSummary();
                    },
                    fail: (err) => {
                        console.log('WeChatClipboard: 微信API复制失败，详细错误:', err);
                        console.log('微信API失败，尝试其他复制方法...');
                        
                        resultElement.textContent = '❌ 微信API失败，尝试其他方法...\n';
                        
                        // 尝试现代浏览器API
                        tryModernAPI(text, resultElement);
                    }
                });
                return;
            }
            
            // 直接尝试其他方法
            console.log('微信API不可用，尝试其他方法');
            tryModernAPI(text, resultElement);
        }

        function tryModernAPI(text, resultElement) {
            if (!navigator.clipboard || !window.isSecureContext) {
                console.log('WeChatClipboard: 现代API不可用');
                tryLegacyAPI(text, resultElement);
                return;
            }
            
            console.log('WeChatClipboard: 使用现代API');
            navigator.clipboard.writeText(text).then(() => {
                console.log('WeChatClipboard: 现代API复制成功');
                testResults.modernAPI = true;
                resultElement.textContent += '✅ 现代API复制成功';
                resultElement.className = 'result success';
                updateTestSummary();
            }).catch((err) => {
                console.log('WeChatClipboard: 现代API复制失败:', err);
                testResults.modernAPI = false;
                resultElement.textContent += '❌ 现代API失败，尝试传统方法...\n';
                tryLegacyAPI(text, resultElement);
            });
        }

        function tryLegacyAPI(text, resultElement) {
            if (typeof document === 'undefined' || typeof document.execCommand !== 'function') {
                console.log('WeChatClipboard: 传统API不可用');
                showFallback(text, resultElement);
                return;
            }
            
            console.log('WeChatClipboard: 使用传统API');
            try {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.position = "fixed";
                textArea.style.left = "-999999px";
                textArea.style.top = "-999999px";
                textArea.style.opacity = "0";
                
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (successful) {
                    console.log('WeChatClipboard: 传统API复制成功');
                    testResults.legacyAPI = true;
                    resultElement.textContent += '✅ 传统API复制成功';
                    resultElement.className = 'result success';
                } else {
                    console.log('WeChatClipboard: 传统API复制失败');
                    testResults.legacyAPI = false;
                    resultElement.textContent += '❌ 传统API失败\n';
                    showFallback(text, resultElement);
                }
                updateTestSummary();
            } catch (error) {
                console.log('WeChatClipboard: 传统API异常:', error);
                testResults.legacyAPI = false;
                resultElement.textContent += '❌ 传统API异常\n';
                showFallback(text, resultElement);
                updateTestSummary();
            }
        }

        function showFallback(text, resultElement) {
            console.log('所有复制方法都失败，显示手动复制提示');
            if (typeof wx !== 'undefined' && wx.showModal) {
                wx.showModal({
                    title: '复制失败',
                    content: `请手动复制邀请码：${text}`,
                    showCancel: false
                });
            }
            resultElement.textContent += '📋 显示手动复制提示';
            resultElement.className = 'result error';
        }

        function showMockToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 9999;
                font-size: 14px;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 2000);
        }

        function showMockModal(title, content) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;
            
            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                padding: 20px;
                border-radius: 10px;
                max-width: 300px;
                text-align: center;
            `;
            
            modalContent.innerHTML = `
                <h3>${title}</h3>
                <p>${content}</p>
                <button onclick="this.closest('.modal').remove()" style="
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 3px;
                    cursor: pointer;
                ">确定</button>
            `;
            
            modal.className = 'modal';
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
        }

        function updateTestSummary() {
            const summary = document.getElementById('test-summary');
            let text = '测试结果摘要:\n';
            text += `微信API: ${testResults.wechatAPI === null ? '未测试' : (testResults.wechatAPI ? '✅ 成功' : '❌ 失败')}\n`;
            text += `现代API: ${testResults.modernAPI === null ? '未测试' : (testResults.modernAPI ? '✅ 成功' : '❌ 失败')}\n`;
            text += `传统API: ${testResults.legacyAPI === null ? '未测试' : (testResults.legacyAPI ? '✅ 成功' : '❌ 失败')}\n`;
            text += `显示备用提示: ${testResults.fallbackShown ? '✅ 是' : '❌ 否'}\n`;
            
            const anySuccess = testResults.wechatAPI || testResults.modernAPI || testResults.legacyAPI;
            text += `\n总体结果: ${anySuccess ? '✅ 至少一种方法成功' : (testResults.fallbackShown ? '⚠️ 显示了备用提示' : '❓ 测试进行中')}`;
            
            summary.textContent = text;
        }

        function updateLogDisplay() {
            const logResult = document.getElementById('log-result');
            logResult.textContent = logs.slice(-20).join('\n'); // 只显示最近20条
            logResult.scrollTop = logResult.scrollHeight;
        }

        function clearLogs() {
            logs.length = 0;
            updateLogDisplay();
        }

        // 页面加载时初始化
        window.onload = function() {
            updateWeChatStatus();
            updateTestSummary();
            console.log('微信环境测试页面已加载');
        };
    </script>
</body>
</html>
