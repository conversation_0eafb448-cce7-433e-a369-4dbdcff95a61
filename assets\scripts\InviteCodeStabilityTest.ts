import { _decorator, Component, Node } from 'cc';
import { WeChatLoginManager } from './WeChatLoginManager';
import { CloudDatabaseManager } from './Data/CloudDatabaseManager';
import { InviteCodeManager } from './InviteCodeManager';

const { ccclass, property } = _decorator;

/**
 * 邀请码稳定性测试
 * 验证邀请码是否会重复生成
 */
@ccclass('InviteCodeStabilityTest')
export class InviteCodeStabilityTest extends Component {

    private inviteCodeHistory: string[] = [];

    onLoad() {
        console.log("=== 邀请码稳定性测试开始 ===");
        
        // 延迟执行，确保系统初始化完成
        this.scheduleOnce(() => {
            this.runStabilityTest();
        }, 3.0);
    }

    private async runStabilityTest() {
        console.log("\n🧪 开始邀请码稳定性测试");
        
        // 1. 记录当前邀请码
        await this.recordCurrentInviteCode("初始状态");
        
        // 2. 模拟多次登录初始化
        await this.simulateMultipleLogins();
        
        // 3. 检查邀请码变化
        await this.checkInviteCodeChanges();
        
        // 4. 验证云端数据一致性
        await this.verifyCloudDataConsistency();
    }

    private async recordCurrentInviteCode(stage: string) {
        const localInviteCode = InviteCodeManager.getPlayerInviteCode();
        const savedUserId = localStorage.getItem('cloud_user_id');
        
        console.log(`\n--- ${stage} ---`);
        console.log(`本地邀请码: ${localInviteCode}`);
        console.log(`保存的用户ID: ${savedUserId}`);
        
        // 查询云端邀请码
        try {
            const cloudDB = CloudDatabaseManager.instance;
            const cloudUserData = await cloudDB.getCurrentUserData();
            if (cloudUserData) {
                console.log(`云端邀请码: ${cloudUserData.inviteCode}`);
                console.log(`云端用户ID: ${cloudUserData._id}`);
                console.log(`云端昵称: ${cloudUserData.nickname}`);
                
                // 记录到历史
                this.inviteCodeHistory.push(`${stage}: 本地=${localInviteCode}, 云端=${cloudUserData.inviteCode}`);
            } else {
                console.log("云端数据: 未找到");
                this.inviteCodeHistory.push(`${stage}: 本地=${localInviteCode}, 云端=未找到`);
            }
        } catch (error) {
            console.error("查询云端数据失败:", error);
            this.inviteCodeHistory.push(`${stage}: 本地=${localInviteCode}, 云端=查询失败`);
        }
    }

    private async simulateMultipleLogins() {
        console.log("\n🔄 模拟多次登录初始化");
        
        const loginManager = WeChatLoginManager.instance;
        
        for (let i = 1; i <= 3; i++) {
            console.log(`\n--- 第${i}次模拟登录 ---`);
            
            try {
                // 重新初始化登录管理器
                const success = await loginManager.initialize();
                console.log(`第${i}次登录结果: ${success}`);
                
                // 记录邀请码状态
                await this.recordCurrentInviteCode(`第${i}次登录后`);
                
                // 等待一秒
                await this.delay(1000);
                
            } catch (error) {
                console.error(`第${i}次登录失败:`, error);
            }
        }
    }

    private async checkInviteCodeChanges() {
        console.log("\n📊 检查邀请码变化历史");
        
        console.log("邀请码变化历史:");
        this.inviteCodeHistory.forEach((record, index) => {
            console.log(`${index + 1}. ${record}`);
        });
        
        // 分析变化
        const localCodes = this.inviteCodeHistory.map(record => {
            const match = record.match(/本地=([^,]+)/);
            return match ? match[1] : 'unknown';
        });
        
        const cloudCodes = this.inviteCodeHistory.map(record => {
            const match = record.match(/云端=([^,]+)/);
            return match ? match[1] : 'unknown';
        });
        
        const uniqueLocalCodes = [...new Set(localCodes)];
        const uniqueCloudCodes = [...new Set(cloudCodes)];
        
        console.log(`\n分析结果:`);
        console.log(`本地邀请码变化: ${localCodes.join(' → ')}`);
        console.log(`云端邀请码变化: ${cloudCodes.join(' → ')}`);
        console.log(`本地唯一邀请码数量: ${uniqueLocalCodes.length}`);
        console.log(`云端唯一邀请码数量: ${uniqueCloudCodes.length}`);
        
        if (uniqueLocalCodes.length === 1 && uniqueCloudCodes.length === 1) {
            console.log("✅ 邀请码保持稳定，没有重复生成");
        } else {
            console.log("❌ 邀请码发生了变化，存在重复生成问题");
        }
    }

    private async verifyCloudDataConsistency() {
        console.log("\n🔍 验证云端数据一致性");
        
        try {
            const cloudDB = CloudDatabaseManager.instance;
            
            // 查询所有玩家数据，检查是否有重复记录
            const allPlayers = await cloudDB.getPlayersData(10);
            
            if (allPlayers && allPlayers.length > 0) {
                console.log(`找到 ${allPlayers.length} 条玩家记录`);
                
                // 获取当前用户的openid
                const loginManager = WeChatLoginManager.instance;
                const currentOpenid = loginManager.getOpenid();
                
                // 查找当前用户的所有记录
                const currentUserRecords = allPlayers.filter(player => 
                    player._openid === currentOpenid
                );
                
                console.log(`当前用户(openid: ${currentOpenid})的记录数量: ${currentUserRecords.length}`);
                
                if (currentUserRecords.length > 1) {
                    console.log("⚠️ 发现多条记录，可能导致邀请码变化:");
                    currentUserRecords.forEach((record, index) => {
                        console.log(`  记录${index + 1}: ID=${record._id}, 邀请码=${record.inviteCode}, 昵称=${record.nickname}`);
                    });
                } else if (currentUserRecords.length === 1) {
                    console.log("✅ 只有一条记录，数据一致性良好");
                    const record = currentUserRecords[0];
                    console.log(`  记录: ID=${record._id}, 邀请码=${record.inviteCode}, 昵称=${record.nickname}`);
                } else {
                    console.log("❌ 没有找到当前用户的记录");
                }
                
            } else {
                console.log("没有找到任何玩家记录");
            }
            
        } catch (error) {
            console.error("验证云端数据一致性失败:", error);
        }
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 显示当前邀请码状态
     */
    public async showCurrentInviteCodeStatus() {
        await this.recordCurrentInviteCode("手动检查");
    }

    /**
     * 清除用户ID，测试恢复机制
     */
    public async testUserIdRecovery() {
        console.log("\n🔄 测试用户ID恢复机制");
        
        // 记录清除前状态
        await this.recordCurrentInviteCode("清除用户ID前");
        
        // 清除用户ID
        localStorage.removeItem('cloud_user_id');
        console.log("已清除本地用户ID");
        
        // 重新初始化
        const loginManager = WeChatLoginManager.instance;
        const success = await loginManager.initialize();
        console.log(`重新初始化结果: ${success}`);
        
        // 记录恢复后状态
        await this.recordCurrentInviteCode("恢复用户ID后");
    }

    /**
     * 手动测试
     */
    public manualTest() {
        this.runStabilityTest();
    }
}
