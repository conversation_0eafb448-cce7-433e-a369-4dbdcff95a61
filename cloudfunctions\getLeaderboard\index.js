// 云函数：获取排行榜数据
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 云函数入口函数
 * 获取指定关卡的排行榜数据，支持批量获取所有关卡
 */
exports.main = async (event, context) => {
  const { gameMode, limit = 100, offset = 0, getAllModes = false } = event

  console.log('获取全服排行榜请求:', { gameMode, limit, offset, getAllModes })

  if (getAllModes) {
    console.log('批量获取所有关卡排行榜数据')
    return await getAllModesLeaderboard(limit, offset)
  } else {
    console.log('gameMode类型:', typeof gameMode, '值:', gameMode)
    return await getSingleModeLeaderboard(gameMode, limit, offset)
  }
}

/**
 * 获取所有关卡的排行榜数据
 */
async function getAllModesLeaderboard(limit, offset) {
  try {
    const allModes = [0, 1, 2, 3, 4, 5] // 对应6个关卡
    const result = {}

    console.log(`开始批量获取${allModes.length}个关卡的排行榜数据，每个关卡${limit}条`)

    // 并行获取所有关卡的数据
    const promises = allModes.map(async (mode) => {
      const modeResult = await getSingleModeLeaderboard(mode, limit, offset)
      return { mode, data: modeResult }
    })

    const results = await Promise.all(promises)

    // 组织返回数据
    results.forEach(({ mode, data }) => {
      if (data.success) {
        result[mode] = data.data.players
      } else {
        result[mode] = []
        console.error(`关卡${mode}数据获取失败:`, data.error)
      }
    })

    const totalPlayers = Object.values(result).reduce((sum, players) => sum + players.length, 0)
    console.log(`批量获取完成，总计${totalPlayers}条数据`)

    return {
      success: true,
      data: {
        allModes: result,
        totalCount: totalPlayers
      }
    }

  } catch (error) {
    console.error('批量获取排行榜数据时出错:', error)
    return {
      success: false,
      error: error.message || '批量获取排行榜数据失败',
      data: null
    }
  }
}

/**
 * 获取单个关卡的排行榜数据
 */
async function getSingleModeLeaderboard(gameMode, limit, offset) {
  try {
    // 验证参数 - 修复gameMode为0时的判断问题
    if (gameMode === undefined || gameMode === null) {
      return {
        success: false,
        error: '缺少gameMode参数',
        data: null
      }
    }
    
    // 构建聚合查询，在云端进行排序
    const result = await db.collection('players')
      .aggregate()
      .addFields({
        // 计算指定关卡的最高分
        maxScore: {
          $max: `$topScores.${gameMode}`
        }
      })
      .match({
        // 过滤掉没有该关卡分数的玩家
        maxScore: _.gt(0)
      })
      .sort({
        // 按最高分降序排列
        maxScore: -1
      })
      .skip(offset)
      .limit(limit) // 使用传入的limit值
      .project({
        // 只返回需要的字段，减少数据传输
        _id: 1,
        nickname: 1,
        avatarUrl: 1,
        coins: 1,
        maxScore: 1,
        [`topScores.${gameMode}`]: 1
      })
      .end()
    
    if (result.errMsg === 'collection.aggregate:ok') {
      // 添加排名信息
      const leaderboardData = result.list.map((player, index) => ({
        ...player,
        rank: offset + index + 1
      }))
      
      console.log(`成功获取排行榜数据，关卡${gameMode}，共${leaderboardData.length}条`)
      
      return {
        success: true,
        error: null,
        data: {
          gameMode,
          players: leaderboardData,
          total: leaderboardData.length,
          hasMore: leaderboardData.length === limit
        }
      }
    } else {
      return {
        success: false,
        error: '查询排行榜失败',
        data: null
      }
    }
    
  } catch (error) {
    console.error('获取排行榜时出错:', error)
    
    return {
      success: false,
      error: error.message || '服务器内部错误',
      data: null
    }
  }
}
