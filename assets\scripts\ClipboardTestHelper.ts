import { _decorator, Component } from 'cc';
import { WeChatClipboard } from './Utils/WeChatClipboard';
const { ccclass, property } = _decorator;

/**
 * 剪贴板功能测试助手
 * 用于测试和调试复制功能
 */
@ccclass('ClipboardTestHelper')
export class ClipboardTestHelper extends Component {

    start() {
        // 暴露测试方法到全局
        this.exposeTestMethods();
        
        // 执行初始检查
        this.performInitialCheck();
    }

    /**
     * 暴露测试方法到全局
     */
    private exposeTestMethods() {
        if (typeof window !== 'undefined') {
            (window as any).clipboardTest = {
                // 测试复制功能
                testCopy: (text?: string) => {
                    this.testCopyFunction(text || "测试文本123");
                },
                
                // 检查复制功能可用性
                checkAvailability: () => {
                    return this.checkCopyAvailability();
                },
                
                // 测试微信API
                testWeChatAPI: (text?: string) => {
                    this.testWeChatAPI(text || "微信API测试");
                },
                
                // 显示环境信息
                showEnvironmentInfo: () => {
                    this.showEnvironmentInfo();
                }
            };
            
            console.log("剪贴板测试方法已暴露到 window.clipboardTest");
            console.log("可用方法:");
            console.log("- window.clipboardTest.testCopy('文本') - 测试复制功能");
            console.log("- window.clipboardTest.checkAvailability() - 检查可用性");
            console.log("- window.clipboardTest.testWeChatAPI('文本') - 测试微信API");
            console.log("- window.clipboardTest.showEnvironmentInfo() - 显示环境信息");
        }
    }

    /**
     * 执行初始检查
     */
    private performInitialCheck() {
        console.log("=== 剪贴板功能初始检查 ===");
        
        const availability = WeChatClipboard.checkAvailability();
        console.log("复制功能可用性:", availability);
        
        this.showEnvironmentInfo();
        
        console.log("=== 初始检查完成 ===");
    }

    /**
     * 测试复制功能
     */
    private testCopyFunction(text: string) {
        console.log(`测试复制功能，文本: ${text}`);
        
        WeChatClipboard.copyText(
            text,
            () => {
                console.log("✅ 复制测试成功");
            },
            (error: string) => {
                console.log("❌ 复制测试失败:", error);
            }
        );
    }

    /**
     * 检查复制功能可用性
     */
    private checkCopyAvailability() {
        console.log("=== 复制功能可用性检查 ===");

        // 使用新的诊断功能
        WeChatClipboard.diagnoseEnvironment();

        const availability = WeChatClipboard.checkAvailability();
        console.log("详细可用性信息:", availability);

        if (availability.anyAvailable) {
            console.log("✅ 至少有一种复制方法可用");
            console.log(`💡 ${availability.recommendation}`);
        } else {
            console.log("❌ 没有可用的复制方法");
            console.log("💡 只能使用手动复制提示");
        }

        return availability;
    }

    /**
     * 测试微信API
     */
    private testWeChatAPI(text: string) {
        console.log(`测试微信API，文本: ${text}`);
        
        if (typeof wx === 'undefined') {
            console.log("❌ wx对象不存在");
            return;
        }
        
        if (!wx.setClipboardData) {
            console.log("❌ wx.setClipboardData方法不存在");
            return;
        }
        
        console.log("✅ 微信API可用，开始测试...");
        
        try {
            wx.setClipboardData({
                data: text,
                success: () => {
                    console.log("✅ 微信API测试成功");
                    
                    // 测试showToast
                    if (wx.showToast) {
                        wx.showToast({
                            title: '微信API测试成功',
                            icon: 'success',
                            duration: 2000
                        });
                    }
                },
                fail: (err: any) => {
                    console.log("❌ 微信API测试失败:", err);
                    
                    // 测试showToast显示错误
                    if (wx.showToast) {
                        wx.showToast({
                            title: '微信API测试失败',
                            icon: 'none',
                            duration: 2000
                        });
                    }
                }
            });
        } catch (error) {
            console.log("❌ 微信API调用异常:", error);
        }
    }

    /**
     * 显示环境信息
     */
    private showEnvironmentInfo() {
        console.log("=== 环境信息 ===");
        
        // 检查微信小游戏环境
        const isWeChatGame = typeof wx !== 'undefined';
        console.log(`微信小游戏环境: ${isWeChatGame ? '是' : '否'}`);
        
        if (isWeChatGame) {
            console.log("微信API检查:");
            console.log(`- wx.setClipboardData: ${typeof wx.setClipboardData === 'function' ? '存在' : '不存在'}`);
            console.log(`- wx.showToast: ${typeof wx.showToast === 'function' ? '存在' : '不存在'}`);
            console.log(`- wx.showModal: ${typeof wx.showModal === 'function' ? '存在' : '不存在'}`);
        }
        
        // 检查浏览器API
        console.log("浏览器API检查:");
        console.log(`- navigator.clipboard: ${!!navigator.clipboard ? '存在' : '不存在'}`);
        console.log(`- window.isSecureContext: ${!!window.isSecureContext ? '是' : '否'}`);
        console.log(`- document.execCommand: ${typeof document.execCommand === 'function' ? '存在' : '不存在'}`);
        
        // 检查其他相关API
        console.log("其他API检查:");
        console.log(`- alert: ${typeof alert === 'function' ? '存在' : '不存在'}`);
        console.log(`- console: ${typeof console === 'object' ? '存在' : '不存在'}`);
        
        console.log("=== 环境信息结束 ===");
    }

    /**
     * 运行完整的复制功能测试套件
     */
    public runFullTest() {
        console.log("=== 开始完整的复制功能测试 ===");
        
        // 1. 检查可用性
        const availability = this.checkCopyAvailability();
        
        // 2. 测试环境信息
        this.showEnvironmentInfo();
        
        // 3. 如果微信API可用，测试微信API
        if (availability.wechat) {
            console.log("测试微信API...");
            this.testWeChatAPI("微信API完整测试");
        }
        
        // 4. 测试通用复制功能
        console.log("测试通用复制功能...");
        this.testCopyFunction("完整测试文本");
        
        console.log("=== 完整测试结束 ===");
    }
}
