import { _decorator, Component, Node, Button, Label } from 'cc';
import { CloudDatabaseManager } from './Data/CloudDatabaseManager';
const { ccclass, property } = _decorator;

/**
 * 云数据库测试组件
 * 用于测试云数据库的连接和数据上传功能
 */
@ccclass('CloudDatabaseTest')
export class CloudDatabaseTest extends Component {

    @property(Button)
    initButton: Button = null;

    @property(Button)
    uploadButton: Button = null;

    @property(Button)
    queryButton: Button = null;

    @property(Button)
    clearButton: Button = null;

    @property(Label)
    statusLabel: Label = null;

    private cloudDB: CloudDatabaseManager = null;

    onLoad() {
        this.cloudDB = CloudDatabaseManager.instance;
        this.updateStatus("云数据库测试组件已加载");
    }

    start() {
        // 绑定按钮事件
        if (this.initButton) {
            this.initButton.node.on('click', this.onInitButtonClick, this);
        }
        if (this.uploadButton) {
            this.uploadButton.node.on('click', this.onUploadButtonClick, this);
        }
        if (this.queryButton) {
            this.queryButton.node.on('click', this.onQueryButtonClick, this);
        }
        if (this.clearButton) {
            this.clearButton.node.on('click', this.onClearButtonClick, this);
        }
    }

    /**
     * 更新状态显示
     */
    private updateStatus(message: string): void {
        console.log(`CloudDatabaseTest: ${message}`);
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
    }

    /**
     * 初始化云数据库
     */
    private async onInitButtonClick(): Promise<void> {
        this.updateStatus("正在初始化云数据库...");
        
        try {
            const success = await this.cloudDB.initialize();
            if (success) {
                this.updateStatus("云数据库初始化成功！");
            } else {
                this.updateStatus("云数据库初始化失败！");
            }
        } catch (error) {
            this.updateStatus(`初始化失败: ${error.message}`);
        }
    }

    /**
     * 上传模拟玩家数据
     */
    private async onUploadButtonClick(): Promise<void> {
        this.updateStatus("正在上传200个模拟玩家数据...");
        
        try {
            const success = await this.cloudDB.uploadMockPlayersData(200);
            if (success) {
                this.updateStatus("模拟玩家数据上传成功！");
            } else {
                this.updateStatus("模拟玩家数据上传失败！");
            }
        } catch (error) {
            this.updateStatus(`上传失败: ${error.message}`);
        }
    }

    /**
     * 查询玩家数据
     */
    private async onQueryButtonClick(): Promise<void> {
        this.updateStatus("正在查询玩家数据...");
        
        try {
            const players = await this.cloudDB.getPlayersData(10, 0);
            if (players.length > 0) {
                this.updateStatus(`查询成功！获取到${players.length}个玩家数据`);
                console.log("查询到的玩家数据:", players);
            } else {
                this.updateStatus("查询成功，但没有找到玩家数据");
            }
        } catch (error) {
            this.updateStatus(`查询失败: ${error.message}`);
        }
    }

    /**
     * 清空玩家数据
     */
    private async onClearButtonClick(): Promise<void> {
        this.updateStatus("正在清空玩家数据...");
        
        try {
            const success = await this.cloudDB.clearPlayersData();
            if (success) {
                this.updateStatus("玩家数据清空成功！");
            } else {
                this.updateStatus("玩家数据清空失败！");
            }
        } catch (error) {
            this.updateStatus(`清空失败: ${error.message}`);
        }
    }

    /**
     * 一键测试流程
     */
    public async runFullTest(): Promise<void> {
        console.log("=== 开始云数据库完整测试 ===");
        
        // 1. 初始化
        this.updateStatus("步骤1: 初始化云数据库");
        const initSuccess = await this.cloudDB.initialize();
        if (!initSuccess) {
            this.updateStatus("测试失败: 无法初始化云数据库");
            return;
        }

        // 2. 清空现有数据
        this.updateStatus("步骤2: 清空现有数据");
        await this.cloudDB.clearPlayersData();

        // 3. 上传模拟数据
        this.updateStatus("步骤3: 上传200个模拟玩家数据");
        const uploadSuccess = await this.cloudDB.uploadMockPlayersData(200);
        if (!uploadSuccess) {
            this.updateStatus("测试失败: 无法上传模拟数据");
            return;
        }

        // 4. 查询验证
        this.updateStatus("步骤4: 查询验证数据");
        const players = await this.cloudDB.getPlayersData(10, 0);
        if (players.length > 0) {
            this.updateStatus(`测试成功！云数据库中有${players.length}个玩家数据`);
            console.log("测试完成，示例玩家数据:", players.slice(0, 3));
        } else {
            this.updateStatus("测试失败: 查询不到上传的数据");
        }

        console.log("=== 云数据库测试完成 ===");
    }

    onDestroy() {
        // 清理事件监听
        if (this.initButton) {
            this.initButton.node.off('click', this.onInitButtonClick, this);
        }
        if (this.uploadButton) {
            this.uploadButton.node.off('click', this.onUploadButtonClick, this);
        }
        if (this.queryButton) {
            this.queryButton.node.off('click', this.onQueryButtonClick, this);
        }
        if (this.clearButton) {
            this.clearButton.node.off('click', this.onClearButtonClick, this);
        }
    }
}

/**
 * 全局测试函数，可以在控制台调用
 */
declare global {
    interface Window {
        testCloudDatabase: () => Promise<void>;
        uploadMockData: () => Promise<void>;
        queryCloudData: () => Promise<void>;
        clearCloudData: () => Promise<void>;
    }
}

// 导出全局测试函数
if (typeof window !== 'undefined') {
    window.testCloudDatabase = async () => {
        const cloudDB = CloudDatabaseManager.instance;
        
        console.log("=== 开始云数据库测试 ===");
        
        // 初始化
        console.log("1. 初始化云数据库...");
        const initSuccess = await cloudDB.initialize();
        if (!initSuccess) {
            console.error("初始化失败");
            return;
        }
        console.log("✅ 初始化成功");

        // 上传数据
        console.log("2. 上传200个模拟玩家数据...");
        const uploadSuccess = await cloudDB.uploadMockPlayersData(200);
        if (!uploadSuccess) {
            console.error("上传失败");
            return;
        }
        console.log("✅ 上传成功");

        // 查询验证
        console.log("3. 查询验证数据...");
        const players = await cloudDB.getPlayersData(5, 0);
        console.log(`✅ 查询成功，获取到${players.length}个玩家数据:`, players);

        console.log("=== 测试完成 ===");
    };

    window.uploadMockData = async () => {
        const cloudDB = CloudDatabaseManager.instance;
        await cloudDB.initialize();
        const success = await cloudDB.uploadMockPlayersData(200);
        console.log(success ? "✅ 上传成功" : "❌ 上传失败");
    };

    window.queryCloudData = async () => {
        const cloudDB = CloudDatabaseManager.instance;
        await cloudDB.initialize();
        const players = await cloudDB.getPlayersData(10, 0);
        console.log(`查询到${players.length}个玩家:`, players);
    };

    window.clearCloudData = async () => {
        const cloudDB = CloudDatabaseManager.instance;
        await cloudDB.initialize();
        const success = await cloudDB.clearPlayersData();
        console.log(success ? "✅ 清空成功" : "❌ 清空失败");
    };
}
