import { _decorator, Component, Node } from 'cc';
import { CloudDatabaseManager } from './Data/CloudDatabaseManager';
import { WeChatLoginManager } from './WeChatLoginManager';
import { InviteCodeManager } from './InviteCodeManager';

const { ccclass, property } = _decorator;

/**
 * 基于_id的用户识别测试
 * 验证简化后的用户识别机制
 */
@ccclass('UserIdTest')
export class UserIdTest extends Component {

    onLoad() {
        console.log("=== 基于_id的用户识别测试开始 ===");
        
        // 延迟执行，确保系统初始化完成
        this.scheduleOnce(() => {
            this.runUserIdTest();
        }, 3.0);
    }

    private async runUserIdTest() {
        console.log("\n🧪 开始基于_id的用户识别测试");
        
        // 1. 检查本地保存的用户ID
        await this.checkSavedUserId();
        
        // 2. 测试用户数据查询
        await this.testUserDataQuery();
        
        // 3. 测试用户识别的一致性
        await this.testUserConsistency();
        
        // 4. 模拟清除用户ID后的恢复
        await this.testUserIdRecovery();
    }

    private async checkSavedUserId() {
        console.log("\n--- 检查本地保存的用户ID ---");
        
        const savedUserId = localStorage.getItem('cloud_user_id');
        console.log(`本地保存的用户ID: ${savedUserId}`);
        
        if (savedUserId) {
            console.log("✅ 找到本地保存的用户ID");
            
            // 验证这个ID是否有效
            try {
                const cloudDB = CloudDatabaseManager.instance;
                const result = await cloudDB.getCurrentUserData();
                
                if (result) {
                    console.log(`✅ 用户ID有效 - 昵称: ${result.nickname}, 邀请码: ${result.inviteCode}`);
                } else {
                    console.log("❌ 用户ID无效，可能记录已被删除");
                }
            } catch (error) {
                console.error("验证用户ID失败:", error);
            }
        } else {
            console.log("⚠️ 没有本地保存的用户ID，将通过openid查询");
        }
    }

    private async testUserDataQuery() {
        console.log("\n--- 测试用户数据查询 ---");
        
        try {
            const cloudDB = CloudDatabaseManager.instance;
            
            // 第一次查询
            console.log("第一次查询用户数据...");
            const userData1 = await cloudDB.getCurrentUserData();
            
            if (userData1) {
                console.log(`第一次查询结果: ID=${userData1._id}, 昵称=${userData1.nickname}, 邀请码=${userData1.inviteCode}`);
                
                // 第二次查询，应该返回相同的数据
                console.log("第二次查询用户数据...");
                const userData2 = await cloudDB.getCurrentUserData();
                
                if (userData2) {
                    console.log(`第二次查询结果: ID=${userData2._id}, 昵称=${userData2.nickname}, 邀请码=${userData2.inviteCode}`);
                    
                    // 比较两次查询结果
                    if (userData1._id === userData2._id && userData1.inviteCode === userData2.inviteCode) {
                        console.log("✅ 两次查询结果一致，用户识别正常");
                    } else {
                        console.log("❌ 两次查询结果不一致，用户识别有问题");
                    }
                } else {
                    console.log("❌ 第二次查询失败");
                }
            } else {
                console.log("❌ 第一次查询失败，可能是新用户");
            }
            
        } catch (error) {
            console.error("测试用户数据查询失败:", error);
        }
    }

    private async testUserConsistency() {
        console.log("\n--- 测试用户识别的一致性 ---");
        
        // 记录当前状态
        const loginManager = WeChatLoginManager.instance;
        const currentUserInfo = loginManager.getUserInfo();
        const currentInviteCode = InviteCodeManager.getPlayerInviteCode();
        const savedUserId = localStorage.getItem('cloud_user_id');
        
        console.log("当前状态:");
        console.log(`  本地用户信息: ${currentUserInfo?.nickname}`);
        console.log(`  本地邀请码: ${currentInviteCode}`);
        console.log(`  保存的用户ID: ${savedUserId}`);
        
        // 查询云端数据
        const cloudDB = CloudDatabaseManager.instance;
        const cloudUserData = await cloudDB.getCurrentUserData();
        
        if (cloudUserData) {
            console.log("云端数据:");
            console.log(`  云端昵称: ${cloudUserData.nickname}`);
            console.log(`  云端邀请码: ${cloudUserData.inviteCode}`);
            console.log(`  云端记录ID: ${cloudUserData._id}`);
            
            // 检查一致性
            const nicknameMatch = currentUserInfo?.nickname === cloudUserData.nickname;
            const inviteCodeMatch = currentInviteCode === cloudUserData.inviteCode;
            const userIdMatch = savedUserId === cloudUserData._id;
            
            console.log("一致性检查:");
            console.log(`  昵称一致: ${nicknameMatch}`);
            console.log(`  邀请码一致: ${inviteCodeMatch}`);
            console.log(`  用户ID一致: ${userIdMatch}`);
            
            if (nicknameMatch && inviteCodeMatch && userIdMatch) {
                console.log("✅ 所有数据一致，用户识别机制正常");
            } else {
                console.log("⚠️ 数据不一致，可能需要同步");
            }
        } else {
            console.log("❌ 无法获取云端数据");
        }
    }

    private async testUserIdRecovery() {
        console.log("\n--- 测试用户ID恢复机制 ---");
        
        // 备份当前用户ID
        const originalUserId = localStorage.getItem('cloud_user_id');
        console.log(`备份原用户ID: ${originalUserId}`);
        
        // 清除用户ID，模拟新设备登录
        localStorage.removeItem('cloud_user_id');
        console.log("已清除本地用户ID，模拟新设备登录");
        
        try {
            // 尝试查询用户数据（应该通过openid恢复）
            const cloudDB = CloudDatabaseManager.instance;
            const recoveredUserData = await cloudDB.getCurrentUserData();
            
            if (recoveredUserData) {
                const recoveredUserId = localStorage.getItem('cloud_user_id');
                console.log(`恢复的用户ID: ${recoveredUserId}`);
                console.log(`恢复的用户数据: 昵称=${recoveredUserData.nickname}, 邀请码=${recoveredUserData.inviteCode}`);
                
                if (recoveredUserId === originalUserId) {
                    console.log("✅ 用户ID恢复成功，识别机制正常");
                } else {
                    console.log("⚠️ 恢复的用户ID与原ID不同，可能存在多条记录");
                    console.log(`  原ID: ${originalUserId}`);
                    console.log(`  新ID: ${recoveredUserId}`);
                }
            } else {
                console.log("❌ 无法恢复用户数据");
                
                // 恢复原用户ID
                if (originalUserId) {
                    localStorage.setItem('cloud_user_id', originalUserId);
                    console.log("已恢复原用户ID");
                }
            }
            
        } catch (error) {
            console.error("测试用户ID恢复失败:", error);
            
            // 恢复原用户ID
            if (originalUserId) {
                localStorage.setItem('cloud_user_id', originalUserId);
                console.log("已恢复原用户ID");
            }
        }
    }

    /**
     * 显示当前用户识别状态
     */
    public async showCurrentStatus() {
        console.log("\n📋 当前用户识别状态:");
        
        const savedUserId = localStorage.getItem('cloud_user_id');
        const loginManager = WeChatLoginManager.instance;
        const openid = loginManager.getOpenid();
        const userInfo = loginManager.getUserInfo();
        const inviteCode = InviteCodeManager.getPlayerInviteCode();
        
        console.log(`保存的用户ID: ${savedUserId}`);
        console.log(`OpenID: ${openid}`);
        console.log(`本地用户信息: ${userInfo?.nickname}`);
        console.log(`本地邀请码: ${inviteCode}`);
        
        // 查询云端数据
        const cloudDB = CloudDatabaseManager.instance;
        const cloudUserData = await cloudDB.getCurrentUserData();
        if (cloudUserData) {
            console.log(`云端数据: ID=${cloudUserData._id}, 昵称=${cloudUserData.nickname}, 邀请码=${cloudUserData.inviteCode}`);
        } else {
            console.log("云端数据: 未找到");
        }
    }

    /**
     * 清除用户ID，强制重新识别
     */
    public clearUserId() {
        localStorage.removeItem('cloud_user_id');
        console.log("✅ 已清除用户ID，下次查询时会重新识别");
    }

    /**
     * 手动测试
     */
    public manualTest() {
        this.runUserIdTest();
    }
}
